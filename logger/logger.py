"""
Custom logger setup for the longevity platform.
"""
import logging
import sys

def setup_logging(name, level=logging.INFO):
    """
    Set up a logger with the given name and level.
    
    Args:
        name: Logger name
        level: Logging level (default: INFO)
        
    Returns:
        logging.Logger: Configured logger
    """
    # Create logger
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # Create formatter
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    
    # Create console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    
    # Add handlers to logger if they don't exist already
    if not logger.handlers:
        logger.addHandler(console_handler)
    
    return logger
