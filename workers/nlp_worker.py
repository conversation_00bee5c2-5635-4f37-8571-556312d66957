"""
Celery worker for NLP tasks.
"""
import logging
import numpy as np
from typing import Dict, Any, List, Optional, Union
from celery import Task

from workers.celery_app import celery_app

# Import NLPService only when needed
nlp_service = None

def get_nlp_service():
    """Lazy import of NLPService to avoid loading dependencies at startup."""
    global nlp_service
    if nlp_service is None:
        from services.nlp_service import NLPService
        nlp_service = NLPService
    return nlp_service


def initialize_nlp_models():
    """Initialize NLP models to ensure they're ready for use."""
    global models_initialized

    try:
        logger.info("Pre-initializing NLP models...")
        service = get_nlp_service()

        # Pre-load embedding model
        logger.info("Loading embedding model...")
        embedding_model = service.get_embedding_model()
        logger.info(f"Embedding model loaded successfully")

        # E5-Large-V2 model is already loaded as the embedding model
        logger.info("E5-Large-V2 model is already loaded as the embedding model")

        # Pre-load BioBERT NER pipeline
        logger.info("Loading BioBERT NER pipeline...")
        biobert_ner = service.get_biobert_ner()
        logger.info(f"BioBERT NER pipeline loaded successfully")

        # Pre-load SapBERT model
        logger.info("Loading SapBERT model...")
        sapbert_model = service.get_sapbert_model()
        logger.info(f"SapBERT model loaded successfully")

        logger.info("All NLP models initialized successfully")
        models_initialized = True
        return True
    except Exception as e:
        logger.error(f"Error initializing NLP models: {str(e)}")
        models_initialized = False
        return False


# We'll initialize models on demand via a task instead of at startup
# initialize_nlp_models()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Track model initialization status
models_initialized = False


class NLPTask(Task):
    """Base task for NLP processing with error handling."""

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """
        Handle task failure.

        Args:
            exc: Exception
            task_id: Task ID
            args: Task arguments
            kwargs: Task keyword arguments
            einfo: Error info
        """
        logger.error(f"NLP Task {task_id} failed: {exc}")


@celery_app.task(name="workers.nlp.initialize_models", bind=True, base=NLPTask)
def initialize_models_task(self) -> Dict[str, Any]:
    """
    Initialize NLP models and return status.

    Returns:
        Dict[str, Any]: Initialization status
    """
    logger.info("Initializing NLP models via task")
    success = initialize_nlp_models()

    return {
        "status": "success" if success else "error",
        "models_initialized": models_initialized,
        "message": "NLP models initialized successfully" if success else "Failed to initialize NLP models"
    }


@celery_app.task(name="workers.nlp.check_models_status", bind=True, base=NLPTask)
def check_models_status(self) -> Dict[str, Any]:
    """
    Check if NLP models are initialized.

    Returns:
        Dict[str, Any]: Status of NLP models
    """
    logger.info("Checking NLP models status")

    return {
        "status": "success",
        "models_initialized": models_initialized,
        "message": "NLP models are initialized" if models_initialized else "NLP models are not initialized"
    }


@celery_app.task(name="workers.nlp.generate_embeddings", bind=True, base=NLPTask)
def generate_embeddings(self, texts: List[str]) -> Dict[str, Any]:
    """
    Generate embeddings for a list of texts using E5-Large-V2 model.

    This task uses E5-Large-V2 with the 'passage:' prefix for document passages,
    which is optimal for semantic search.

    Args:
        texts: List of texts to generate embeddings for

    Returns:
        Dict[str, Any]: Embeddings result
    """
    logger.info(f"Generating embeddings for {len(texts)} texts using E5-Large-V2")

    try:
        # Get NLPService
        NLPService = get_nlp_service()

        # Generate embeddings using E5-Large-V2
        embeddings = NLPService.generate_embeddings(texts)

        # Convert numpy arrays to Python lists for JSON serialization
        embeddings_list = []
        for embedding in embeddings:
            # Ensure each value is a Python float, not a numpy float type
            embedding_list = [float(val) for val in embedding]
            embeddings_list.append(embedding_list)

        return {
            "status": "success",
            "embeddings": embeddings_list
        }
    except Exception as e:
        logger.error(f"Error generating embeddings: {e}")
        return {
            "status": "error",
            "error": str(e)
        }


@celery_app.task(name="workers.nlp.semantic_search", bind=True, base=NLPTask)
def semantic_search(self, query: str, passage_embeddings: List[List[float]], passages: List[Dict[str, Any]], top_k: int = 5) -> Dict[str, Any]:
    """
    Perform semantic search using E5-Large-V2 embeddings.

    Args:
        query: Search query
        passage_embeddings: Precomputed passage embeddings from E5-Large-V2
        passages: List of passage dictionaries
        top_k: Number of results to return

    Returns:
        Dict[str, Any]: Search results
    """
    logger.info(f"Performing semantic search with query: '{query[:50]}...' on {len(passages)} passages")

    try:
        # Get NLPService
        NLPService = get_nlp_service()

        # Convert passage embeddings to numpy array
        passage_embeddings_np = np.array(passage_embeddings)

        # Perform semantic search
        results = NLPService.semantic_search(query, passage_embeddings_np, passages, top_k)

        # Ensure all values are JSON serializable
        serializable_results = []
        for result in results:
            serializable_result = {}
            for key, value in result.items():
                # Convert numpy types to Python types
                if hasattr(value, 'dtype') and hasattr(value, 'tolist'):
                    serializable_result[key] = float(value)
                else:
                    serializable_result[key] = value
            serializable_results.append(serializable_result)

        return {
            "status": "success",
            "results": serializable_results
        }
    except Exception as e:
        logger.error(f"Error performing semantic search: {e}")
        return {
            "status": "error",
            "error": str(e)
        }


@celery_app.task(name="workers.nlp.generate_biomedical_embeddings", bind=True, base=NLPTask)
def generate_biomedical_embeddings(self, texts: List[str]) -> Dict[str, Any]:
    """
    Generate biomedical embeddings for a list of texts using BioBERT.

    Args:
        texts: List of texts to generate biomedical embeddings for

    Returns:
        Dict[str, Any]: Biomedical embeddings result
    """
    logger.info(f"Generating biomedical embeddings for {len(texts)} texts")

    try:
        # Get NLPService
        NLPService = get_nlp_service()

        # Generate biomedical embeddings
        embeddings = NLPService.get_embeddings(texts)

        # Convert numpy arrays to Python lists for JSON serialization
        embeddings_list = []
        for embedding in embeddings:
            # Ensure each value is a Python float, not a numpy float type
            embedding_list = [float(val) for val in embedding]
            embeddings_list.append(embedding_list)

        return {
            "status": "success",
            "embeddings": embeddings_list
        }
    except Exception as e:
        logger.error(f"Error generating biomedical embeddings: {e}")
        return {
            "status": "error",
            "error": str(e)
        }


@celery_app.task(name="workers.nlp.generate_semantic_search_embeddings", bind=True, base=NLPTask)
def generate_semantic_search_embeddings(self, texts: List[str], is_query: bool = False) -> Dict[str, Any]:
    """
    Generate embeddings for semantic search using E5-Large-V2 model.

    Args:
        texts: List of texts to generate embeddings for
        is_query: Whether the texts are search queries (True) or passages (False)

    Returns:
        Dict[str, Any]: Semantic search embeddings result
    """
    logger.info(f"Generating semantic search embeddings for {len(texts)} texts (is_query={is_query})")

    try:
        # Get NLPService
        NLPService = get_nlp_service()

        # Generate semantic search embeddings
        embeddings = NLPService.generate_semantic_search_embeddings(texts, is_query=is_query)

        # Convert numpy arrays to Python lists for JSON serialization
        embeddings_list = []
        for embedding in embeddings:
            # Ensure each value is a Python float, not a numpy float type
            embedding_list = [float(val) for val in embedding]
            embeddings_list.append(embedding_list)

        return {
            "status": "success",
            "embeddings": embeddings_list
        }
    except Exception as e:
        logger.error(f"Error generating semantic search embeddings: {e}")
        return {
            "status": "error",
            "error": str(e)
        }


@celery_app.task(name="workers.nlp.extract_entities_from_text", bind=True, base=NLPTask)
def extract_entities_from_text(self, text: str) -> Dict[str, Any]:
    """
    Extract biomedical entities from text.

    Args:
        text: Text to extract entities from

    Returns:
        Dict[str, Any]: Extracted entities result
    """
    logger.info(f"Extracting entities from text: {text[:50]}...")

    try:
        # Get NLPService
        NLPService = get_nlp_service()

        # Extract entities
        entities = NLPService.extract_biomedical_entities(text)

        # Convert numpy types to Python types for JSON serialization
        serializable_entities = []
        for entity in entities:
            serializable_entity = {}
            for key, value in entity.items():
                # Convert numpy types to Python types
                if hasattr(value, 'dtype') and hasattr(value, 'tolist'):
                    serializable_entity[key] = float(value)
                else:
                    serializable_entity[key] = value
            serializable_entities.append(serializable_entity)

        return {
            "status": "success",
            "entities": serializable_entities
        }
    except Exception as e:
        logger.error(f"Error extracting entities from text: {e}")
        return {
            "status": "error",
            "error": str(e)
        }
