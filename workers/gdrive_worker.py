"""
Celery worker for Google Drive integration.
"""
import logging
from typing import Dict, Any, Optional
from celery import Task

from workers.celery_app import celery_app

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class GoogleDriveTask(Task):
    """Base task for Google Drive operations with error handling."""

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """
        Handle task failure.

        Args:
            exc: Exception
            task_id: Task ID
            args: Task arguments
            kwargs: Task keyword arguments
            einfo: Error info
        """
        logger.error(f"Task {task_id} failed: {exc}")


# Use shared_task to avoid circular imports at module level
@celery_app.task(name="workers.gdrive.check_for_new_documents", bind=True, base=GoogleDriveTask)
def check_for_new_documents(self, folder_id: Optional[str] = None) -> Dict[str, Any]:
    """
    Check Google Drive for new documents and ingest them.

    Args:
        folder_id: Optional Google Drive folder ID. If None, uses the default folder.

    Returns:
        Dict[str, Any]: Processing results
    """
    # Import here to avoid circular imports and initialization issues
    from services.gdrive_service import GoogleDriveService
    
    logger.info("Checking for new documents in Google Drive")

    try:
        # Use the GoogleDriveService to check for and ingest new documents
        with GoogleDriveService() as gdrive_service:
            result = gdrive_service.check_and_ingest_new_documents(folder_id)

        logger.info(f"Google Drive document check completed: {result}")
        return result
    except Exception as e:
        logger.error(f"Error checking for new documents in Google Drive: {e}")
        raise