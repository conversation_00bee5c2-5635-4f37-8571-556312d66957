"""Workers package."""

import os
import sys

# Only import worker modules when running as a worker
# This prevents API from loading unnecessary dependencies
if 'celery' in sys.argv[0] or os.environ.get('WORKER_ENVIRONMENT') == 'true':
    # Import tasks to ensure they are registered
    from workers import document_worker
    from workers import knowledge_graph_worker
    from workers import pubmed_worker
    from workers import gdrive_worker  # Import Google Drive worker to register its tasks

