"""
Celery worker for entity extraction and knowledge graph building.
"""
import logging
import uuid
from typing import Dict, Any, Optional, Union
from celery import Task

from workers.celery_app import celery_app
from transport.data_transport import DataTransport

# Import EntityService only when needed
entity_service = None

def get_entity_service():
    """Lazy import of EntityService to avoid loading dependencies at startup."""
    global entity_service
    if entity_service is None:
        from services.entity_service import EntityService
        entity_service = EntityService
    return entity_service

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EntityTask(Task):
    """Base task for entity processing with error handling."""

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """
        Handle task failure.

        Args:
            exc: Exception
            task_id: Task ID
            args: Task arguments
            kwargs: Task keyword arguments
            einfo: Error info
        """
        document_id = args[0] if args else kwargs.get('document_id')
        if not document_id:
            logger.error(f"Task {task_id} failed without document_id: {exc}")
            return

        # Update task status in database
        try:
            with DataTransport() as transport:
                transport.update_task_status(
                    task_id=kwargs.get('processing_task_id'),
                    status="failed",
                    error=str(exc)
                )
        except Exception as e:
            logger.error(f"Failed to update task status: {e}")


@celery_app.task(name="workers.entity.extract_entities", bind=True, base=EntityTask)
def extract_entities(self, document_id: Union[str, uuid.UUID], processing_task_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
    """
    Extract entities from a document.

    Args:
        document_id: Document ID
        processing_task_id: Processing task ID

    Returns:
        Dict[str, Any]: Processing result
    """
    logger.info(f"Extracting entities from document: {document_id}")

    # Get EntityService
    EntityService = get_entity_service()

    # Delegate to entity service
    return EntityService.extract_entities_from_document(document_id, processing_task_id)