"""
Celery worker for document processing and search.
"""
import logging
import uuid
from typing import Dict, Any, Optional, Union, List
from celery import Task

from workers.celery_app import celery_app
from transport.data_transport import DataTransport

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DocumentTask(Task):
    """Base task for document processing with error handling."""

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """
        Handle task failure.

        Args:
            exc: Exception
            task_id: Task ID
            args: Task arguments
            kwargs: Task keyword arguments
            einfo: Error info
        """
        document_id = args[0] if args else kwargs.get('document_id')
        if not document_id:
            logger.error(f"Task {task_id} failed without document_id: {exc}")
            return

        # Update task status in database
        try:
            with DataTransport() as transport:
                transport.update_task_status(
                    task_id=kwargs.get('processing_task_id'),
                    status="failed",
                    error=str(exc)
                )
        except Exception as e:
            logger.error(f"Failed to update task status: {e}")


@celery_app.task(name="workers.document.ingest_document", bind=True, base=DocumentTask)
def ingest_document(self, source_type: str, source_path: str, metadata: Optional[Dict[str, Any]] = None, callback_task: Optional[str] = None, file_content: Optional[bytes] = None, filename: Optional[str] = None, content_type: Optional[str] = None, title: Optional[str] = None, build_knowledge_graph: bool = True, use_batch_api: bool = False) -> Dict[str, Any]:
    """
    Ingest a document from a source.

    Args:
        source_type: Source type (url, file, etc.)
        source_path: Source path
        metadata: Optional metadata
        callback_task: Optional callback task to execute after ingestion
        file_content: Optional file content for file uploads
        filename: Optional filename for file uploads
        content_type: Optional content type for file uploads
        title: Optional document title (if available)
        build_knowledge_graph: Whether to trigger knowledge graph building after processing
        use_batch_api: Whether to use batch API for processing

    Returns:
        Dict[str, Any]: Processing result
    """
    logger.info(f"Ingesting document from {source_type}: {source_path}")

    try:
        # Import DocumentService here to avoid circular imports
        from services.document_service import DocumentService
        import io

        # Create file object if file content is provided
        file_obj = None
        if file_content and filename:
            file_obj = io.BytesIO(file_content)

        # Use the DocumentService to ingest the document
        result = DocumentService.ingest_document(
            source_type=source_type,
            source_path=source_path,
            metadata=metadata,
            task_id=self.request.id,
            file_obj=file_obj,
            filename=filename,
            content_type=content_type,
            title=title
        )

        # Check if the document is a duplicate
        if result.get('is_duplicate', False):
            logger.info(f"Document is a duplicate, returning existing document information")
            return result

        # If a callback task is specified, execute it with the document ID and task ID
        if callback_task and result.get("document_id") and result.get("task_id"):
            logger.info(f"Executing callback task {callback_task} for document {result['document_id']}, build_knowledge_graph: {build_knowledge_graph}")
            celery_app.send_task(
                callback_task,
                args=[result["document_id"], result["task_id"], use_batch_api, build_knowledge_graph]
            )

        return result
    except Exception as e:
        logger.error(f"Error ingesting document: {e}")
        raise


@celery_app.task(name="workers.document.process_document",
                 bind=True, base=DocumentTask,
                 autoretry_for=(Exception,),
                retry_kwargs={'max_retries': 3, 'countdown': 60})
def process_document(self, document_id: Union[str, uuid.UUID], processing_task_id: Union[str, uuid.UUID], use_batch_api: bool = False, build_knowledge_graph: bool = True) -> Dict[str, Any]:
    """
    Process a document.

    Args:
        document_id: Document ID
        processing_task_id: Processing task ID
        use_batch_api: Whether to use Vertex AI batch API for processing
        build_knowledge_graph: Whether to trigger knowledge graph building after processing

    Returns:
        Dict[str, Any]: Processing result
    """
    logger.info(f"Processing document: {document_id}, use_batch_api: {use_batch_api}, build_knowledge_graph: {build_knowledge_graph}")

    try:
        # Import DocumentService here to avoid circular imports
        from services.document_service import DocumentService

        # Use the DocumentService to process the document
        result = DocumentService.process_document(
            document_id=document_id,
            processing_task_id=processing_task_id,
            use_batch_api=use_batch_api,
            build_knowledge_graph=build_knowledge_graph
        )

        if not build_knowledge_graph:
            return result

        celery_app.send_task(
            "workers.knowledge_graph.build_knowledge_graph",
            args=[document_id, processing_task_id]
        )
        return result

    except Exception as e:
        logger.error(f"Error processing document: {e}")
        raise


@celery_app.task(name="workers.document.find_similar_documents", bind=True)
def find_similar_documents(self, query_text: str, limit: int = 10,
                          min_similarity: float = 0.0, filter_document_id: Optional[Union[str, uuid.UUID]] = None) -> Dict[str, Any]:
    """
    Find similar documents using semantic search.

    Args:
        query_text: Query text
        limit: Maximum number of results
        min_similarity: Minimum similarity threshold (0.0 to 1.0)
        filter_document_id: Optional document ID to filter results by

    Returns:
        Dict[str, Any]: Search results with similar document chunks
    """
    logger.info(f"Finding similar documents with query: {query_text}, limit: {limit}, min_similarity: {min_similarity}")

    try:
        # Import DocumentService here to avoid circular imports
        from services.document_service import DocumentService

        # Use the DocumentService to find similar documents
        result = DocumentService.find_similar_documents(
            query_text=query_text,
            limit=limit,
            min_similarity=min_similarity,
            filter_document_id=filter_document_id
        )

        return result
    except Exception as e:
        logger.error(f"Error finding similar documents: {e}")
        raise


@celery_app.task(name="workers.document.find_similar_by_chunk_id", bind=True)
def find_similar_by_chunk_id(self, chunk_id: Union[str, uuid.UUID], limit: int = 10,
                            min_similarity: float = 0.0, filter_document_id: Optional[Union[str, uuid.UUID]] = None) -> Dict[str, Any]:
    """
    Find chunks similar to a specific chunk using pgvector similarity search.

    Args:
        chunk_id: ID of the chunk to find similar chunks for
        limit: Maximum number of results
        min_similarity: Minimum similarity threshold (0.0 to 1.0)
        filter_document_id: Optional document ID to filter results by

    Returns:
        Dict[str, Any]: Search results with similar document chunks
    """
    logger.info(f"Finding similar chunks for chunk ID: {chunk_id}, limit: {limit}, min_similarity: {min_similarity}")

    try:
        # Import DocumentService here to avoid circular imports
        from services.document_service import DocumentService

        # Use the DocumentService to find similar chunks
        result = DocumentService.find_similar_by_chunk_id(
            chunk_id=chunk_id,
            limit=limit,
            min_similarity=min_similarity,
            filter_document_id=filter_document_id
        )

        return result
    except Exception as e:
        logger.error(f"Error searching documents: {e}")
        raise