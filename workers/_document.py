"""
Celery worker tasks for document processing.
"""
import logging
import time
import os
import tempfile
import uuid
from typing import List, Dict, Any, Optional, Union
from celery import shared_task

from transport.data_transport import DataTransport
from transport.vertex_ai_client import VertexAIClient
from transport.vertex_ai_batch_client import VertexAIBatchClient
from common.config import settings
from services.document_service import DocumentService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


@shared_task(bind=True, name="workers.document.process_document")
def process_document(self, document_id: str, task_id: str, use_batch_api: bool = False, build_knowledge_graph: bool = True):
    """
    Process a document to extract entities and relationships.

    Args:
        document_id: Document ID
        task_id: Task ID for tracking
        use_batch_api: Whether to use batch API for processing
        build_knowledge_graph: Whether to trigger knowledge graph building after processing
    """
    logger.info(f"Processing document {document_id} with task {task_id}")
    logger.info(f"Batch API: {use_batch_api}, Build Knowledge Graph: {build_knowledge_graph}")

    try:
        # Use DocumentService to process the document
        result = DocumentService.process_document(
            document_id=document_id,
            processing_task_id=task_id,
            use_batch_api=use_batch_api,
            build_knowledge_graph=build_knowledge_graph
        )

        logger.info(f"Document {document_id} processed successfully with {result.get('chunks_count', 0)} chunks")
        return result

    except Exception as e:
        logger.error(f"Error processing document {document_id}: {e}")

        # Re-raise the exception for Celery to handle
        raise


@shared_task(bind=True, name="workers.document.batch_process_documents")
def batch_process_documents(self, document_ids: List[str], task_id: str, use_batch_api: bool = True):
    """
    Process multiple documents in batch mode.

    Args:
        document_ids: List of document IDs
        task_id: Task ID for tracking
        use_batch_api: Whether to use batch API for processing (default: True)
    """
    logger.info(f"Batch processing {len(document_ids)} documents with task {task_id}")
    logger.info(f"Batch API: {use_batch_api}")

    start_time = time.time()

    try:
        # Initialize clients
        with DataTransport() as transport:
            vertex_client = VertexAIClient()
            vertex_batch_client = VertexAIBatchClient()

            # Update task status
            transport.update_task_status(task_id, "processing")

            # Update document statuses
            for doc_id in document_ids:
                transport.update_document_status(doc_id, "processing")

            # Get all chunks from all documents
            all_chunks = []
            doc_chunk_map = {}  # Map to track which chunks belong to which document

            for doc_id in document_ids:
                chunks = transport.get_document_chunks(doc_id)

                for chunk in chunks:
                    chunk_data = {
                        "id": str(chunk.id),
                        "text": chunk.text,
                        "document_id": doc_id  # Track which document this chunk belongs to
                    }
                    all_chunks.append(chunk_data)
                    doc_chunk_map[str(chunk.id)] = doc_id

            if not all_chunks:
                logger.warning(f"No chunks found for any documents in batch")
                transport.update_task_status(
                    task_id,
                    "completed",
                    metadata={"message": "No chunks found for any documents"}
                )
                for doc_id in document_ids:
                    transport.update_document_status(doc_id, "processed")
                return

            logger.info(f"Processing {len(all_chunks)} chunks from {len(document_ids)} documents")

            # Process all chunks in a single batch
            results = vertex_batch_client.batch_process_chunks(all_chunks)

            # Store results
            for result in results:
                chunk_id = result.get("chunk_id")
                entities = result.get("entities", [])
                relationships = result.get("relationships", [])

                # Store entities
                for entity in entities:
                    entity_data = {
                        "chunk_id": chunk_id,
                        "text": entity.get("text", ""),
                        "entity_type": entity.get("type", ""),
                        "start_pos": entity.get("start", 0),
                        "end_pos": entity.get("end", 0),
                        "entity_metadata": {}  # Additional metadata if available
                    }
                    transport.create_entity(entity_data)

                # Store relationships
                for relationship in relationships:
                    # Get source and target entities
                    source_idx = relationship.get("source")
                    target_idx = relationship.get("target")

                    if source_idx is not None and target_idx is not None and 0 <= source_idx < len(entities) and 0 <= target_idx < len(entities):
                        source_entity = entities[source_idx]
                        target_entity = entities[target_idx]

                        relationship_data = {
                            "chunk_id": chunk_id,
                            "source_text": source_entity.get("text", ""),
                            "source_type": source_entity.get("type", ""),
                            "target_text": target_entity.get("text", ""),
                            "target_type": target_entity.get("type", ""),
                            "relationship_type": relationship.get("relationship_type", ""),
                            "evidence_text": relationship.get("evidence_text", ""),
                            "relationship_metadata": {}  # Additional metadata if available
                        }
                        transport.create_relationship(relationship_data)

            # Update document statuses
            for doc_id in document_ids:
                transport.update_document_status(doc_id, "processed")

            # Update task status
            duration = time.time() - start_time
            transport.update_task_status(
                task_id,
                "completed",
                metadata={
                    "duration_seconds": duration,
                    "document_count": len(document_ids),
                    "chunk_count": len(all_chunks),
                    "entity_count": sum(len(result.get("entities", [])) for result in results),
                    "relationship_count": sum(len(result.get("relationships", [])) for result in results)
                }
            )

            logger.info(f"Batch processing of {len(document_ids)} documents completed in {duration:.2f} seconds")

    except Exception as e:
        logger.error(f"Error in batch processing: {e}")

        # Update task status
        try:
            with DataTransport() as transport:
                transport.update_task_status(
                    task_id,
                    "failed",
                    metadata={"error": str(e)}
                )
                for doc_id in document_ids:
                    transport.update_document_status(doc_id, "error")
        except Exception as update_error:
            logger.error(f"Error updating task status: {update_error}")

        # Re-raise the exception for Celery to handle
        raise


@shared_task(bind=True, name="workers.document.ingest_document")
def ingest_document(self, source_type: str, source_path: str, metadata: Optional[Dict[str, Any]] = None,
                  file_content: Optional[bytes] = None, filename: Optional[str] = None,
                  content_type: Optional[str] = None, callback_task: Optional[str] = None):
    """
    Ingest a document from a URL or file content.

    Args:
        source_type: Type of source (url, file)
        source_path: Path or URL
        metadata: Optional metadata
        file_content: Optional file content for file uploads
        filename: Optional filename for file uploads
        content_type: Optional content type for file uploads
        callback_task: Optional callback task to execute after ingestion

    Returns:
        Dict with document ID and status
    """
    logger.info(f"Ingesting document from {source_type}: {source_path}")

    try:
        # Use DocumentService to ingest the document
        if source_type == "file" and file_content:
            # Create a temporary file
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name

            try:
                # Open the file for reading
                with open(temp_file_path, "rb") as file_obj:
                    result = DocumentService.ingest_document(
                        source_type=source_type,
                        source_path=source_path,
                        metadata=metadata,
                        task_id=self.request.id,
                        file_obj=file_obj,
                        filename=filename or os.path.basename(source_path),
                        content_type=content_type
                    )
            finally:
                # Clean up temporary file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
        else:
            # URL ingestion
            result = DocumentService.ingest_document(
                source_type=source_type,
                source_path=source_path,
                metadata=metadata,
                task_id=self.request.id
            )

        # Get document ID and processing task ID
        document_id = result.get("document_id")
        processing_task_id = result.get("processing_task_id")

        # Connect to Neo4j
        with DataTransport() as transport:
            # Log connection to Neo4j
            logger.info(f"Connected to Neo4j at {transport.neo4j_client.uri}")
            # Close Neo4j connection
            logger.info("Neo4j connection closed")

        # Execute callback task if provided
        if callback_task and document_id:
            logger.info(f"Executing callback task {callback_task} for document {document_id}")
            from workers.celery_app import celery_app
            celery_app.send_task(
                callback_task,
                args=[document_id, processing_task_id]
            )

        return result

    except Exception as e:
        logger.error(f"Error ingesting document from {source_path}: {str(e)}")
        raise
