"""
Celery worker for entity orchestration.
"""
import logging
import uuid
from typing import Dict, Any, Union
from celery import Task

from workers.celery_app import celery_app
from transport.data_transport import DataTransport

# Import EntityOrchestrator only when needed
entity_orchestrator = None

def get_entity_orchestrator():
    """Lazy import of EntityOrchestrator to avoid loading dependencies at startup."""
    global entity_orchestrator
    if entity_orchestrator is None:
        from services.entity_orchestrator import EntityOrchestrator
        entity_orchestrator = EntityOrchestrator
    return entity_orchestrator

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EntityOrchestratorTask(Task):
    """Base task for entity orchestration with error handling."""

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """
        Handle task failure.

        Args:
            exc: Exception
            task_id: Task ID
            args: Task arguments
            kwargs: Task keyword arguments
            einfo: Error info
        """
        document_id = args[0] if args else kwargs.get('document_id')
        if not document_id:
            logger.error(f"Task {task_id} failed without document_id: {exc}")
            return

        # Update task status in database
        try:
            with DataTransport() as transport:
                transport.update_task_status(
                    task_id=kwargs.get('processing_task_id'),
                    status="failed",
                    error=str(exc)
                )
        except Exception as e:
            logger.error(f"Failed to update task status: {e}")


@celery_app.task(name="workers.entity_orchestrator.process_document", bind=True, base=EntityOrchestratorTask)
def process_document(self, document_id: Union[str, uuid.UUID], processing_task_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
    """
    Process a document to extract entities, normalize them, and build relationships.

    Args:
        document_id: Document ID
        processing_task_id: Processing task ID

    Returns:
        Dict[str, Any]: Processing result
    """
    logger.info(f"Processing document with entity orchestrator: {document_id}")

    # Get EntityOrchestrator
    EntityOrchestrator = get_entity_orchestrator()

    # Delegate to entity orchestrator
    return EntityOrchestrator.process_document(document_id, processing_task_id)


@celery_app.task(name="workers.entity_orchestrator.find_similar_entities", bind=True)
def find_similar_entities(self, query_text: str, entity_type: str = None, limit: int = 10) -> Dict[str, Any]:
    """
    Find entities similar to a query text.

    Args:
        query_text: Query text
        entity_type: Optional entity type to filter by
        limit: Maximum number of results

    Returns:
        Dict[str, Any]: Search results
    """
    logger.info(f"Finding similar entities for query: {query_text}")

    # Get EntityOrchestrator
    EntityOrchestrator = get_entity_orchestrator()

    # Find similar entities
    results = EntityOrchestrator.find_similar_entities(query_text, entity_type, limit)

    return {
        "query": query_text,
        "entity_type": entity_type,
        "results": results,
        "count": len(results)
    }
