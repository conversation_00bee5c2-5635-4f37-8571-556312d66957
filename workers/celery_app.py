"""
Celery application configuration.
"""
import os
from celery import Celery

from common.config import settings

# Create Celery instance
celery_app = Celery(
    'longevity_platform',
    broker=settings.REDIS_URL,
    backend=settings.REDIS_URL
)

# Configure Celery
celery_app.conf.update(
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='UTC',
    enable_utc=True,
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    worker_max_tasks_per_child=100,
)

# Configure periodic tasks (Celery Beat)
celery_app.conf.beat_schedule = {
    'check-vertex-ai-batch-jobs': {
        'task': 'workers.knowledge_graph.check_and_continue_batch_jobs',
        'schedule': 60.0,  # Run every 60 seconds
    },
    'check-google-drive-documents': {
        'task': 'workers.gdrive.check_for_new_documents',
        'schedule': 300.0,  # Run every 5 minutes (300 seconds)
    },
}

# Include tasks from workers
celery_app.autodiscover_tasks(['workers'])

# Just configure routes without importing modules
celery_app.conf.task_routes = {
    'workers.document.*': {'queue': 'document'},
    'workers.knowledge_graph.*': {'queue': 'knowledge_graph'},
    'workers.pubmed.*': {'queue': 'document_processing_queue'},
    'workers.nlp.*': {'queue': 'nlp_queue'},
    'workers.gdrive.*': {'queue': 'gdrive'},  # Route Google Drive tasks to document queue
}