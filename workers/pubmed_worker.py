# workers/pubmed_worker.py
import logging
import os
import tempfile
import uuid
from typing import Dict, Any

from workers.celery_app import celery_app
from services.pubmed_service import PubMedService
from services.document_partitioner import DocumentPartitioner
from services.chunking_strategy import ChunkingStrategy, ChunkingPurpose
from services.nlp_service import NLPService
from transport.data_transport import DataTransport

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Initialize services when needed
def get_pubmed_service():
    """Get PubMed service instance."""
    return PubMedService()

@celery_app.task(name="workers.pubmed.ingest_pubmed_article", bind=True, max_retries=3, default_retry_delay=60)
def ingest_pubmed_article(self, pmid: str):
    """
    Celery task to fetch PubMed article details and store initial document record and content.
    """
    logger.info(f"Starting ingestion for PMID: {pmid} (Task ID: {self.request.id})")
    document_id = None
    processing_task_id = None

    try:
        # 1. Fetch article details
        pubmed_service = get_pubmed_service()
        article_details = pubmed_service.get_article_details(pmid)
        if not article_details or not article_details.get("abstract"):
            logger.warning(f"No details or abstract found for PMID: {pmid}. Skipping.")
            return {"status": "skipped", "reason": "No details or abstract found"}

        abstract = article_details["abstract"]
        title = article_details.get("title", f"PubMed Article {pmid}")
        # Assuming full_text might be available, adjust as needed
        full_text = article_details.get("full_text", None)
        metadata = {
            "pmid": pmid,
            "title": title,
            "authors": article_details.get("authors"),
            "publication_date": article_details.get("publication_date"),
            "journal": article_details.get("journal"),
            # Add other relevant metadata
        }

        with DataTransport() as transport:
            # 2. Store initial document record and content
            # Check for duplicates first
            existing_doc = transport.db_client.get_document_by_source('pubmed', pmid)
            if existing_doc:
                 logger.info(f"Document for PMID {pmid} already exists (ID: {existing_doc.id}). Checking status.")
                 # For now, we skip if it exists
                 return {"status": "already_exists", "document_id": str(existing_doc.id)}

            # Store abstract (and full text if available) to storage
            # Use a temporary file to leverage existing store_document logic
            # Determine filename and content_type
            filename = f"{pmid}_abstract.txt"
            content_to_store = abstract
            content_type = "text/plain"

            if full_text:
                content_to_store = full_text
                filename = f"{pmid}_full.txt" # Or determine based on actual format

            with tempfile.NamedTemporaryFile(mode="w+", delete=False, suffix=".txt") as temp_file:
                 temp_file.write(content_to_store)
                 temp_file_path = temp_file.name

            try:
                with open(temp_file_path, "rb") as file_obj:
                    # Update metadata to include source type
                    if metadata is None:
                        metadata = {}
                    metadata["source"] = "pubmed"

                    doc_result = transport.store_document(
                        file_obj=file_obj,
                        filename=filename,
                        content_type=content_type,
                        source_path=pmid,
                        metadata=metadata,
                        title=title,
                        check_duplicates=False # Already checked above
                    )
                document_id = doc_result["document_id"]
                logger.info(f"Stored initial document record for PMID {pmid}, Document ID: {document_id}")
                
                # 2b. Store article details in the pubmed_articles table
                try:
                    # Extract additional fields from article_details
                    publication_date = article_details.get("publication_date")
                    publication_year = None
                    if publication_date and isinstance(publication_date, str):
                        # Try to extract year from ISO format date string
                        try:
                            publication_year = int(publication_date.split('-')[0])
                        except (IndexError, ValueError):
                            logger.warning(f"Could not parse publication year from date: {publication_date}")
                    
                    # Store article in pubmed_articles table using the new method
                    pubmed_result = transport.store_pubmed_article(
                        pmid=pmid,
                        title=title,
                        abstract=abstract,
                        document_id=document_id,
                        authors=article_details.get("authors", []),
                        journal=article_details.get("journal"),
                        publication_date=publication_date,
                        publication_year=publication_year,
                        mesh_terms=article_details.get("mesh_terms", []),
                        keywords=article_details.get("keywords", [])
                    )
                    
                    if pubmed_result["status"] == "success":
                        logger.info(f"Stored article details for PMID {pmid} in pubmed_articles table")
                    else:
                        logger.error(f"Error storing article in pubmed_articles table: {pubmed_result.get('error')}")
                    
                except Exception as db_error:
                    logger.error(f"Error storing article in pubmed_articles table: {str(db_error)}")
                    # Continue with the process even if storing in pubmed_articles fails
                    # We'll still have the document record in the main tables

            finally:
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path) # Clean up temp file

            # 3. Create processing task
            task_result = transport.create_processing_task(
                document_id=document_id,
                task_type="pubmed_processing", # Use a specific type
                celery_task_id=self.request.id # Link to this Celery task
            )
            processing_task_id = task_result["task_id"]
            logger.info(f"Created processing task {processing_task_id} for Document ID: {document_id}")

            # 4. Trigger the next processing task
            celery_app.send_task(
                "workers.pubmed.process_pubmed_article",
                args=[str(document_id), str(processing_task_id)],
                queue="document_processing_queue" # Or your relevant queue
            )

            logger.info(f"Sent task to process PubMed article {pmid} (Document ID: {document_id})")

            return {"status": "ingestion_started", "document_id": str(document_id), "processing_task_id": str(processing_task_id)}

    except Exception as e:
        logger.error(f"Error during initial ingestion for PMID {pmid} (Task ID: {self.request.id}): {str(e)}", exc_info=True)
        # Update task status if possible
        if processing_task_id and document_id:
             with DataTransport() as transport:
                 transport.update_task_status(task_id=processing_task_id, status="failed", error=f"Ingestion error: {str(e)}")
                 transport.db_client.update_document_status(document_id=document_id, status="ingestion_failed")
        
        # Retry the task if applicable
        raise self.retry(exc=e)


@celery_app.task(name="workers.pubmed.process_pubmed_article", bind=True, max_retries=3, default_retry_delay=120)
def process_pubmed_article(self, document_id_str: str, processing_task_id_str: str, chunk_type: str = "kg"):
    """
    Celery task to process a fetched PubMed article: chunk for RAG, create single KG chunk, store, and trigger KG build.
    
    Args:
        document_id_str: Document ID string
        processing_task_id_str: Processing task ID string
        chunk_type: Type of chunks to create, default is "kg" for knowledge graph chunks
    """
    document_id = uuid.UUID(document_id_str)
    processing_task_id = uuid.UUID(processing_task_id_str)
    logger.info(f"Starting processing for PubMed Document ID: {document_id} (Task ID: {self.request.id}, Processing Task: {processing_task_id}, chunk_type: {chunk_type})")

    with DataTransport() as transport:
        try:
            # 1. Update task status
            transport.update_task_status(task_id=processing_task_id, status="processing")
            
            # Get task parameters to check if chunk_type was specified
            task = transport.get_processing_task(processing_task_id)
            task_params = task.get("task_metadata", {})
            if isinstance(task_params, dict) and "chunk_type" in task_params:
                # Override chunk_type if specified in task parameters
                chunk_type = task_params["chunk_type"]
                logger.info(f"Using chunk_type from task parameters: {chunk_type}")

            # 2. Get stored document content (potentially full text) for RAG chunking
            stored_content_bytes, doc_metadata = transport.get_document_content(document_id)
            stored_content = stored_content_bytes.decode('utf-8')
            logger.info(f"Retrieved stored document content, length: {len(stored_content)} characters")

            # 2b. Get the PMID from metadata or determine it from other sources
            pmid = doc_metadata.get("pmid")
            logger.info(f"PMID from metadata: {pmid}")

            if not pmid:
                # Attempt to get from source_path if not in metadata
                logger.info("PMID not found in metadata, attempting to get from database")
                db_doc = transport.db_client.get_document(document_id)
                
                if db_doc:
                    # Check if source and source_url attributes exist before accessing
                    source = getattr(db_doc, 'source', None)
                    source_url = getattr(db_doc, 'source_url', None)
                    source_path = getattr(db_doc, 'source_path', None)
                    
                    # Try to get PMID from different possible attributes
                    if source == 'pubmed' and source_url:
                        pmid = source_url
                        logger.info(f"Retrieved PMID from source_url: {pmid}")
                    elif source_path and source_path.isdigit():
                        pmid = source_path
                        logger.info(f"Retrieved PMID from source_path: {pmid}")
                    elif hasattr(db_doc, 'doc_metadata') and db_doc.doc_metadata:
                        metadata = db_doc.doc_metadata
                        if isinstance(metadata, dict) and metadata.get('source') == 'pubmed':
                            pmid = metadata.get('pmid')
                            if pmid:
                                logger.info(f"Retrieved PMID from doc_metadata: {pmid}")
                            else:
                                # Try to extract from filename
                                filename = db_doc.filename if hasattr(db_doc, 'filename') else None
                                if filename and filename.startswith('pubmed_') and '_abstract.txt' in filename:
                                    try:
                                        pmid_part = filename.split('_')[1].split('_abstract.txt')[0]
                                        if pmid_part.isdigit():
                                            pmid = pmid_part
                                            logger.info(f"Retrieved PMID from filename: {pmid}")
                                    except Exception as e:
                                        logger.error(f"Error extracting PMID from filename: {e}")
                else:
                    logger.error(f"Failed to determine PMID for document {document_id}")
                    raise ValueError(f"Cannot determine PMID for document {document_id} to fetch abstract.")

            # 3. Fetch the article details again to ensure we have the abstract for KG
            logger.info(f"Fetching article details for PMID: {pmid}")
            pubmed_service = get_pubmed_service()
            article_details = pubmed_service.get_article_details(pmid)

            # Get the abstract specifically for KG chunking
            abstract = article_details.get("abstract")
            logger.info(f"Abstract retrieved, length: {len(abstract) if abstract else 0}")

            if not abstract:
                logger.error(f"No abstract found for PMID {pmid}")
                raise ValueError(f"Could not retrieve abstract for PMID {pmid} during processing.")

            # --- RAG Chunking (using stored_content - which could be full text or abstract) ---
            logger.info(f"Starting RAG chunking for Document ID: {document_id} using full stored content")
            
            # Create a temporary file with the stored content for RAG chunking
            with tempfile.NamedTemporaryFile(delete=False, suffix='.txt') as temp_file:
                temp_file.write(stored_content.encode('utf-8'))
                temp_file_path = temp_file.name

            try:
                # Use partition_text on the stored content for RAG chunks
                logger.info(f"Partitioning stored content from: {temp_file_path}")
                elements = DocumentPartitioner.partition_text(temp_file_path)
                logger.info(f"Partitioned stored content into {len(elements)} elements for RAG")
                
                # Log a sample of the elements for debugging
                if elements and len(elements) > 0:
                    sample_size = min(2, len(elements))
                    for i in range(sample_size):
                        element = elements[i]
                        if hasattr(element, 'get'):
                            element_type = element.get('type', 'unknown')
                            element_text = element.get('text', '')
                        else:
                            element_type = getattr(element, 'type', 'unknown')
                            element_text = getattr(element, 'text', '')
                            
                        logger.info(f"Element {i+1}/{sample_size} - Type: {element_type}, "
                                  f"Length: {len(element_text) if element_text else 0}, "
                                  f"Sample: {element_text[:100] if element_text else ''}...")
            finally:
                # Clean up the temporary file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)

            # Create RAG chunks from the full content elements
            rag_chunks = ChunkingStrategy.chunk_for_purpose(elements, ChunkingPurpose.RAG)
            logger.info(f"Created {len(rag_chunks)} RAG chunks for Document ID: {document_id}")

            # Always store RAG chunks regardless of chunk_type for search functionality
            if rag_chunks:
                rag_texts = [chunk["text"] for chunk in rag_chunks]
                rag_embeddings = NLPService.generate_semantic_search_embeddings(rag_texts, is_query=False)
                for i, embedding in enumerate(rag_embeddings):
                    rag_chunks[i]["embedding"] = embedding.tolist()
                rag_result = transport.store_document_chunks(document_id, rag_chunks, chunk_type="rag")
                logger.info(f"Stored {rag_result['chunks_count']} RAG chunks for Document ID: {document_id}")
            else:
                logger.warning(f"No RAG chunks created for Document ID: {document_id}")

            # Only create KG chunks if specified
            kg_count = 0
            if chunk_type == "kg":
                # --- KG Chunking (Single Abstract Chunk) ---
                logger.info(f"Creating single KG chunk (abstract) for Document ID: {document_id}")
                kg_chunk = {
                    "id": str(uuid.uuid4()), # Generate a unique ID for the chunk
                    "document_id": str(document_id),
                    "text": abstract, # Use the explicitly fetched abstract for KG
                    "metadata": { # Add relevant metadata
                        "element_type": "abstract",
                        "source": "pubmed",
                        "text_length": len(abstract)
                     },
                     "chunk_type": "kg" # Explicitly set type
                }
                # Generate embedding for the abstract KG chunk
                kg_embedding = NLPService.generate_semantic_search_embeddings([abstract], is_query=False)[0]
                kg_chunk["embedding"] = kg_embedding.tolist()

                # Store the single KG chunk
                kg_result = transport.store_document_chunks(document_id, [kg_chunk], chunk_type="kg")
                kg_count = kg_result['chunks_count']
                logger.info(f"Stored {kg_count} KG chunk (abstract) for Document ID: {document_id}")
            else:
                logger.info(f"Skipping KG chunk creation as chunk_type is set to: {chunk_type}")

            # 4. Update document and task status
            transport.db_client.update_document_status(document_id=document_id, status="processed")
            rag_count = len(rag_chunks) if rag_chunks else 0
            result_data = {
                "document_id": str(document_id),
                "rag_chunks_count": rag_count,
                "kg_chunks_count": kg_count,
                "total_chunks_count": rag_count + kg_count,
                "chunk_type": chunk_type,
                "status": "success",
            }
            transport.update_task_status(
                task_id=processing_task_id,
                status="completed",
                result=result_data
            )
            logger.info(f"Successfully processed PubMed Document ID: {document_id}")

            # 5. Trigger Knowledge Graph building only if KG chunks were created
            if chunk_type == "kg" and kg_count > 0:
                celery_app.send_task(
                    "workers.knowledge_graph.build_knowledge_graph",
                    args=[str(document_id), str(processing_task_id)], # Pass document_id and original task_id
                    kwargs={"use_batch_api": True},  # Use Vertex AI batch processing
                    queue="knowledge_graph"  # Match queue name to what worker is listening to
                )
                logger.info(f"Sent task to build knowledge graph for Document ID: {document_id} using Vertex AI batch processing")
            else:
                logger.info(f"Skipping knowledge graph building as chunk_type is: {chunk_type} or kg_count is: {kg_count}")

            return {"status": "processing_completed", **result_data}

        except Exception as e:
            logger.error(f"Error processing PubMed Document ID {document_id} (Task ID: {self.request.id}, Processing Task: {processing_task_id}): {str(e)}", exc_info=True)
            # Update task status
            transport.update_task_status(task_id=processing_task_id, status="failed", error=f"Processing error: {str(e)}")
            # Update document status
            transport.db_client.update_document_status(document_id=document_id, status="processing_failed")
            # Retry the task if applicable
            raise self.retry(exc=e)
