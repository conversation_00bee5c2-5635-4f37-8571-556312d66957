"""
Celery worker for knowledge graph operations.
"""
import logging
import uuid
import time
from typing import Dict, Any, Optional, Union
from celery import Task

from workers.celery_app import celery_app
from transport.data_transport import DataTransport
from common.database import ProcessingTask

# Import KnowledgeGraphService only when needed
knowledge_graph_service = None

# Fix in knowledge_graph_worker.py
def get_knowledge_graph_service(batch=True):
    """Lazy import of KnowledgeGraphService to avoid loading dependencies at startup."""
    global knowledge_graph_service
    if knowledge_graph_service is None:
        if batch:
            from services.knowledge_graph_service import KnowledgeGraphService
            knowledge_graph_service = KnowledgeGraphService
        else:
            from services.knowledge_graph_alt_service import KnowledgeGraphAltService
            knowledge_graph_service = KnowledgeGraphAltService
    # Return statement should be outside the if block
    return knowledge_graph_service


# Maximum number of tasks to process in a single run
MAX_TASKS_PER_RUN = 10


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class KnowledgeGraphTask(Task):
    """Base task for knowledge graph operations with error handling."""

    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """
        Handle task failure.

        Args:
            exc: Exception
            task_id: Task ID
            args: Task arguments
            kwargs: Task keyword arguments
            einfo: Error info
        """
        document_id = args[0] if args else kwargs.get('document_id')
        if not document_id:
            logger.error(f"Task {task_id} failed without document_id: {exc}")
            return

        # Update task status in database
        try:
            with DataTransport() as transport:
                transport.update_task_status(
                    task_id=kwargs.get('processing_task_id'),
                    status="failed",
                    error=str(exc)
                )
        except Exception as e:
            logger.error(f"Failed to update task status: {e}")


@celery_app.task(name="workers.knowledge_graph.build_knowledge_graph", bind=True, base=KnowledgeGraphTask)
def build_knowledge_graph(self, document_id: Union[str, uuid.UUID], processing_task_id=None, use_batch_api=True, async_processing=True) -> Dict[str, Any]:
    """
    Build a knowledge graph from a document using VertexAI batch processing.

    Args:
        document_id: Document ID
        processing_task_id: Optional ID of the originating processing task
        use_batch_api: Whether to use batch API (default: True)
        async_processing: Whether to use asynchronous processing (default: True)

    Returns:
        Dict[str, Any]: Processing result
    """
    logger.info(f"[DEBUG] Received build_knowledge_graph task for document: {document_id}")
    logger.info(f"[DEBUG] Task parameters - use_batch_api={use_batch_api}, async_processing={async_processing}, processing_task_id={processing_task_id}")
    logger.info(f"[DEBUG] Task ID: {self.request.id}, Task name: {self.request.task}")
    
    # Log queue information and Celery worker details
    logger.info(f"[DEBUG] Queue: {self.request.delivery_info.get('routing_key') if hasattr(self.request, 'delivery_info') else 'unknown'}")
    logger.info(f"[DEBUG] Worker PID: {self.request.hostname if hasattr(self.request, 'hostname') else 'unknown'}")
    
    # Convert document_id to UUID if it's a string
    if isinstance(document_id, str):
        try:
            document_id = uuid.UUID(document_id)
            logger.info(f"[DEBUG] Converted document_id string to UUID: {document_id}")
        except ValueError as e:
            logger.error(f"[DEBUG] Invalid document_id format: {document_id}, Error: {e}")
            return {"status": "error", "message": f"Invalid document_id format: {document_id}"}
    
    try:
        with DataTransport() as transport:
            # Verify document exists in database
            document = transport.db_client.get_document(document_id)
            if not document:
                logger.error(f"[DEBUG] Document not found in database: {document_id}")
                return {"status": "error", "message": f"Document not found: {document_id}"}
            
            logger.info(f"[DEBUG] Document found: {document_id}, Title: {document.title}, Status: {document.status}")
            
            # Check for KG chunks
            chunks = transport.get_document_chunks(document_id, chunk_type="kg")
            logger.info(f"[DEBUG] Found {len(chunks) if chunks else 0} KG chunks for document {document_id}")
            
            # Create a new processing task
            kg_task = transport.create_processing_task(
                document_id=document_id,
                task_type="knowledge_graph_building",
                metadata={
                    "use_batch_api": use_batch_api,
                    "async_processing": async_processing,
                    "processing_method": "async_batch" if (use_batch_api and async_processing) else
                                        "batch" if use_batch_api else "parallel",
                    "original_celery_task_id": self.request.id,
                    "original_processing_task_id": str(processing_task_id) if processing_task_id else None
                }
            )
            logger.info(f"[DEBUG] Created new knowledge graph processing task: {kg_task}")

        # Explicitly log which KnowledgeGraphService implementation we're using
        service_class_name = "KnowledgeGraphService" if use_batch_api else "KnowledgeGraphAltService"
        logger.info(f"[DEBUG] Using {service_class_name} implementation")
        
        # Get KnowledgeGraphService class
        KnowledgeGraphService = get_knowledge_graph_service(use_batch_api)
        
        # Create an instance of KnowledgeGraphService and delegate to it
        try:
            service = KnowledgeGraphService()
            logger.info(f"[DEBUG] Successfully created {service_class_name} instance")
        except Exception as service_init_error:
            logger.error(f"[DEBUG] Error initializing {service_class_name}: {service_init_error}", exc_info=True)
            raise
        
        # The build_knowledge_graph_from_document method accepts 3 parameters: document_id, task_id, and async_mode
        logger.info(f"[DEBUG] Calling build_knowledge_graph_from_document with document_id={document_id}, task_id={kg_task['task_id']}, async_mode={async_processing}")
        result = service.build_knowledge_graph_from_document(document_id, kg_task["task_id"], async_processing)
        logger.info(f"[DEBUG] Knowledge graph building result: {result}")
        
        return result
    except Exception as e:
        logger.error(f"[DEBUG] Error in build_knowledge_graph task: {e}", exc_info=True)
        # Log specific info about exception type
        logger.error(f"[DEBUG] Exception type: {type(e)}, module: {type(e).__module__}")
        # Capture any nested exceptions
        if hasattr(e, '__cause__') and e.__cause__:
            logger.error(f"[DEBUG] Caused by: {e.__cause__}")
        
        # Re-raise the exception to trigger the task's error handling
        raise

@celery_app.task(name="workers.knowledge_graph.build_knowledge_graph_parallel", bind=True, base=KnowledgeGraphTask)
def build_knowledge_graph_parallel(self, document_id: Union[str, uuid.UUID], processing_task_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
    """
    Build a knowledge graph from a document using VertexAI batch processing.

    Args:
        document_id: Document ID
        processing_task_id: Processing task ID

    Returns:
        Dict[str, Any]: Processing result
    """
    logger.info(f"Building knowledge graph for document: {document_id} using VertexAI batch processing")

    # Get KnowledgeGraphService
    KnowledgeGraphService = get_knowledge_graph_service()

    # Delegate to knowledge graph service
    service = KnowledgeGraphService()
    return service.build_knowledge_graph_from_document(document_id, processing_task_id)

@celery_app.task(name="workers.knowledge_graph.query_knowledge_graph", bind=True)
def query_knowledge_graph(self, query_text: str, limit: int = 10) -> Dict[str, Any]:
    """
    Query the knowledge graph.

    Args:
        query_text: Query text
        limit: Maximum number of results

    Returns:
        Dict[str, Any]: Query results
    """
    logger.info(f"Querying knowledge graph: {query_text}")

    # Get KnowledgeGraphService class
    KnowledgeGraphService = get_knowledge_graph_service()

    # Create an instance of KnowledgeGraphService and delegate to it
    service = KnowledgeGraphService()
    return service.query_knowledge_graph(query_text, limit)


@celery_app.task(name="workers.knowledge_graph.check_batch_job_status", bind=True)
def check_batch_job_status(self, task_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
    """
    Check the status of a batch job.

    Args:
        task_id: Processing task ID

    Returns:
        Dict[str, Any]: Batch job status
    """
    logger.info(f"Checking batch job status for task: {task_id}")

    # Get KnowledgeGraphService class
    KnowledgeGraphService = get_knowledge_graph_service()

    # Create an instance of KnowledgeGraphService and delegate to it
    service = KnowledgeGraphService()
    return service.check_batch_job_status(task_id)


@celery_app.task(name="workers.knowledge_graph.continue_knowledge_graph_building", bind=True)
def continue_knowledge_graph_building(self, task_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
    """
    Continue knowledge graph building after batch job completes.

    Args:
        task_id: Processing task ID

    Returns:
        Dict[str, Any]: Processing result
    """
    logger.info(f"Continuing knowledge graph building for task: {task_id}")

    # Get KnowledgeGraphService class
    KnowledgeGraphService = get_knowledge_graph_service()

    # Create an instance of KnowledgeGraphService and delegate to it
    service = KnowledgeGraphService()
    return service.continue_knowledge_graph_building(task_id)


@celery_app.task(name="workers.knowledge_graph.get_entity_neighborhood", bind=True)
def get_entity_neighborhood(self, entity_id: str, depth: int = 1, limit: int = 10) -> Dict[str, Any]:
    """
    Get the neighborhood of an entity in the knowledge graph.

    Args:
        entity_id: Entity ID
        depth: Depth of neighborhood traversal
        limit: Maximum number of results

    Returns:
        Dict[str, Any]: Entity neighborhood
    """
    logger.info(f"Getting entity neighborhood: {entity_id}")

    # Get KnowledgeGraphService class
    KnowledgeGraphService = get_knowledge_graph_service()

    # Create an instance of KnowledgeGraphService and delegate to it
    service = KnowledgeGraphService()
    return service.get_entity_neighborhood(entity_id, depth, limit)


@celery_app.task(name="workers.knowledge_graph.check_and_continue_batch_jobs")
def check_and_continue_batch_jobs() -> Dict[str, Any]:
    """
    Periodic task to check for completed Vertex AI batch jobs and continue knowledge graph building.

    This task runs on a schedule to:
    1. Find and fail stale tasks that have been running for too long (>1 hour)
    2. Check pending vertex_ai_batch_job tasks and update their status
    3. Continue with knowledge graph building for completed jobs

    Returns:
        Dict[str, Any]: Summary of processed tasks
    """
    logger.info("==================== BATCH JOB CHECK STARTED ====================")
    logger.info("Running periodic check for Vertex AI batch jobs")

    stale_task_count = 0
    pending_job_count = 0
    
    try:
        # Get KnowledgeGraphService class
        KnowledgeGraphService = get_knowledge_graph_service()
        
        with DataTransport() as transport:
            # 1. Auto-fail stale tasks older than 1 hour
            logger.info("Checking for stale tasks...")
            stale_tasks = transport.get_stale_tasks(
                task_types=['knowledge_graph_building', 'vertex_ai_batch_job'],
                statuses=['processing', 'pending', 'batch_job_created', 'batch_job_submitted', 'created'],
                hours_threshold=1
            )
            
            if stale_tasks:
                stale_task_ids = [task["id"] for task in stale_tasks]
                stale_task_count = transport.mark_tasks_as_failed(
                    task_ids=stale_task_ids,
                    reason="Task automatically failed after being stuck for over 1 hour"
                )
                logger.info(f"Auto-failed {stale_task_count} stale tasks")
                
                # Process each stale task to find and fail related tasks
                for task in stale_tasks:
                    if task["task_type"] == "vertex_ai_batch_job" and task["result"] and "batch_job_id" in task["result"]:
                        # Find and fail related knowledge_graph_building tasks
                        related_tasks = transport.find_related_kg_tasks(task["result"]["batch_job_id"])
                        if related_tasks:
                            related_task_ids = [related_task["id"] for related_task in related_tasks]
                            related_failed_count = transport.mark_tasks_as_failed(
                                task_ids=related_task_ids,
                                reason=f"Related batch job task {task['id']} failed: Task was stale"
                            )
                            logger.info(f"Auto-failed {related_failed_count} related knowledge graph tasks")
            
            # 2. Check pending vertex_ai_batch_job tasks with Vertex AI service
            logger.info("Checking pending vertex_ai_batch_job tasks...")
            
            # Get batch job tasks that need to be checked
            pending_jobs = transport.get_pending_vertex_ai_batch_jobs(max_tasks=10)
            pending_job_count = len(pending_jobs)
            
            if pending_jobs:
                # Import and initialize the VertexAIBatchClient to check these jobs
                from transport.vertex_ai_batch_client import VertexAIBatchClient
                vertex_client = VertexAIBatchClient()
                
                # Process the pending jobs
                batch_result = vertex_client.process_pending_batch_jobs(max_tasks=10)
                
                if batch_result["status"] == "success":
                    logger.info(f"Processed {batch_result.get('processed', 0)} pending batch jobs: "
                              f"{batch_result.get('completed', 0)} completed, {batch_result.get('failed', 0)} failed")
                else:
                    logger.error(f"Error processing pending batch jobs: {batch_result.get('message', 'Unknown error')}")
            
            # 3. Log task status summary
            task_summary = transport.get_task_status_summary()
            logger.info("Task status summary:")
            for status, count in task_summary.get("status_counts", {}).items():
                logger.info(f"  {status}: {count}")
            
            # Get recent batch jobs for informational purposes
            batch_jobs = transport.get_batch_job_tasks(limit=10)
            vertex_jobs_count = len(batch_jobs.get("vertex_ai_batch_job_tasks", []))
            kg_jobs_count = len(batch_jobs.get("knowledge_graph_building_tasks", []))
            logger.info(f"Recent batch jobs: {vertex_jobs_count} vertex_ai_batch_jobs, {kg_jobs_count} knowledge_graph_building")
            
    except Exception as e:
        logger.error(f"Error during batch jobs check: {e}", exc_info=True)
    
    # Continue with original implementation - checking knowledge graph service
    try:
        service = KnowledgeGraphService()
        result = service.check_and_continue_batch_jobs()
        result.update({
            "stale_tasks_failed": stale_task_count,
            "pending_jobs_checked": pending_job_count
        })
        logger.info(f"Knowledge graph service check_and_continue_batch_jobs result: {result}")
        logger.info("==================== BATCH JOB CHECK COMPLETED ====================")
        return result
    except Exception as e:
        logger.error(f"Error in service.check_and_continue_batch_jobs: {e}", exc_info=True)
        logger.info("==================== BATCH JOB CHECK FAILED ====================")
        return {
            "status": "error", 
            "message": str(e),
            "stale_tasks_failed": stale_task_count,
            "pending_jobs_checked": pending_job_count
        }

