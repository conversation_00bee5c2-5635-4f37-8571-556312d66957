#!/usr/bin/env python
"""
Test script to verify entity extraction from Vertex AI batch response.
This script simulates the batch job response and processes it using the KnowledgeGraphService.
"""

import json
import logging
import os
import sys
import uuid
from pathlib import Path

# Add the parent directory to the path so we can import the services
sys.path.append(str(Path(__file__).parent.parent))

from services.knowledge_graph_service import KnowledgeGraphService
from transport.data_transport import DataTransport
from transport.neo4j_client import Neo4jClient

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def load_sample_response(file_path):
    """Load sample Vertex AI batch response from file"""
    with open(file_path, 'r') as f:
        return json.load(f)

def test_entity_extraction():
    """Test entity extraction from Vertex AI batch response"""
    # Load sample response
    sample_file = os.path.join('tests', 'data', 'sample_vertex_ai_batch_response.json')
    sample_response = load_sample_response(sample_file)

    # Create a document ID for testing
    document_id = str(uuid.uuid4())

    # Create a list of chunks for testing
    chunks = []
    for i, prediction in enumerate(sample_response.get('predictions', [])):
        chunk_id = prediction.get('chunk_id', f'chunk-{i+1}')
        chunks.append({
            'id': chunk_id,
            'document_id': document_id,
            'text': f'Sample text for chunk {i+1}',
            'index': i
        })

    # Initialize Neo4j client and DataTransport
    neo4j_client = Neo4jClient()

    # Process the sample response
    with DataTransport() as transport:
        # Add execute_query method to DatabaseClient
        def execute_query(self, query, params=None):
            # Mock implementation that returns empty list
            return []

        # Add the method to the DatabaseClient instance
        transport.db_client.execute_query = execute_query.__get__(transport.db_client, type(transport.db_client))

        # Add create_entity method to DatabaseClient
        def create_entity(self, id, document_id, chunk_id, text, entity_type, metadata=None, embedding=None):
            # Convert string UUID to UUID object if needed
            if isinstance(id, str):
                id = uuid.UUID(id)
            if isinstance(document_id, str):
                document_id = uuid.UUID(document_id)
            if chunk_id and isinstance(chunk_id, str):
                chunk_id = uuid.UUID(chunk_id)

            # Return a mock entity object
            class MockEntity:
                pass
            entity = MockEntity()
            entity.id = id
            entity.document_id = document_id
            entity.chunk_id = chunk_id
            entity.text = text
            entity.entity_type = entity_type
            entity.metadata = metadata or {}
            entity.embedding = embedding
            return entity

        # Add the method to the DatabaseClient instance
        transport.db_client.create_entity = create_entity.__get__(transport.db_client, type(transport.db_client))

        # Create a document first
        document = transport.db_client.create_document(
            filename="test_document.pdf",
            source_path="test",
            content_type="application/pdf",
            storage_path="test",
            file_size=1000,
            metadata={"test": True}
        )
        document_id = str(document.id)

        # Create a processing task
        task = transport.db_client.create_processing_task(
            document_id=document.id,
            task_type='knowledge_graph_building'
        )
        processing_task_id = str(task.id)

        # Update task with metadata
        transport.db_client.update_task_status(
            task_id=task.id,
            status='processing',
            result={'chunks_count': len(chunks)}
        )

        # Create a mock NLPService class
        class MockNLPService:
            @staticmethod
            def deduplicate_and_normalize_entities(entities, document_id):
                return entities

            @staticmethod
            def generate_embeddings(texts):
                return [[0.1, 0.2, 0.3, 0.4, 0.5] for _ in texts]

        # Skip Neo4j operations by creating a mock Neo4j client
        class MockNeo4jClient:
            def store_chunk(self, chunk_id, document_id, chunk_data):
                return {"id": chunk_id, "status": "success"}

            def store_entity(self, entity_id, entity_data):
                return {"id": entity_id, "status": "success"}

            def store_entities(self, chunk_id, entities):
                return {"status": "success", "count": len(entities)}

            def create_entity_relationship(self, entity_id, chunk_id, relationship_type):
                return {"status": "success"}

            def create_entity_to_entity_relationship(self, source_id, target_id, relationship_type):
                return {"status": "success"}

            def create_chunk_relationships(self, document_id, chunks):
                return {"status": "success"}

            def close(self):
                pass

        # Process entities
        total_entities, deduplicated_entities, entity_id_map = KnowledgeGraphService._process_entities(
            sample_response.get('predictions', []),
            chunks,
            document_id,
            MockNeo4jClient(),
            transport,
            processing_task_id,
            MockNLPService  # Use our mock NLPService
        )

        # Print results
        logger.info(f"Processed {total_entities} entities")
        logger.info(f"Deduplicated entities: {len(deduplicated_entities)}")

        # Instead of querying the database, use the deduplicated_entities list
        logger.info(f"Entities extracted from Vertex AI batch response: {len(deduplicated_entities)}")

        # Print the first few entities
        for i, entity in enumerate(deduplicated_entities[:5]):
            logger.info(f"Entity {i+1}: {entity['text']} ({entity['type']})")

        # Clean up
        try:
            # Delete document (will cascade to delete related entities, chunks, and tasks)
            transport.db_client.delete_document(document_id)
            logger.info("Cleanup completed successfully")
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")

if __name__ == '__main__':
    test_entity_extraction()
