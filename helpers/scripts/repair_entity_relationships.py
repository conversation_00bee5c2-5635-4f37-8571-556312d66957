#!/usr/bin/env python
"""
Script to repair entity relationships in Neo4j.

Usage:
    python repair_entity_relationships.py [--document_id=<uuid>]
"""
import argparse
import logging
import sys
import os
import uuid

# Add project root to path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

from transport.neo4j_client import Neo4jClient
from transport.data_transport import DataTransport
from common.config import settings
from neo4j.exceptions import Neo4jError

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def repair_entity_relationships(document_id=None):
    """
    Repair entity relationships in Neo4j.
    
    Args:
        document_id: Optional document ID to repair relationships for
    """
    print("Repairing entity relationships in Neo4j...")
    
    # Override settings for local connection if needed
    if os.environ.get("NEO4J_HOST"):
        settings.NEO4J_HOST = os.environ.get("NEO4J_HOST")
    if os.environ.get("NEO4J_PORT"):
        settings.NEO4J_PORT = int(os.environ.get("NEO4J_PORT"))
    
    print(f"Connecting to Neo4j at {settings.NEO4J_HOST}:{settings.NEO4J_PORT}")
    
    neo4j_client = Neo4jClient()
    
    try:
        with neo4j_client._driver.session() as session:
            # Check relationship counts before repair
            result = session.run("MATCH (e1:Entity)-[r]->(e2:Entity) WHERE type(r) <> 'MENTIONED_IN' RETURN count(r) as count")
            before_count = result.single()["count"]
            print(f"Entity relationships before repair: {before_count}")
            
            # If a document ID was provided, only repair that document
            if document_id:
                print(f"Repairing relationships for document {document_id}")
                
                # Find relationships in PostgreSQL that might be missing in Neo4j
                query = """
                MATCH (d:Document {id: $document_id})
                WITH d
                MATCH (c:Chunk)-[:PART_OF]->(d)
                WITH c, c.id as chunk_id
                MATCH (e1:Entity)-[:MENTIONED_IN]->(c)
                MATCH (e2:Entity)-[:MENTIONED_IN]->(c)
                WHERE e1 <> e2 AND NOT (e1)-[:RELATED_TO|TREATS|CAUSES|DIAGNOSES|CONTRAINDICATES|PREVENTS|PERFORMS]-(e2)
                RETURN e1.id as source_id, e1.text as source_text, e1.type as source_type,
                       e2.id as target_id, e2.text as target_text, e2.type as target_type,
                       c.id as chunk_id
                LIMIT 1000
                """
                
                result = session.run(query, document_id=document_id)
                records = list(result)
                
                print(f"Found {len(records)} potential entity pairs missing relationships")
                
                # Create relationships for these pairs
                created_count = 0
                for record in records:
                    try:
                        # Look up relationship in PostgreSQL via DataTransport
                        with DataTransport() as transport:
                            db_client = transport.db_client
                            relationships = db_client.get_relationships_between_entities(
                                source_id=record["source_id"],
                                target_id=record["target_id"]
                            )
                            
                            if relationships:
                                # If relationship exists in PostgreSQL, recreate it in Neo4j
                                rel = relationships[0]
                                
                                create_query = """
                                MATCH (source:Entity {id: $source_id})
                                MATCH (target:Entity {id: $target_id})
                                CALL apoc.do.when(
                                    $rel_type = 'RELATED_TO',
                                    'MERGE (source)-[r:RELATED_TO]->(target) SET r += properties RETURN r',
                                    'MERGE (source)-[r:' + $rel_type + ']->(target) SET r += properties RETURN r',
                                    {source: source, target: target, properties: {
                                        id: $rel_id,
                                        confidence: $confidence,
                                        evidence_text: $evidence_text,
                                        chunk_id: $chunk_id
                                    }}
                                ) YIELD value
                                RETURN value.r AS created_rel
                                """
                                
                                session.run(
                                    create_query,
                                    source_id=str(rel.source_id),
                                    target_id=str(rel.target_id),
                                    rel_type=rel.relationship_type.upper(),
                                    rel_id=str(rel.id),
                                    confidence=rel.confidence or 1.0,
                                    evidence_text=rel.evidence_text or "",
                                    chunk_id=record["chunk_id"]
                                )
                                created_count += 1
                            else:
                                # If no relationship in PostgreSQL, create a default RELATED_TO relationship
                                rel_id = str(uuid.uuid4())
                                rel_type = "RELATED_TO"
                                
                                # Check if the entity types suggest a specific relationship
                                source_type = record["source_type"].upper()
                                target_type = record["target_type"].upper()
                                
                                # Define specific relationship types based on entity type pairs
                                if source_type == "MEDICATION" and target_type == "CONDITION":
                                    rel_type = "TREATS"
                                elif source_type == "CONDITION" and target_type == "CONDITION":
                                    rel_type = "ASSOCIATED_WITH"
                                elif source_type == "TREATMENT" and target_type == "CONDITION":
                                    rel_type = "TREATS" 
                                
                                create_query = """
                                MATCH (source:Entity {id: $source_id})
                                MATCH (target:Entity {id: $target_id})
                                MERGE (source)-[r:%s]->(target)
                                SET r += $properties
                                RETURN source.id AS source_id, target.id AS target_id, type(r) AS type
                                """ % rel_type
                                
                                session.run(
                                    create_query,
                                    source_id=record["source_id"],
                                    target_id=record["target_id"],
                                    properties={
                                        "id": rel_id,
                                        "confidence": 0.8,
                                        "evidence_text": f"Inferred relationship between {record['source_text']} and {record['target_text']}",
                                        "chunk_id": record["chunk_id"],
                                        "auto_created": True
                                    }
                                )
                                created_count += 1
                                
                    except Exception as e:
                        print(f"Error creating relationship: {e}")
                        
                print(f"Created {created_count} new entity relationships")
            else:
                # Run repair across all documents
                print("Repairing entity relationships across all documents")
                
                # Create any missing entity-entity relationships
                repair_query = """
                MATCH (c:Chunk)
                WITH c
                MATCH (e1:Entity)-[:MENTIONED_IN]->(c)
                MATCH (e2:Entity)-[:MENTIONED_IN]->(c)
                WHERE e1 <> e2 AND e1.type = e2.type AND NOT (e1)-[:RELATED_TO]->(e2)
                WITH e1, e2, c
                LIMIT 1000
                MERGE (e1)-[r:RELATED_TO]->(e2)
                SET r.id = apoc.create.uuid(),
                    r.auto_created = true,
                    r.confidence = 0.7,
                    r.chunk_id = c.id
                RETURN count(*) as created
                """
                
                try:
                    result = session.run(repair_query)
                    created = result.single()["created"]
                    print(f"Created {created} missing RELATED_TO relationships")
                except Neo4jError as e:
                    print(f"Error running repair query: {e}")
            
            # Check relationship counts after repair
            result = session.run("MATCH (e1:Entity)-[r]->(e2:Entity) WHERE type(r) <> 'MENTIONED_IN' RETURN count(r) as count")
            after_count = result.single()["count"]
            print(f"Entity relationships after repair: {after_count}")
            print(f"Created {after_count - before_count} new relationships")
            
            # Show sample created relationships
            result = session.run("""
            MATCH (e1:Entity)-[r]->(e2:Entity)
            WHERE type(r) <> 'MENTIONED_IN' AND r.auto_created = true
            RETURN e1.text as source, type(r) as relationship, e2.text as target
            LIMIT 10
            """)
            
            print("\nSample created relationships:")
            for record in result:
                print(f"- {record['source']} --[{record['relationship']}]--> {record['target']}")
                
    finally:
        neo4j_client.close()

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Repair entity relationships in Neo4j')
    parser.add_argument('--document_id', type=str, help='Document ID to repair relationships for')
    
    args = parser.parse_args()
    repair_entity_relationships(args.document_id)

if __name__ == '__main__':
    main()