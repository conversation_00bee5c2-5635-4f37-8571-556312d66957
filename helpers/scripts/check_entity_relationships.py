#!/usr/bin/env python
"""
Script to check and debug entity relationships in Neo4j.

Usage:
    python check_entity_relationships.py [--document_id=<uuid>]
"""
import argparse
import logging
import sys
import os

# Add project root to path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

from transport.neo4j_client import Neo4jClient
from transport.data_transport import DataTransport
from common.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_entity_relationships(document_id=None):
    """
    Check entity relationships in Neo4j.
    
    Args:
        document_id: Optional document ID to check relationships for
    """
    print("Checking entity relationships in Neo4j...")
    
    # Override settings for local connection if needed
    if os.environ.get("NEO4J_HOST"):
        settings.NEO4J_HOST = os.environ.get("NEO4J_HOST")
    if os.environ.get("NEO4J_PORT"):
        settings.NEO4J_PORT = int(os.environ.get("NEO4J_PORT"))
    
    print(f"Connecting to Neo4j at {settings.NEO4J_HOST}:{settings.NEO4J_PORT}")
    
    neo4j_client = Neo4jClient()
    
    try:
        # Get global relationship counts
        with neo4j_client._driver.session() as session:
            # Check entity counts first
            result = session.run("MATCH (e:Entity) RETURN count(e) as count")
            entity_count = result.single()["count"]
            print(f"\nTotal entities in Neo4j: {entity_count}")
            
            # Check document counts
            result = session.run("MATCH (d:Document) RETURN count(d) as count")
            document_count = result.single()["count"]
            print(f"Total documents in Neo4j: {document_count}")
            
            # Check chunk counts
            result = session.run("MATCH (c:Chunk) RETURN count(c) as count")
            chunk_count = result.single()["count"]
            print(f"Total chunks in Neo4j: {chunk_count}")
            
            # Check all relationship types
            result = session.run("MATCH ()-[r]->() RETURN type(r), count(r) as count ORDER BY count DESC")
            relationships = list(result)
            
            if relationships:
                print("\nRelationship types in Neo4j:")
                for record in relationships:
                    rel_type = record["type(r)"]
                    count = record["count"]
                    print(f"- {rel_type}: {count}")
                    
            # Check entity-entity relationships specifically
            result = session.run("MATCH (e1:Entity)-[r]->(e2:Entity) WHERE type(r) <> 'MENTIONED_IN' RETURN type(r), count(r) as count ORDER BY count DESC")
            entity_relationships = list(result)
            
            if entity_relationships:
                print("\nEntity-entity relationships:")
                for record in entity_relationships:
                    rel_type = record["type(r)"]
                    count = record["count"]
                    print(f"- {rel_type}: {count}")
            else:
                print("\nNo entity-entity relationships found.")
                
            # If a document ID was provided, check relationships for that document
            if document_id:
                print(f"\nChecking relationships for document {document_id}")
                
                # Check entities in document
                result = session.run("""
                MATCH (d:Document {id: $document_id})<-[:PART_OF]-(c:Chunk)<-[:MENTIONED_IN]-(e:Entity)
                RETURN count(DISTINCT e) as entity_count
                """, document_id=document_id)
                doc_entity_count = result.single()["entity_count"]
                print(f"Entities in document: {doc_entity_count}")
                
                # Check entity-entity relationships in document
                result = session.run("""
                MATCH (d:Document {id: $document_id})<-[:PART_OF]-(c:Chunk)<-[:MENTIONED_IN]-(e1:Entity)
                MATCH (e1)-[r]->(e2:Entity)
                WHERE type(r) <> 'MENTIONED_IN'
                RETURN type(r), count(r) as count ORDER BY count DESC
                """, document_id=document_id)
                doc_entity_relationships = list(result)
                
                if doc_entity_relationships:
                    print(f"Entity-entity relationships in document {document_id}:")
                    for record in doc_entity_relationships:
                        rel_type = record["type(r)"]
                        count = record["count"]
                        print(f"- {rel_type}: {count}")
                else:
                    print(f"No entity-entity relationships found in document {document_id}.")
                    
                # Show sample relationships
                print("\nSample entity relationships in this document:")
                result = session.run("""
                MATCH (d:Document {id: $document_id})<-[:PART_OF]-(c:Chunk)<-[:MENTIONED_IN]-(e1:Entity)
                MATCH (e1)-[r]->(e2:Entity)
                WHERE type(r) <> 'MENTIONED_IN'
                RETURN e1.text as source, type(r) as relationship, e2.text as target
                LIMIT 10
                """, document_id=document_id)
                
                samples = list(result)
                if samples:
                    for idx, record in enumerate(samples):
                        print(f"{idx+1}. {record['source']} --[{record['relationship']}]--> {record['target']}")
                else:
                    print("No samples found.")
    finally:
        neo4j_client.close()

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Check entity relationships in Neo4j')
    parser.add_argument('--document_id', type=str, help='Document ID to check relationships for')
    
    args = parser.parse_args()
    check_entity_relationships(args.document_id)

if __name__ == '__main__':
    main()