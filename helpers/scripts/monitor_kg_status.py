#!/usr/bin/env python
"""
Script to monitor the status of the knowledge graph building process.
"""
import time
import sys
import os
from datetime import datetime

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from transport.neo4j_client import Neo4jClient
from common.config import settings

def check_processing_tasks():
    """Check the status of knowledge graph building tasks using docker-compose."""
    print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Checking knowledge graph building tasks...")

    # Use docker-compose to check the status of the tasks
    import subprocess

    try:
        # Run the command to check the status of the tasks
        cmd = "docker-compose exec -T worker python -c \"from workers.knowledge_graph import check_and_continue_batch_jobs; result = check_and_continue_batch_jobs(); print(result)\""
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

        if result.returncode == 0:
            print(f"Command output: {result.stdout}")
        else:
            print(f"Command failed with error: {result.stderr}")
            print("Falling back to checking worker logs...")

            # Check the worker logs
            cmd = "docker-compose logs --tail=50 worker | grep -i 'knowledge_graph_building'"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)

            if result.returncode == 0:
                print(f"Worker logs: {result.stdout}")
            else:
                print(f"Failed to get worker logs: {result.stderr}")
    except Exception as e:
        print(f"Error checking processing tasks: {e}")

def check_neo4j_entities():
    """Check entities in Neo4j."""
    print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Checking entities in Neo4j...")

    # Override settings for local connection
    settings.NEO4J_HOST = "localhost"
    settings.NEO4J_PORT = 7687
    settings.NEO4J_URI = f"bolt://{settings.NEO4J_HOST}:{settings.NEO4J_PORT}"

    neo4j_client = Neo4jClient()

    try:
        # Check entity count
        with neo4j_client._driver.session() as session:
            result = session.run("MATCH (e:Entity) RETURN count(e) as count")
            record = result.single()
            entity_count = record["count"] if record else 0

            print(f"Found {entity_count} entities in Neo4j.")

            if entity_count > 0:
                # Check entity types
                result = session.run("MATCH (e:Entity) RETURN e.type, count(e) as count ORDER BY count DESC LIMIT 10")
                records = list(result)

                if records:
                    print("\nEntity types in Neo4j:")
                    for record in records:
                        entity_type = record["e.type"]
                        count = record["count"]
                        print(f"- {entity_type}: {count}")

                # Check entity properties
                result = session.run("MATCH (e:Entity) RETURN e LIMIT 5")
                records = list(result)

                if records:
                    print("\nSample entity properties:")
                    for i, record in enumerate(records):
                        entity = record["e"]
                        print(f"\nEntity {i+1}:")
                        for key, value in entity.items():
                            print(f"  {key}: {value}")
    finally:
        neo4j_client.close()

def check_neo4j_relationships():
    """Check relationships in Neo4j."""
    print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] Checking relationships in Neo4j...")

    # Override settings for local connection
    settings.NEO4J_HOST = "localhost"
    settings.NEO4J_PORT = 7687
    settings.NEO4J_URI = f"bolt://{settings.NEO4J_HOST}:{settings.NEO4J_PORT}"

    neo4j_client = Neo4jClient()

    try:
        # Check relationship count
        with neo4j_client._driver.session() as session:
            result = session.run("MATCH ()-[r]->() RETURN count(r) as count")
            record = result.single()
            rel_count = record["count"] if record else 0

            print(f"Found {rel_count} relationships in Neo4j.")

            if rel_count > 0:
                # Check relationship types
                result = session.run("MATCH ()-[r]->() RETURN type(r), count(r) as count ORDER BY count DESC LIMIT 10")
                records = list(result)

                if records:
                    print("\nRelationship types in Neo4j:")
                    for record in records:
                        rel_type = record["type(r)"]
                        count = record["count"]
                        print(f"- {rel_type}: {count}")

                # Check entity-entity relationships
                result = session.run("MATCH (e1:Entity)-[r]->(e2:Entity) WHERE type(r) <> 'MENTIONED_IN' RETURN type(r), count(r) as count ORDER BY count DESC LIMIT 10")
                records = list(result)

                if records:
                    print("\nEntity-entity relationship types:")
                    for record in records:
                        rel_type = record["type(r)"]
                        count = record["count"]
                        print(f"- {rel_type}: {count}")
                else:
                    print("\nNo entity-entity relationships found.")

                # Check relationship properties
                result = session.run("MATCH (e1:Entity)-[r]->(e2:Entity) WHERE type(r) <> 'MENTIONED_IN' RETURN e1.text as source, type(r) as type, e2.text as target, properties(r) as props LIMIT 5")
                records = list(result)

                if records:
                    print("\nSample entity-entity relationships:")
                    for i, record in enumerate(records):
                        source = record["source"]
                        rel_type = record["type"]
                        target = record["target"]
                        props = record["props"]
                        print(f"\nRelationship {i+1}: {source} --[{rel_type}]--> {target}")
                        for key, value in props.items():
                            print(f"  {key}: {value}")
    finally:
        neo4j_client.close()

def main():
    """Main function."""
    # Check status every 5 minutes
    interval = 300  # 5 minutes in seconds
    max_checks = 12  # Check for up to 1 hour

    for i in range(max_checks):
        print(f"\n=== Check {i+1}/{max_checks} ===")

        try:
            check_processing_tasks()
            check_neo4j_entities()
            check_neo4j_relationships()
        except Exception as e:
            print(f"Error during check: {e}")

        if i < max_checks - 1:
            print(f"\nWaiting {interval} seconds for next check...")
            time.sleep(interval)

    return 0

if __name__ == "__main__":
    sys.exit(main())
