#!/usr/bin/env python
"""
Script to check Neo4j database.
"""
import sys
import os

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from transport.neo4j_client import Neo4jClient
from common.config import settings

def check_entity_types():
    """Check entity types in Neo4j."""
    print("Checking entity types in Neo4j...")

    # Override settings for local connection
    settings.NEO4J_HOST = "localhost"
    settings.NEO4J_PORT = 7687
    settings.NEO4J_URI = f"bolt://{settings.NEO4J_HOST}:{settings.NEO4J_PORT}"

    print(f"Connecting to Neo4j at {settings.NEO4J_URI}")

    neo4j_client = Neo4jClient()

    try:
        # Check entity types
        with neo4j_client._driver.session() as session:
            result = session.run("MATCH (e:Entity) RETURN e.type, count(e) as count ORDER BY count DESC LIMIT 10")
            records = list(result)

            if not records:
                print("No entities found in Neo4j.")
            else:
                print("Entity types in Neo4j:")
                for record in records:
                    print(f"- {record['e.type']}: {record['count']}")
    finally:
        # Close client
        neo4j_client.close()

def check_entity_relationships():
    """Check entity-entity relationships in Neo4j."""
    print("Checking entity-entity relationships in Neo4j...")

    # Override settings for local connection
    settings.NEO4J_HOST = "localhost"
    settings.NEO4J_PORT = 7687
    settings.NEO4J_URI = f"bolt://{settings.NEO4J_HOST}:{settings.NEO4J_PORT}"

    neo4j_client = Neo4jClient()

    try:
        # Check entity-entity relationships
        with neo4j_client._driver.session() as session:
            result = session.run("MATCH (e1:Entity)-[r]->(e2:Entity) WHERE type(r) <> 'MENTIONED_IN' RETURN type(r), count(r) as count ORDER BY count DESC LIMIT 10")
            records = list(result)

            if not records:
                print("No entity-entity relationships found in Neo4j.")
            else:
                print("Entity-entity relationships in Neo4j:")
                for record in records:
                    print(f"- {record['type(r)']}: {record['count']}")
    finally:
        # Close client
        neo4j_client.close()

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "relationships":
        check_entity_relationships()
    else:
        check_entity_types()
