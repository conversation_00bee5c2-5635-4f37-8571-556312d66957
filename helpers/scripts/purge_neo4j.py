#!/usr/bin/env python
"""
Script to purge Neo4j database.
"""
import sys
import os

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from transport.neo4j_client import Neo4jClient
from common.config import settings

def purge_neo4j():
    """Purge all data from Neo4j."""
    print("Purging Neo4j database...")

    # Override settings for local connection
    settings.NEO4J_HOST = "localhost"
    settings.NEO4J_PORT = 7687
    settings.NEO4J_URI = f"bolt://{settings.NEO4J_HOST}:{settings.NEO4J_PORT}"

    print(f"Connecting to Neo4j at {settings.NEO4J_URI}")

    neo4j_client = Neo4jClient()

    try:
        # Delete all nodes and relationships
        with neo4j_client._driver.session() as session:
            session.run("MATCH (n) DETACH DELETE n")
        print("Neo4j database purged successfully.")
    finally:
        # Close client
        neo4j_client.close()

if __name__ == "__main__":
    purge_neo4j()
