# Helper Scripts

This directory contains helper scripts for testing, monitoring, and maintaining the knowledge graph system.

## Scripts

### purge_and_test.sh

A comprehensive script that:
1. Purges the PostgreSQL database
2. Purges the Neo4j database
3. Runs the full ingest test
4. Checks entity types in Neo4j
5. Checks entity-entity relationships in Neo4j

Usage:
```bash
./helpers/scripts/purge_and_test.sh
```

### purge_neo4j.py

A Python script to purge all data from the Neo4j database.

Usage:
```bash
python helpers/scripts/purge_neo4j.py
```

### check_neo4j.py

A Python script to check entity types and entity-entity relationships in Neo4j.

Usage:
```bash
# Check entity types
python helpers/scripts/check_neo4j.py

# Check entity-entity relationships
python helpers/scripts/check_neo4j.py relationships
```

### monitor_kg_status.py

A Python script to monitor the status of the knowledge graph building process, including:
1. Checking processing tasks
2. Checking entities in Neo4j
3. Checking relationships in Neo4j

Usage:
```bash
python helpers/scripts/monitor_kg_status.py
```

## Running Scripts from Project Root

All scripts are designed to be run from the project root directory. For example:

```bash
# From project root
./helpers/scripts/purge_and_test.sh
```

## Adding New Scripts

When adding new helper scripts:
1. Place them in this directory
2. Make them executable (`chmod +x script_name.sh`)
3. Update this README.md file with documentation
4. Ensure they can be run from the project root directory
