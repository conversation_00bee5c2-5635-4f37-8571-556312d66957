#!/bin/bash

# Set colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
  local color=$1
  local message=$2
  echo -e "${color}${message}${NC}"
}

# Function to check if a command was successful
check_success() {
  if [ $? -eq 0 ]; then
    print_message "$GREEN" "$1"
  else
    print_message "$RED" "$2"
    exit 1
  fi
}

# Purge PostgreSQL database
print_message "$YELLOW" "Purging PostgreSQL database..."
docker-compose exec -T postgres psql -U longevity -d longevity -c "
DELETE FROM relationships;
DELETE FROM entities;
DELETE FROM kg_chunks;
DELETE FROM chunks;
DELETE FROM processing_tasks;
DELETE FROM documents;
"
check_success "PostgreSQL database purged successfully" "Failed to purge PostgreSQL database"

# Purge Neo4j database
print_message "$YELLOW" "Purging Neo4j database..."
python helpers/scripts/purge_neo4j.py
check_success "Neo4j database purged successfully" "Failed to purge Neo4j database"

# Run the full ingest test
print_message "$YELLOW" "Running full ingest test..."
./tests/system/run_full_ingest_test.sh
check_success "Full ingest test completed successfully" "Full ingest test failed"

# Check entity types in Neo4j
print_message "$YELLOW" "Checking entity types in Neo4j..."
python helpers/scripts/check_neo4j.py
check_success "Entity types check completed" "Failed to check entity types"

# Check entity-entity relationships in Neo4j
print_message "$YELLOW" "Checking entity-entity relationships in Neo4j..."
python helpers/scripts/check_neo4j.py relationships
check_success "Entity-entity relationships check completed" "Failed to check entity-entity relationships"

print_message "$GREEN" "All tests completed successfully!"
