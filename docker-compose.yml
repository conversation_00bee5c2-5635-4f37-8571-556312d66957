services:
  api:
    build:
      context: .
      dockerfile: docker/api/Dockerfile
    ports:
      - "8000:8000"
    volumes:
      - ./:/app
      - ./gcs_login.json:/app/gcs_login.json:ro
    env_file:
      - .env
    environment:
      - GOOGLE_APPLICATION_CREDENTIALS=/app/gcs_login.json
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_started
      minio:
        condition: service_healthy
      neo4j:
        condition: service_started
    networks:
      - longevity-network

  worker:
    build:
      context: .
      dockerfile: docker/worker/Dockerfile
    volumes:
      - ./:/app
      - ./gcs_login.json:/app/gcs_login.json:ro
      - celerybeat_data:/app/celerybeat
    env_file:
      - .env
    environment:
      - GOOGLE_APPLICATION_CREDENTIALS=/app/gcs_login.json
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    depends_on:
      minio:
        condition: service_healthy
      redis:
        condition: service_started
      postgres:
        condition: service_started
    networks:
      - longevity-network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --requirepass ${REDIS_PASSWORD}
    networks:
      - longevity-network

  postgres:
    build:
      context: ./docker/postgres
      dockerfile: Dockerfile
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-longevity}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-longevitypass}
      POSTGRES_DB: ${POSTGRES_DB:-longevity}
      EMBEDDING_DIMENSION: ${EMBEDDING_DIMENSION:-1024}
    ports:
      - "5435:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-longevity} -d ${POSTGRES_DB:-longevity}"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - longevity-network

  minio:
    image: minio/minio
    ports:
      - "9090:9000"
      - "9091:9001"
    volumes:
      - minio_data:/data
    environment:
      MINIO_ROOT_USER: ${MINIO_ROOT_USER:-minio}
      MINIO_ROOT_PASSWORD: ${MINIO_ROOT_PASSWORD:-minio123}
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 5s
      timeout: 5s
      retries: 3
    networks:
      - longevity-network

  createbuckets:
    image: minio/mc
    depends_on:
      minio:
        condition: service_healthy
    entrypoint: >
      /bin/sh -c "
      until /usr/bin/mc config host add myminio http://minio:9000 ${MINIO_ROOT_USER:-minio} ${MINIO_ROOT_PASSWORD:-minio123}; do
        echo 'Waiting for MinIO to be ready...'
        sleep 2
      done;
      /usr/bin/mc mb --ignore-existing myminio/${STORAGE_BUCKET_NAME:-longevity-documents};
      /usr/bin/mc anonymous set download myminio/${STORAGE_BUCKET_NAME:-longevity-documents};
      exit 0;
      "
    networks:
      - longevity-network

  neo4j:
    image: neo4j:5.13
    ports:
      - "7474:7474"
      - "7687:7687"
    environment:
      NEO4J_AUTH: ${NEO4J_USER:-neo4j}/${NEO4J_PASSWORD:-longevityneo4j}
      NEO4J_apoc_export_file_enabled: "true"
      NEO4J_apoc_import_file_enabled: "true"
      NEO4J_apoc_import_file_use__neo4j__config: "true"
      NEO4J_PLUGINS: '["apoc"]'
    volumes:
      - neo4j_data:/data
      - neo4j_logs:/logs
      - neo4j_import:/var/lib/neo4j/import
      - neo4j_plugins:/plugins
    networks:
      - longevity-network

volumes:
  redis_data:
  postgres_data:
  minio_data:
  neo4j_data:
  neo4j_logs:
  neo4j_import:
  neo4j_plugins:
  celerybeat_data:

networks:
  longevity-network:
    driver: bridge
