services:
  postgres:
    build:
      context: ./docker/postgres
      dockerfile: Dockerfile
    restart: always
    env_file:
      - .env
    environment:
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_DB: ${POSTGRES_DB}
      EMBEDDING_DIMENSION: ${EMBEDDING_DIMENSION:-1024}
    ports:
      - "5432:5432"
    volumes:
      # Store data in a named volume
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-longevity} -d ${POSTGRES_DB:-longevity}"]
      interval: 5s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7
    restart: always
    env_file:
      - .env
    environment:
      REDIS_PASSWORD: ${REDIS_PASSWORD}
    command: redis-server --requirepass ${REDIS_PASSWORD}
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "${REDIS_PASSWORD}", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5

  neo4j:
    image: neo4j:5
    restart: always
    env_file:
      - .env
    environment:
      NEO4J_AUTH: ${NEO4J_AUTH}
      NEO4J_server_config_strict__validation_enabled: "false"
    ports:
      - "7474:7474"  # HTTP
      - "7687:7687"  # Bolt
    volumes:
      - neo4j_data:/data
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:7474"]
      interval: 10s
      timeout: 10s
      retries: 5

  api:
    build:
      context: .
      dockerfile: docker/api/Dockerfile
    restart: always
    env_file:
      - .env
    environment:
      - PYTHONPATH=/app
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_HOST=${POSTGRES_HOST}
      - POSTGRES_PORT=${POSTGRES_PORT}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - NEO4J_AUTH=${NEO4J_AUTH}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      neo4j:
        condition: service_healthy
    ports:
      - "8000:8000"
    volumes:
      - ./api:/app/api
      - ./common:/app/common
      - ./services:/app/services
      - ./transport:/app/transport
      - ./logger:/app/logger
      - ./config:/app/config
      - ./scripts:/app/scripts
      - ./gcs_login.json:/app/gcs_login.json

  worker:
    build:
      context: .
      dockerfile: docker/worker/Dockerfile
    restart: always
    env_file:
      - .env
    environment:
      - PYTHONPATH=/app
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_HOST=${POSTGRES_HOST}
      - POSTGRES_PORT=${POSTGRES_PORT}
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - NEO4J_AUTH=${NEO4J_AUTH}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      neo4j:
        condition: service_healthy
    volumes:
      - ./workers:/app/workers
      - ./common:/app/common
      - ./services:/app/services
      - ./transport:/app/transport
      - ./logger:/app/logger
      - ./config:/app/config
      - ./scripts:/app/scripts
      - ./gcs_login.json:/app/gcs_login.json

volumes:
  postgres_data:
  redis_data:
  neo4j_data:
