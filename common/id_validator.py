"""
Utility module for ID validation and normalization.

This module provides functions for validating and normalizing IDs,
particularly chunk IDs, entity IDs, and other identifiers used in the system.

The module includes functions for:
- Validating and normalizing IDs
- Checking if an ID is a valid UUID
- Safely converting IDs to UUID objects
- Validating IDs with context-specific error handling
"""

import re
import uuid
import logging

# Configure logging
logger = logging.getLogger(__name__)

def validate_and_normalize_id(id_value, log_prefix=""):
    """
    Validate and normalize an ID value.

    This function handles various ID formats:
    - UUIDs (with or without braces)
    - Alphanumeric strings with hyphens
    - Other strings (cleaned to remove invalid characters)

    Args:
        id_value: The ID value to validate and normalize
        log_prefix: Optional prefix for log messages

    Returns:
        str: Normalized ID value
    """
    if id_value is None:
        logger.error(f"{log_prefix}ID value is None")
        return None

    # Convert to string if not already
    if not isinstance(id_value, str):
        try:
            id_value = str(id_value)
            logger.info(f"{log_prefix}Converted ID to string: {id_value}")
        except Exception as e:
            logger.error(f"{log_prefix}Failed to convert ID to string: {e}")
            return None

    # Trim whitespace
    id_value = id_value.strip()

    if not id_value:
        logger.error(f"{log_prefix}ID value is empty after trimming")
        return None

    # Check if it's a UUID format (with or without braces)
    uuid_pattern = r'^\{?[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\}?$'
    if re.match(uuid_pattern, id_value, re.IGNORECASE):
        # It's a UUID, remove braces if present
        if id_value.startswith('{') and id_value.endswith('}'):
            original_id = id_value
            id_value = id_value[1:-1]
            logger.info(f"{log_prefix}Removed braces from UUID: '{original_id}' -> '{id_value}'")
        logger.info(f"{log_prefix}Validated UUID format ID: {id_value}")
        return id_value

    # Check if it's a valid alphanumeric+hyphen ID
    if re.match(r'^[\w\-]+$', id_value):
        logger.info(f"{log_prefix}Validated alphanumeric ID: {id_value}")
        return id_value

    # Clean up invalid characters
    logger.warning(f"{log_prefix}ID '{id_value}' contains invalid characters, cleaning up")
    original_id = id_value
    id_value = re.sub(r'[^\w\-]', '', id_value)
    logger.info(f"{log_prefix}Cleaned up ID: '{original_id}' -> '{id_value}'")

    return id_value

def is_valid_uuid(id_value):
    """
    Check if an ID is a valid UUID.

    Args:
        id_value: The ID value to check

    Returns:
        bool: True if the ID is a valid UUID, False otherwise
    """
    if not id_value:
        return False

    try:
        # Try to convert to UUID
        uuid.UUID(str(id_value))
        return True
    except (ValueError, AttributeError, TypeError):
        return False

def safe_uuid_conversion(id_value):
    """
    Safely convert an ID to a UUID object.

    Args:
        id_value: The ID value to convert

    Returns:
        uuid.UUID: UUID object, or the original value if conversion fails
    """
    if isinstance(id_value, uuid.UUID):
        return id_value

    if id_value is None:
        return None

    try:
        return uuid.UUID(str(id_value))
    except (ValueError, AttributeError, TypeError):
        logger.warning(f"Invalid UUID format: {id_value}, using original value")
        return id_value


def validate_id_with_context(id_value, context_name, logger_obj=None, error_on_invalid=False):
    """
    Validate an ID with context-specific error handling.

    This function handles the entire validation flow including:
    - Validating and normalizing the ID
    - Logging the results with context
    - Handling invalid IDs according to the error_on_invalid flag

    Args:
        id_value: The ID value to validate
        context_name: A name for the context (used in log messages)
        logger_obj: Optional logger object to use (defaults to module logger)
        error_on_invalid: Whether to raise an error for invalid IDs

    Returns:
        str: Normalized ID value, or None if invalid and error_on_invalid is False

    Raises:
        ValueError: If the ID is invalid and error_on_invalid is True
    """
    # Use the provided logger or fall back to the module logger
    log = logger_obj or logger

    # Use the ID validator to normalize the ID
    normalized_id = validate_and_normalize_id(id_value, log_prefix=f"{context_name}: ")

    if not normalized_id:
        error_msg = f"Invalid ID '{id_value}' in {context_name}"
        log.error(error_msg)

        if error_on_invalid:
            raise ValueError(error_msg)
        return None

    # Update the ID if it was normalized
    if normalized_id != id_value:
        log.info(f"Normalized ID in {context_name}: {id_value} -> {normalized_id}")
    else:
        log.info(f"Using validated ID in {context_name}: {normalized_id}")

    return normalized_id
