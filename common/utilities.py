# Utility function to add to your code
import time
import logging

logger = logging.getLogger(__name__)

def retry_with_backoff(func, max_retries=3, initial_delay=1, backoff_factor=2):
    """Retry a function with exponential backoff"""
    retry_count = 0
    delay = initial_delay
    
    while retry_count < max_retries:
        try:
            return func()
        except Exception as e:
            retry_count += 1
            if retry_count == max_retries:
                raise
                
            logger.warning(f"Retry {retry_count}/{max_retries} failed: {str(e)}. Retrying in {delay}s...")
            time.sleep(delay)
            delay *= backoff_factor
            
    return None