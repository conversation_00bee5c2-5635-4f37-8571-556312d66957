"""
Configuration settings for the longevity platform.
"""
import os
from pydantic_settings import BaseSettings, SettingsConfigDict
from typing import Optional


class Settings(BaseSettings):
    """Main settings for the application."""

    # API Settings
    API_HOST: str = "0.0.0.0"
    API_PORT: int = 8000
    DEBUG: bool = True
    APP_ENV: str = "docker"  # Added APP_ENV
    SECRET_KEY: str = "your_secret_key_here"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # Database Settings (loaded from environment variables or secrets)
    POSTGRES_USER: str = os.getenv("POSTGRES_USER", "longevity")
    POSTGRES_PASSWORD: str = os.getenv("POSTGRES_PASSWORD", "longevitypass")
    POSTGRES_DB: str = os.getenv("POSTGRES_DB", "longevity")
    POSTGRES_HOST: str = "postgres"  # Default to Docker service name
    POSTGRES_PORT: int = 5432       # Default to Docker service port
    DATABASE_URL: Optional[str] = None  # Will be constructed


    # Redis Settings
    REDIS_HOST: str = "redis"    # Default to Docker service name
    REDIS_PORT: int = 6379       # Default to Docker service port
    REDIS_PASSWORD: Optional[str] = None
    REDIS_URL: Optional[str] = None

    # Storage Settings
    MINIO_ROOT_USER: str = "minio"
    MINIO_ROOT_PASSWORD: str = "minio123"
    MINIO_HOST: str = "minio"    # Default to Docker service name
    MINIO_PORT: int = 9000       # Default to Docker service port
    MINIO_SECURE: bool = False
    STORAGE_BUCKET_NAME: str = "longevity-documents"
    STORAGE_URL: Optional[str] = None
    STORAGE_PROVIDER: str = "gcs"

    # Processing Settings
    CHUNK_SIZE: int = 1000
    CHUNK_OVERLAP: int = 200
    EMBEDDING_MODEL: str = "intfloat/e5-large-v2"
    EMBEDDING_DIMENSION: int = 1024
    MAX_DOCUMENT_SIZE_MB: int = 50

    # Vertex AI Settings
    GCS_PROJECT_ID: str = "rosy-rider-453708-c3"
    GOOGLE_APPLICATION_CREDENTIALS: Optional[str] = "gcs_login.json"
    VERTEX_AI_PROJECT_ID: Optional[str] = "rosy-rider-453708-c3"
    VERTEX_AI_LOCATION: str = "europe-west4"
    VERTEX_AI_MODEL_ID: str = "publishers/google/models/gemini-2.0-flash-001"
    VERTEX_AI_BATCH_MODEL_ID: str = "projects/rosy-rider-453708-c3/locations/europe-west4/publishers/google/models/gemini-2.0-flash-001"
    VERTEX_AI_MODEL_NAME: str = "google/gemini-2.0-flash-001"

    # Google Drive Settings
    GOOGLE_DRIVE_FOLDER_ID: Optional[str] = os.getenv("GOOGLE_DRIVE_FOLDER_ID", "11SzDukhfSxRI2JH-tdsltgwo2XfPcLfS")
    GDRIVE_CHECK_INTERVAL_SECONDS: int = 300

    # Google Cloud Storage Settings
    GCS_BUCKET_NAME: str = "longevity-batch-predictions"
    GCS_CREDENTIALS_FILE: str = "gcs_login.json"

    # Neo4j Settings
    NEO4J_HOST: str = "neo4j"    # Default to Docker service name
    NEO4J_PORT: int = 7687       # Default to Docker service port
    NEO4J_USER: Optional[str] = None
    NEO4J_PASSWORD: Optional[str] = None
    NEO4J_URI: Optional[str] = None
    NEO4J_AUTH: Optional[str] = None

    def __init__(self, **kwargs):
        super().__init__(**kwargs) # Loads from .env and environment variables

        # Dynamically adjust host and port settings based on APP_ENV
        if self.APP_ENV in ["local", "test_local"]:
            self.POSTGRES_HOST = "localhost"
            self.POSTGRES_PORT = 5435
            self.REDIS_HOST = "localhost"
            self.REDIS_PORT = 6379  # Standard Redis port
            self.NEO4J_HOST = "localhost"
            self.NEO4J_PORT = 7687  # Standard Neo4j port
            self.MINIO_HOST = "localhost"
            self.MINIO_PORT = 9090
        elif self.APP_ENV in ["docker", "test_docker"]: # Or just an else block for default
            self.POSTGRES_HOST = "postgres"
            self.POSTGRES_PORT = 5432
            self.REDIS_HOST = "redis"
            self.REDIS_PORT = 6379
            self.NEO4J_HOST = "neo4j"
            self.NEO4J_PORT = 7687
            self.MINIO_HOST = "minio"
            self.MINIO_PORT = 9000
        # else:
            # Defaults are already set for "docker" like environments if APP_ENV is something else

        # Parse NEO4J_AUTH if provided
        if self.NEO4J_AUTH:
            try:
                self.NEO4J_USER, self.NEO4J_PASSWORD = self.NEO4J_AUTH.split("/", 1)
            except ValueError:
                raise ValueError("NEO4J_AUTH must be in the format 'username/password'")

        # Build URLs if not provided
        # Build URLs if not provided - this logic now uses potentially modified hosts/ports
        if not self.DATABASE_URL:
            self.DATABASE_URL = f"postgresql://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_HOST}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"

        if not self.REDIS_URL:
            if self.REDIS_PASSWORD:
                # URL encoding the password to handle special characters
                import urllib.parse
                encoded_password = urllib.parse.quote_plus(self.REDIS_PASSWORD)
                self.REDIS_URL = f"redis://:{encoded_password}@{self.REDIS_HOST}:{self.REDIS_PORT}/0"
            else:
                self.REDIS_URL = f"redis://{self.REDIS_HOST}:{self.REDIS_PORT}/0"

        if not self.STORAGE_URL:
            protocol = "https" if self.MINIO_SECURE else "http"
            self.STORAGE_URL = f"{protocol}://{self.MINIO_HOST}:{self.MINIO_PORT}"

        if not self.NEO4J_URI:
            self.NEO4J_URI = f"bolt://{self.NEO4J_HOST}:{self.NEO4J_PORT}"

    model_config = SettingsConfigDict(
        env_file=".env",
        case_sensitive=True
    )


# Create a global settings object
settings = Settings()


def get_settings():
    """Return the settings object."""
    return settings
