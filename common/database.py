"""
Database setup and models for the longevity platform.
"""
import uuid
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any

from sqlalchemy import (
    create_engine, Column, Integer, String, Text, DateTime,
    ForeignKey, Boolean, Float, JSON, Table, func
)
from sqlalchemy.orm import declarative_base, sessionmaker, relationship, Session
from sqlalchemy.dialects.postgresql import <PERSON>SO<PERSON><PERSON>, ARRAY

from common.config import settings

# Use PostgreSQL UUID or SQLAlchemy String for SQLite
if 'sqlite' not in str(settings.DATABASE_URL):
    from sqlalchemy.dialects.postgresql import UUID
else:
    from sqlalchemy import String as UUID

# Import pgvector only if not using SQLite
if 'sqlite' not in str(settings.DATABASE_URL):
    from pgvector.sqlalchemy import Vector
else:
    # Mock Vector for SQLite testing
    from sqlalchemy import ARRAY, Float

    class Vector(ARRAY):
        """Mock Vector class for SQLite testing."""
        def __init__(self, dimensions):
            super().__init__(Float)

# Create SQLAlchemy engine with proper connection pooling
engine = create_engine(
    settings.DATABASE_URL, 
    connect_args={"check_same_thread": False} if 'sqlite' in str(settings.DATABASE_URL) else {},
    # Configure connection pooling to prevent "too many clients already" errors
    pool_size=20,             # Max number of permanent connections
    max_overflow=10,          # Max number of temporary connections
    pool_timeout=30,          # Seconds to wait before timing out
    pool_recycle=1800,        # Recycle connections after 30 minutes
    pool_pre_ping=True        # Enable connection health checks
)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()


# Database Models
class Document(Base):
    """Document model for storing document metadata."""
    __tablename__ = "documents"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    filename = Column(String(255), nullable=False)  # Original filename
    title = Column(String(255), nullable=True)     # Document title if available
    content = Column(Text, nullable=True)
    source_url = Column(String(500), nullable=True)
    document_type = Column(String(100), nullable=False)
    storage_path = Column(String(500), nullable=True)
    doc_metadata = Column(JSONB, nullable=True) if 'sqlite' not in str(settings.DATABASE_URL) else Column(JSON, nullable=True)
    content_hash = Column(String(64), nullable=True)  # SHA-256 hash of document content
    status = Column(String(50), default="pending")
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    # No longer needed as we have a filename column

    @property
    def source_path(self):
        return self.source_url

    # Storage path is now a column, not a property

    @property
    def content_type(self):
        return self.document_type

    @property
    def file_size(self):
        return len(self.content) if self.content else 0

    # Relationships
    chunks = relationship("Chunk", back_populates="document", cascade="all, delete-orphan")
    kg_chunks = relationship("KGChunk", back_populates="document", cascade="all, delete-orphan")

    def to_dict(self):
        """Convert model to dictionary."""
        return {
            "document_id": str(self.id),
            "filename": self.filename,
            "title": self.title,
            "document_type": self.document_type,
            "source_url": self.source_url,
            "status": self.status,
            "content_hash": self.content_hash,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "chunk_count": len(self.chunks) if self.chunks else 0,
            "metadata": self.doc_metadata,
            # For backward compatibility
            "content_type": self.document_type,
            "file_size": len(self.content) if self.content else 0,
        }


class Chunk(Base):
    """Chunk model for storing document chunks and embeddings for RAG."""
    __tablename__ = "chunks"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False)
    chunk_index = Column(Integer, nullable=False)
    text = Column(Text, nullable=False)
    chunk_metadata = Column(JSONB, nullable=True) if 'sqlite' not in str(settings.DATABASE_URL) else Column(JSON, nullable=True)
    embedding = Column(Vector(settings.EMBEDDING_DIMENSION), nullable=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    # Relationships
    document = relationship("Document", back_populates="chunks")

    def to_dict(self):
        """Convert model to dictionary."""
        return {
            "id": str(self.id),
            "document_id": str(self.document_id),
            "chunk_index": self.chunk_index,
            "text": self.text,
            "metadata": self.chunk_metadata,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }


class KGChunk(Base):
    """Chunk model for storing document chunks and embeddings for Knowledge Graph."""
    __tablename__ = "kg_chunks"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False)
    chunk_index = Column(Integer, nullable=False)
    text = Column(Text, nullable=False)
    chunk_metadata = Column(JSONB, nullable=True) if 'sqlite' not in str(settings.DATABASE_URL) else Column(JSON, nullable=True)
    embedding = Column(Vector(settings.EMBEDDING_DIMENSION), nullable=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    # Relationships
    document = relationship("Document", back_populates="kg_chunks")

    def to_dict(self):
        """Convert model to dictionary."""
        return {
            "id": str(self.id),
            "document_id": str(self.document_id),
            "chunk_index": self.chunk_index,
            "text": self.text,
            "metadata": self.chunk_metadata,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }


class ProcessingTask(Base):
    """Task model for tracking document processing tasks."""
    __tablename__ = "processing_tasks"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False)
    celery_task_id = Column(String(255), nullable=True)
    task_type = Column(String(100), nullable=False)
    status = Column(String(50), default="pending")
    result = Column(JSONB, nullable=True) if 'sqlite' not in str(settings.DATABASE_URL) else Column(JSON, nullable=True)
    error = Column(Text, nullable=True)
    task_metadata = Column(JSONB, nullable=True) if 'sqlite' not in str(settings.DATABASE_URL) else Column(JSON, nullable=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    updated_at = Column(DateTime, default=lambda: datetime.now(timezone.utc), onupdate=lambda: datetime.now(timezone.utc))

    def to_dict(self):
        """Convert model to dictionary."""
        return {
            "id": str(self.id),
            "document_id": str(self.document_id),
            "celery_task_id": self.celery_task_id,
            "task_type": self.task_type,
            "status": self.status,
            "result": self.result,
            "error": self.error,
            "metadata": self.task_metadata,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
        }


class Entity(Base):
    """Entity model for storing extracted entities."""
    __tablename__ = "entities"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False)
    chunk_id = Column(UUID(as_uuid=True), ForeignKey("chunks.id"), nullable=True)
    kg_chunk_id = Column(UUID(as_uuid=True), ForeignKey("kg_chunks.id"), nullable=True)
    text = Column(String(500), nullable=False)
    entity_type = Column(String(100), nullable=False)
    normalized_id = Column(String(255), nullable=True)
    start_pos = Column(Integer, nullable=True)
    end_pos = Column(Integer, nullable=True)
    confidence = Column(Float, nullable=True)
    entity_metadata = Column(JSONB, nullable=True) if 'sqlite' not in str(settings.DATABASE_URL) else Column(JSON, nullable=True)
    embedding = Column(Vector(settings.EMBEDDING_DIMENSION), nullable=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    # Relationships
    document = relationship("Document")
    chunk = relationship("Chunk")
    kg_chunk = relationship("KGChunk")
    source_relationships = relationship("Relationship", foreign_keys="Relationship.source_id", back_populates="source", cascade="all, delete-orphan")
    target_relationships = relationship("Relationship", foreign_keys="Relationship.target_id", back_populates="target", cascade="all, delete-orphan")

    def to_dict(self):
        """Convert model to dictionary."""
        return {
            "id": str(self.id),
            "document_id": str(self.document_id),
            "chunk_id": str(self.chunk_id) if self.chunk_id else None,
            "kg_chunk_id": str(self.kg_chunk_id) if self.kg_chunk_id else None,
            "text": self.text,
            "entity_type": self.entity_type,
            "normalized_id": self.normalized_id,
            "start_pos": self.start_pos,
            "end_pos": self.end_pos,
            "confidence": self.confidence,
            "metadata": self.entity_metadata,
            "embedding": self.embedding.tolist() if self.embedding is not None else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }


class Relationship(Base):
    """Relationship model for storing entity relationships."""
    __tablename__ = "relationships"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=False)
    chunk_id = Column(UUID(as_uuid=True), ForeignKey("chunks.id"), nullable=True)
    kg_chunk_id = Column(UUID(as_uuid=True), ForeignKey("kg_chunks.id"), nullable=True)
    source_id = Column(UUID(as_uuid=True), ForeignKey("entities.id"), nullable=False)
    target_id = Column(UUID(as_uuid=True), ForeignKey("entities.id"), nullable=False)
    relationship_type = Column(String(100), nullable=False)
    confidence = Column(Float, nullable=True)
    evidence_text = Column(Text, nullable=True)
    relationship_metadata = Column(JSONB, nullable=True) if 'sqlite' not in str(settings.DATABASE_URL) else Column(JSON, nullable=True)
    created_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))

    # Relationships
    document = relationship("Document")
    chunk = relationship("Chunk")
    kg_chunk = relationship("KGChunk")
    source = relationship("Entity", foreign_keys=[source_id], back_populates="source_relationships")
    target = relationship("Entity", foreign_keys=[target_id], back_populates="target_relationships")

    def to_dict(self):
        """Convert model to dictionary."""
        return {
            "id": str(self.id),
            "document_id": str(self.document_id),
            "chunk_id": str(self.chunk_id) if self.chunk_id else None,
            "kg_chunk_id": str(self.kg_chunk_id) if self.kg_chunk_id else None,
            "source_id": str(self.source_id),
            "target_id": str(self.target_id),
            "relationship_type": self.relationship_type,
            "confidence": self.confidence,
            "evidence_text": self.evidence_text,
            "metadata": self.relationship_metadata,
            "created_at": self.created_at.isoformat() if self.created_at else None,
        }


class GoogleDriveProcessedFile(Base):
    """Model for tracking processed Google Drive files."""
    __tablename__ = "gdrive_processed_files"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    file_id = Column(String(255), nullable=False, unique=True, index=True)
    file_name = Column(String(500), nullable=True)
    mime_type = Column(String(255), nullable=True)
    md5_checksum = Column(String(255), nullable=True)
    folder_id = Column(String(255), nullable=True)
    processed_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    document_id = Column(UUID(as_uuid=True), ForeignKey("documents.id"), nullable=True)
    status = Column(String(50), default="processed")
    result = Column(JSONB, nullable=True) if 'sqlite' not in str(settings.DATABASE_URL) else Column(JSON, nullable=True)

    # Relationship to document if applicable
    document = relationship("Document")

    def to_dict(self):
        """Convert model to dictionary."""
        return {
            "id": str(self.id),
            "file_id": self.file_id,
            "file_name": self.file_name,
            "mime_type": self.mime_type,
            "md5_checksum": self.md5_checksum,
            "folder_id": self.folder_id,
            "processed_at": self.processed_at.isoformat() if self.processed_at else None,
            "document_id": str(self.document_id) if self.document_id else None,
            "status": self.status,
            "result": self.result,
        }


# Database utility functions
def get_db():
    """Get database session."""
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


def init_db():
    """Initialize database with tables."""
    # First create a session to create the pgvector extension
    db_session = SessionLocal()
    try:
        # Create the extension first
        create_pgvector_extension(db_session)
        # Now create tables
        Base.metadata.create_all(bind=engine)
    finally:
        db_session.close()


def create_pgvector_extension(db: Session):
    """
    Create pgvector extension if it doesn't exist.
    
    IMPORTANT: If EMBEDDING_DIMENSION in config.py is changed after the database tables are created,
    you must run scripts/update_vector_schema.py to update the vector columns in existing tables.
    The database table schema won't automatically update when the EMBEDDING_DIMENSION setting changes.
    """
    # Skip for SQLite
    if 'sqlite' in str(settings.DATABASE_URL):
        return

    from sqlalchemy import text
    db.execute(text('CREATE EXTENSION IF NOT EXISTS vector'))
    db.commit()
