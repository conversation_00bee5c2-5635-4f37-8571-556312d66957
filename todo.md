** NEXT STEPS
1- fix testing, make every test works and delete unnecassery ones
2- develop context retrival for llm.
3- check prompts 
4- integrate pubmed ingester.
5- use vertexai llm realtime with context to retreieve information

** Optional
1- Add pubmed bert entity extracter and ner model for relationships to be used in RAG


We are going to fix tests, can you check implemented tests there should be outdated ones, delete them.
Each test should be implemented under tests
We should test with real like data, which are under tests/data
1- unit tests can use mocks
2- integration tests should not use mocks, you need to spin up docker-compose files you can have different docker-compose.test.<service>.yml for each service, if the service that you are testing in local host remember to update connection parameter to docker system,
3- system test all services shulld run on docker and you can only call api and monitor the results.
