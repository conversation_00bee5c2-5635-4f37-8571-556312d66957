## Rules for creating tests
1- use pytest
2- use real data, there is data un under tests/data, if you need ask me to provide more test data
3- only unit tests will use mock
4- integration tests will use real services, only for testing batch flow can use sample vertext_ai_batch_response.jsonl
5- build test service by service test and contine
6- when tests fail check also code if it needs fixing.
7- when mockin services for check the code of the service to be mocked, and add input constraints accordingly, do not accept all inputs, if a field cannot be null and the code send null, test needs fail and you need to check the code.
one thing to consider create a 2 seperate test configs
1- one for localhost testing, remember that it is out of docker env. so instead of service name you need to exposeed portals and localhost on the docer-compose file.
2- another config for test run in docker environment or cloud
3-you cn use same credentials with producrion env.

## Rules for fixing the code
1- Do not only add new code, analyze the code constantly and remove code as needed.
2- Try to write readable code
3- when there is long text input or a very specific parse logic, create those functions on seperate files
4- When editted a function run the unit test for that function 
5- When you add a new function
6- If you need to create helper scripts to asses the data, create them in under ai/scripts folder

## System Test
Now lets run a system test, system test to ingest documents. It means you need to use api ingest document and also monitorif the document is ingested without any other script etc. check if workers beat working and gets the completed vertex ai jobs
1- docker compose up, build
2- remove all the old data in databases,
3- start ingesting, ingestion process should be fully automated after calling the ingest endpoint, batch processing should bw used. test only need to call ingest api endpoint with sample files:
     -query to ingest pubmed article ingesting 2 articlers is enaough search query "longevity diet"
     -sample_txt.txt, 
     -sample_epub.epub, 
     -sample_pdf.pdf, 
     -sample_url.txt in test/data folder and note sample_url has the url to fetch. 
4- check rag chunks are create, kg_chunks are created
5- if entities extracted from kg_chunks, normalized and insterted in postgres
6- check if neo4j contains:
     - entities, entity-entity and entity-chunk relations, 
     - chunk nodes and relations, 
     - document node, chunk and entites


## TODO:
1- Make batch processing work
2- check continue batch processing, multiple status is not necessary to check   
2- Check batch processing with parallel


I would like you to check pubmed workflow and if it does as it should. 
1- when ingest pubmed document, the document should be added to rag with regular chunking,
2- we need to add its abstract to. kg_chunks as single chunk no chunking
3- Each chunk should have embedding
4- Then knowledge building tests should start
5- Vertex-ai job should created
6- Vertex ai job result should be fetched and entities should be on neo4j
7- you can purge the database i necessary 


## TODO:
1- use global field names class to standarize keys.
2- send figures to gcs from unstructured.