import os
# import socket # No longer needed

# is_running_in_docker() function removed as it's available in tests.config.test_config_selector
# get_storage_host() function removed as host selection is now handled by APP_ENV in common.config.Settings

def configure_test_environment():
    """Configure environment variables for testing."""
    if os.environ.get('TESTING', '').lower() == 'true':
        # MINIO_HOST and MINIO_ENDPOINT are now determined by APP_ENV in the main Settings class.
        
        # Set test-specific MinIO credentials and bucket name
        os.environ['MINIO_ACCESS_KEY'] = os.getenv('MINIO_ACCESS_KEY', 'minio')
        os.environ['MINIO_SECRET_KEY'] = os.getenv('MINIO_SECRET_KEY', 'minio123')
        os.environ['MINIO_BUCKET_NAME'] = os.getenv('MINIO_BUCKET_NAME', 'test-longevity-documents') # Important for tests

        # Determine and set TEST_DATA_DIR
        # This path is used by get_test_data_path in test_config_selector
        app_env = os.environ.get('APP_ENV', 'test_docker') # Default to test_docker if somehow not set
        if app_env == 'test_local':
            # For local tests, construct path relative to project root (assuming this file is in config/)
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            test_data_dir = os.path.join(project_root, "tests", "data")
        else: # For test_docker or any other case
            test_data_dir = "/app/tests/data" # Path inside Docker container
        os.environ['TEST_DATA_DIR'] = test_data_dir

        # Set path for Vertex AI batch sample response, used in tests
        # This was previously part of the specific test settings classes.
        vertex_ai_sample_file = "vertex_ai_batch_response.jsonl"
        os.environ['VERTEX_AI_BATCH_SAMPLE_RESPONSE'] = os.path.join(test_data_dir, vertex_ai_sample_file)
        
        # Other test-specific configurations can be set here as environment variables
        # For example, if Settings class was extended to pick up more test-specific vars.
