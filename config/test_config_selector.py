"""
Test configuration selector for automatically choosing the appropriate test settings.
This module detects the environment (Docker or localhost) and selects the right configuration.
"""
import os
import socket
from typing import Union

from config.test_config_localhost import LocalhostTestSettings, get_localhost_test_settings
from config.test_config_docker import DockerTestSettings, get_docker_test_settings


def is_running_in_docker():
    """Check if the application is running inside a Docker container."""
    try:
        with open('/proc/1/cgroup', 'rt') as f:
            return 'docker' in f.read()
    except Exception:
        # If the file doesn't exist or can't be read, we're likely not in Docker
        return False


def get_test_settings() -> Union[LocalhostTestSettings, DockerTestSettings]:
    """
    Automatically select the appropriate test settings based on the environment.
    
    Returns:
        Either LocalhostTestSettings or DockerTestSettings based on the detected environment.
    """
    # Allow manual override through environment variable
    env_override = os.environ.get('TEST_ENVIRONMENT', '').lower()
    
    if env_override == 'docker':
        return get_docker_test_settings()
    elif env_override == 'localhost':
        return get_localhost_test_settings()
    
    # Auto-detect environment if no override specified
    if is_running_in_docker() or os.environ.get('FORCE_DOCKER_HOSTS', '').lower() == 'true':
        return get_docker_test_settings()
    else:
        return get_localhost_test_settings()


# Convenience function to get test data directory
def get_test_data_path(filename: str = '') -> str:
    """
    Get the full path to a test data file or directory.
    
    Args:
        filename: Optional filename within the test data directory
        
    Returns:
        Full path to the test data file or directory
    """
    settings = get_test_settings()
    if filename:
        return os.path.join(settings.TEST_DATA_DIR, filename)
    return settings.TEST_DATA_DIR