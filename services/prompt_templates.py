"""
Prompt templates for Vertex AI LLM interactions.
"""
from typing import List, Dict, Any, Optional


class PromptTemplates:
    """
    Unified prompt templates for Vertex AI LLM interactions.
    Centralizes all prompts used in the system for consistency and easy updates.
    """

    @staticmethod
    def entity_extraction_prompt(chunk_text: str, chunk_id: str = None) -> str:
        """
        Generate a prompt for entity extraction.

        Args:
            chunk_text: Text chunk to analyze
            chunk_id: Optional chunk ID to include in the output

        Returns:
            str: Formatted prompt for entity extraction
        """
        chunk_id_field = f'"chunk_id": "{chunk_id}",\n                ' if chunk_id else ''

        return f"""Extract all longevity-related entities and their relationships from the text.

                Entity categories: diseases, supplements, trackers, biomarkers, interventions, lifestyle factors, genetic factors, organizations/people

                For each entity: identify name, category, and significance in longevity

                For relationships between entities: identify Entity1 → relationship type → Entity2
                (relationship types: measures, treats/prevents, causes, correlates with, increases/decreases, used for, studied by)

                Output as JSON:
                {{
                {chunk_id_field}"entities": [{{
                    "text": "",
                    "type": "",
                    "significance": ""
                }}],
                "relationships": [{{
                    "source_text": "",
                    "type": "",
                    "target_text": "",
                    "evidence_text": ""
                }}]
                }}

                Text to analyze: {chunk_text}"""

    @staticmethod
    def relationship_extraction_prompt(chunk_text: str, entities: List[Dict[str, Any]]) -> str:
        """
        Generate a prompt for relationship extraction.

        Args:
            chunk_text: Text chunk to analyze
            entities: List of entities extracted from the text

        Returns:
            str: Formatted prompt for relationship extraction
        """
        import json

        return f"""
        Analyze the following text and extract relationships between the entities listed below.

        Text: {chunk_text}

        Entities:
        {json.dumps(entities, indent=2)}

        Return the results as a JSON array of objects with the following structure:
        [
            {{
                "source_text": "text of source entity",
                "target_text": "text of target entity",
                "type": "type of relationship",
                "evidence_text": "text evidence for this relationship"
            }}
        ]

        Only return the JSON array, no other text.
        """

    @staticmethod
    def entity_normalization_prompt(entities: List[Dict[str, Any]]) -> str:
        """
        Generate a prompt for entity normalization.

        Args:
            entities: List of entities to normalize

        Returns:
            str: Formatted prompt for entity normalization
        """
        import json

        return f"""
        Normalize the following biomedical and health-related entities.
        For each entity, provide a standardized form and identify duplicates or very similar entities.

        Entities:
        {json.dumps(entities, indent=2)}

        Return the results as a JSON array of objects with the following structure:
        [
            {{
                "original_index": index in the input array,
                "text": "original entity text",
                "normalized_text": "standardized form of the entity",
                "type": "entity type",
                "similar_to": [indices of similar entities in the input array or null if none]
            }}
        ]

        Only return the JSON array, no other text.
        """

    @staticmethod
    def chunk_relationship_prompt(chunks: List[Dict[str, Any]]) -> str:
        """
        Generate a prompt for determining relationships between chunks.

        Args:
            chunks: List of text chunks

        Returns:
            str: Formatted prompt for chunk relationship extraction
        """
        # Create a simplified representation of chunks for the prompt
        chunk_summaries = []
        for i, chunk in enumerate(chunks):
            # Limit text to first 100 characters for the summary
            text_preview = chunk.get("text", "")[:100] + "..." if len(chunk.get("text", "")) > 100 else chunk.get("text", "")
            chunk_summaries.append(f"Chunk {i}: {text_preview}")

        chunk_text = "\n".join(chunk_summaries)

        return f"""
        Analyze the following text chunks and determine the relationships between them.
        Consider sequential relationships, thematic relationships, and logical connections.

        Chunks:
        {chunk_text}

        Return the results as a JSON array of objects with the following structure:
        [
            {{
                "source": index of source chunk,
                "target": index of target chunk,
                "relationship_type": "NEXT" for sequential or "RELATED" for thematic,
                "strength": a number between 0 and 1 indicating relationship strength
            }}
        ]

        Only return the JSON array, no other text.
        """
