"""
Entity Orchestrator service for coordinating entity extraction, normalization, and storage.
"""
import logging
import uuid
from typing import Dict, Any, List, Optional, Union, Tuple
import numpy as np

from transport.data_transport import DataTransport
from services.nlp_service import NLPService
from transport.vertex_ai_batch_client import VertexAIBatchClient
from common.database import Entity, Relationship

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EntityOrchestrator:
    """
    Orchestrator for entity extraction, normalization, and relationship building.
    Coordinates between PostgreSQL and Neo4j databases.
    """

    @staticmethod
    def process_document(document_id: Union[str, uuid.UUID], processing_task_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
        """
        Process a document to extract entities, normalize them, and build relationships.

        Args:
            document_id: Document ID
            processing_task_id: Processing task ID

        Returns:
            Dict[str, Any]: Processing result
        """
        logger.info(f"Processing document for entity extraction: {document_id}")

        try:
            with DataTransport() as transport:
                # Update task status
                transport.update_task_status(
                    task_id=processing_task_id,
                    status="processing"
                )

                # Get document chunks
                chunks = transport.get_document_chunks(document_id)
                if not chunks:
                    logger.warning(f"No chunks found for document {document_id}")
                    transport.update_task_status(
                        task_id=processing_task_id,
                        status="completed",
                        result={"message": "No chunks found for document"}
                    )
                    return {"status": "success", "message": "No chunks found for document"}

                # Process chunks to extract and normalize entities
                result = EntityOrchestrator._process_chunks(document_id, chunks, transport)

                # Update task status
                transport.update_task_status(
                    task_id=processing_task_id,
                    status="completed",
                    result=result
                )

                return result

        except Exception as e:
            logger.error(f"Error processing document {document_id}: {e}")

            # Update task status
            try:
                with DataTransport() as transport:
                    transport.update_task_status(
                        task_id=processing_task_id,
                        status="failed",
                        error=str(e)
                    )
            except Exception as update_error:
                logger.error(f"Error updating task status: {update_error}")

            raise

    @staticmethod
    def _process_chunks(document_id: Union[str, uuid.UUID], chunks: List[Dict[str, Any]], transport: DataTransport) -> Dict[str, Any]:
        """
        Process chunks to extract entities, normalize them, and build relationships.

        Args:
            document_id: Document ID
            chunks: List of document chunks
            transport: DataTransport instance

        Returns:
            Dict[str, Any]: Processing result
        """
        # Initialize Vertex AI Batch client for entity extraction
        vertex_client = VertexAIBatchClient()

        # Process chunks with Vertex AI
        logger.info(f"Processing {len(chunks)} chunks with Vertex AI for document {document_id}")

        # Always use batch processing
        logger.info(f"Using batch processing for document {document_id} with {len(chunks)} chunks")

        # Start batch processing job
        batch_job_result = vertex_client._batch_process_with_batch_api(chunks)

        # Check if batch job was started successfully
        if not batch_job_result.get("job"):
            logger.error(f"Failed to start batch job for document {document_id}")
            return {
                "status": "error",
                "document_id": str(document_id),
                "message": "Failed to start batch processing job"
            }

        # Get the batch job
        batch_job = batch_job_result.get("job")
        batch_job_id = batch_job_result.get("batch_job_id")

        # Wait for batch job to complete
        try:
            logger.info(f"Waiting for batch job {batch_job_id} to complete for document {document_id}")
            vertex_client.wait_for_batch_job(batch_job, timeout=3600)  # Wait up to 1 hour
        except Exception as e:
            logger.error(f"Error waiting for batch job {batch_job_id}: {e}")
            return {
                "status": "error",
                "document_id": str(document_id),
                "message": f"Error waiting for batch job: {str(e)}"
            }

        # Get batch job results
        try:
            logger.info(f"Retrieving results for batch job {batch_job_id}")
            extraction_results = vertex_client.get_batch_prediction_results(batch_job_id)
        except Exception as e:
            logger.error(f"Error retrieving batch job results for {batch_job_id}: {e}")
            return {
                "status": "error",
                "document_id": str(document_id),
                "message": f"Error retrieving batch job results: {str(e)}"
            }

        # Collect all entities and relationships
        all_entities = []
        all_relationships = []
        chunk_texts = {}

        for result in extraction_results:
            chunk_id = result.get("chunk_id")
            if not chunk_id:
                continue

            # Find the chunk text
            chunk = next((c for c in chunks if c["id"] == chunk_id), None)
            if not chunk:
                continue

            chunk_texts[chunk_id] = chunk["text"]

            # Process entities
            entities = result.get("entities", [])
            for entity in entities:
                # Add document and chunk references
                entity["document_id"] = str(document_id)
                entity["chunk_id"] = chunk_id
                entity["id"] = str(uuid.uuid4())  # Generate unique ID for entity

                # Ensure entity has required fields
                if "type" not in entity and "entity_type" in entity:
                    entity["type"] = entity["entity_type"]
                elif "type" not in entity:
                    entity["type"] = "unknown"

                all_entities.append(entity)

            # Process relationships
            relationships = result.get("relationships", [])

            # Create a mapping of entity indices to entity IDs for this chunk
            entity_idx_to_id = {}
            for idx, entity in enumerate(entities):
                entity_idx_to_id[idx] = entity.get("id")

            for relationship in relationships:
                # Convert source/target indices to entity references
                source_idx = relationship.get("source")
                target_idx = relationship.get("target")

                if source_idx is not None and target_idx is not None:
                    source_id = entity_idx_to_id.get(source_idx)
                    target_id = entity_idx_to_id.get(target_idx)

                    if source_id and target_id:
                        # Create relationship with entity IDs
                        rel = {
                            "source_id": source_id,
                            "target_id": target_id,
                            "relationship_type": relationship.get("relationship_type", "RELATED_TO"),
                            "evidence_text": relationship.get("evidence_text", ""),
                            "chunk_id": chunk_id,
                            "document_id": str(document_id)
                        }
                        all_relationships.append(rel)

        if not all_entities:
            logger.info(f"No entities found in document {document_id}")
            return {
                "status": "success",
                "document_id": str(document_id),
                "entities_count": 0,
                "relationships_count": 0,
                "message": "No entities found in document"
            }

        # Normalize entities
        normalized_entities = EntityOrchestrator._normalize_entities(all_entities, transport)

        # Store normalized entities in PostgreSQL
        EntityOrchestrator._store_entities_in_postgres(document_id, normalized_entities, transport)

        # Store entities in Neo4j
        EntityOrchestrator._store_entities_in_neo4j(normalized_entities, transport)

        # Use relationships extracted by Vertex AI
        # No need to call NLPService.extract_relationships again

        # Store chunks in Neo4j and create chunk-to-chunk relationships
        EntityOrchestrator._store_chunks_in_neo4j(document_id, chunks, transport)

        # Store relationships in Neo4j
        relationship_count = EntityOrchestrator._store_relationships(document_id, all_relationships, normalized_entities, transport)

        return {
            "status": "success",
            "document_id": str(document_id),
            "entities_count": len(normalized_entities),
            "relationships_count": relationship_count
        }

    @staticmethod
    def _normalize_entities(entities: List[Dict[str, Any]], transport: DataTransport = None) -> List[Dict[str, Any]]:
        """
        Normalize entities and identify duplicates using vector similarity.

        Args:
            entities: List of extracted entities
            transport: Optional DataTransport instance

        Returns:
            List[Dict[str, Any]]: List of normalized entities with embeddings
        """
        if not entities:
            return []

        # Create a transport instance if not provided
        close_transport = False
        if transport is None:
            transport = DataTransport()
            close_transport = True

        try:
            # Generate embeddings for all entities
            entity_texts = [entity["text"] for entity in entities]
            embeddings = NLPService.generate_embeddings(entity_texts)

            # Add embeddings to entities
            for i, entity in enumerate(entities):
                entity["embedding"] = embeddings[i]

            # Find similar existing entities in the database
            normalized_entities = []
            duplicate_ids = set()

            for i, entity in enumerate(entities):
                # Skip if already marked as duplicate
                if entity["id"] in duplicate_ids:
                    continue

                # Find similar entities in the database
                similar_entities = transport.find_similar_entities(
                    entity["text"],
                    entity["embedding"],
                    entity_type=entity["type"],
                    threshold=0.92,  # High threshold for similarity
                    limit=5
                )

                if similar_entities:
                    # Found similar entity in database, use it as the normalized form
                    similar_entity = similar_entities[0]  # Most similar entity
                    entity["normalized_id"] = similar_entity["id"]
                    entity["normalized_text"] = similar_entity["text"]
                    entity["is_duplicate"] = True
                    entity["duplicate_of"] = similar_entity["id"]
                else:
                    # No similar entity found, this is a new entity
                    entity["normalized_id"] = entity["id"]
                    entity["normalized_text"] = entity["text"]
                    entity["is_duplicate"] = False
                    entity["duplicate_of"] = None

                normalized_entities.append(entity)

            # Now check for duplicates within the current batch
            for i, entity1 in enumerate(normalized_entities):
                if entity1["is_duplicate"]:
                    continue  # Skip if already a duplicate

                for j, entity2 in enumerate(normalized_entities[i+1:], i+1):
                    if entity2["is_duplicate"]:
                        continue  # Skip if already a duplicate

                    # Calculate similarity between embeddings
                    similarity = EntityOrchestrator._calculate_similarity(
                        entity1["embedding"],
                        entity2["embedding"]
                    )

                    if similarity > 0.92 and entity1["type"] == entity2["type"]:
                        # Mark as duplicate
                        entity2["normalized_id"] = entity1["id"]
                        entity2["normalized_text"] = entity1["text"]
                        entity2["is_duplicate"] = True
                        entity2["duplicate_of"] = entity1["id"]
                        duplicate_ids.add(entity2["id"])

            return normalized_entities

        finally:
            # Close transport if we created it
            if close_transport and transport:
                transport.close()

    @staticmethod
    def _calculate_similarity(embedding1: np.ndarray, embedding2: np.ndarray) -> float:
        """
        Calculate cosine similarity between two embeddings.

        Args:
            embedding1: First embedding vector
            embedding2: Second embedding vector

        Returns:
            float: Cosine similarity (0-1)
        """
        # Ensure embeddings are numpy arrays
        if not isinstance(embedding1, np.ndarray):
            embedding1 = np.array(embedding1)
        if not isinstance(embedding2, np.ndarray):
            embedding2 = np.array(embedding2)

        # Normalize vectors
        norm1 = np.linalg.norm(embedding1)
        norm2 = np.linalg.norm(embedding2)

        if norm1 == 0 or norm2 == 0:
            return 0.0

        # Calculate cosine similarity
        return np.dot(embedding1, embedding2) / (norm1 * norm2)

    @staticmethod
    def _store_entities_in_postgres(document_id: Union[str, uuid.UUID], entities: List[Dict[str, Any]], transport: DataTransport) -> List[Entity]:
        """
        Store normalized entities in PostgreSQL.

        Args:
            document_id: Document ID
            entities: List of normalized entities
            transport: DataTransport instance

        Returns:
            List[Entity]: List of stored entity records
        """
        # Use the database client to store entities
        db_client = transport.db_client
        stored_entities = []

        for entity in entities:
            # Create entity record
            entity_record = Entity(
                id=uuid.UUID(entity["id"]) if isinstance(entity["id"], str) else entity["id"],
                document_id=uuid.UUID(document_id) if isinstance(document_id, str) else document_id,
                chunk_id=uuid.UUID(entity["chunk_id"]) if entity.get("chunk_id") else None,
                text=entity["text"],
                entity_type=entity["type"],
                normalized_id=entity.get("normalized_id"),
                start_pos=entity.get("start", 0),
                end_pos=entity.get("end", 0),
                confidence=entity.get("score", 1.0),
                entity_metadata={
                    "variants": entity.get("variants", []),
                    "embedding": entity["embedding"].tolist() if isinstance(entity.get("embedding"), np.ndarray) else entity.get("embedding")
                }
            )

            # Add to session
            db_client.db.add(entity_record)
            stored_entities.append(entity_record)

        # Commit changes
        db_client.db.commit()

        # Refresh entities to get generated IDs
        for entity in stored_entities:
            db_client.db.refresh(entity)

        logger.info(f"Stored {len(stored_entities)} entities in PostgreSQL for document {document_id}")
        return stored_entities

    @staticmethod
    def _store_chunks_in_neo4j(document_id: Union[str, uuid.UUID], chunks: List[Dict[str, Any]], transport: DataTransport) -> Dict[str, Any]:
        """
        Store chunks in Neo4j and create relationships between them.

        Args:
            document_id: Document ID
            chunks: List of document chunks
            transport: DataTransport instance

        Returns:
            Dict[str, Any]: Operation result
        """
        # First store document if it doesn't exist
        document_result = transport.neo4j_client.store_document(
            str(document_id),
            {"filename": f"document-{document_id}", "content_type": "text/plain"}
        )

        # Store each chunk
        chunk_results = []
        neo4j_chunks = []

        for i, chunk in enumerate(chunks):
            chunk_id = chunk["id"]

            # Prepare chunk data
            chunk_data = {
                "text": chunk["text"],
                "chunk_index": i,
                "created_at": chunk.get("created_at", ""),
                # Store embedding if available
                "embedding": chunk.get("embedding", None)
            }

            # Store chunk in Neo4j
            result = transport.neo4j_client.store_chunk(str(chunk_id), str(document_id), chunk_data)
            chunk_results.append(result)

            # Add to list for relationship creation
            neo4j_chunks.append({
                "id": str(chunk_id),
                "chunk_index": i
            })

        # Create relationships between chunks
        relationship_result = transport.neo4j_client.create_chunk_relationships(str(document_id), neo4j_chunks)

        logger.info(f"Stored {len(chunks)} chunks in Neo4j and created chunk relationships")
        return {
            "document_id": str(document_id),
            "chunks_stored": len(chunks),
            "relationships": relationship_result.get("sequential_relationships", 0),
            "status": "success"
        }

    @staticmethod
    def _store_entities_in_neo4j(entities: List[Dict[str, Any]], transport: DataTransport) -> List[Dict[str, Any]]:
        """
        Store normalized entities in Neo4j.

        Args:
            entities: List of normalized entities
            transport: DataTransport instance

        Returns:
            List[Dict[str, Any]]: List of Neo4j entity results
        """
        # Group entities by chunk for efficient storage
        entities_by_chunk = {}
        for entity in entities:
            chunk_id = entity.get("chunk_id")
            if not chunk_id:
                continue

            if chunk_id not in entities_by_chunk:
                entities_by_chunk[chunk_id] = []

            # Create Neo4j entity object
            neo4j_entity = {
                "id": entity["id"],
                "text": entity["text"],
                "type": entity["type"],
                "normalized_id": entity.get("normalized_id"),
                "normalized_text": entity.get("normalized_text", entity["text"]),
                "start": entity.get("start", 0),
                "end": entity.get("end", 0),
                "is_duplicate": entity.get("is_duplicate", False)
            }

            entities_by_chunk[chunk_id].append(neo4j_entity)

        # Store entities in Neo4j by chunk
        results = []
        for chunk_id, chunk_entities in entities_by_chunk.items():
            result = transport.neo4j_client.store_entities(chunk_id, chunk_entities)
            results.append(result)

        logger.info(f"Stored entities in Neo4j for {len(entities_by_chunk)} chunks")
        return results

    @staticmethod
    def _store_relationships(document_id: Union[str, uuid.UUID], relationships: List[Dict[str, Any]],
                            entities: List[Dict[str, Any]], transport: DataTransport) -> int:
        """
        Store relationships in Neo4j.

        Args:
            document_id: Document ID
            relationships: List of relationships
            entities: List of normalized entities
            transport: DataTransport instance

        Returns:
            int: Number of stored relationships
        """
        if not relationships:
            logger.info("No relationships to store")
            return 0

        # Create a mapping of entity IDs for quick lookup
        entity_map = {entity["id"]: entity for entity in entities}

        # Group relationships by chunk for efficient storage
        relationships_by_chunk = {}
        for relationship in relationships:
            source_id = relationship.get("source_id")
            target_id = relationship.get("target_id")

            if not source_id or not target_id:
                logger.warning(f"Missing source_id or target_id in relationship: {relationship}")
                continue

            source_entity = entity_map.get(source_id)
            target_entity = entity_map.get(target_id)

            if not source_entity or not target_entity:
                logger.warning(f"Source or target entity not found: {source_id} -> {target_id}")
                continue

            chunk_id = source_entity.get("chunk_id")
            if not chunk_id:
                logger.warning(f"No chunk_id for entity {source_id}")
                continue

            if chunk_id not in relationships_by_chunk:
                relationships_by_chunk[chunk_id] = []

            # Create Neo4j relationship object
            # Standardize the relationship type field name
            relationship_type = relationship.get("relationship_type", relationship.get("type", "RELATED_TO"))
            
            neo4j_relationship = {
                "id": str(uuid.uuid4()),
                "source_id": source_id,
                "target_id": target_id,
                "relationship_type": relationship_type,
                "type": relationship_type,  # Add both fields for compatibility
                "evidence_text": relationship.get("evidence_text", ""),
                "confidence": relationship.get("confidence", 1.0)
            }

            relationships_by_chunk[chunk_id].append(neo4j_relationship)

            # Also store in PostgreSQL
            relationship_record = Relationship(
                id=uuid.UUID(neo4j_relationship["id"]),
                document_id=uuid.UUID(document_id) if isinstance(document_id, str) else document_id,
                chunk_id=uuid.UUID(chunk_id) if isinstance(chunk_id, str) else chunk_id,
                source_id=uuid.UUID(source_id) if isinstance(source_id, str) else source_id,
                target_id=uuid.UUID(target_id) if isinstance(target_id, str) else target_id,
                relationship_type=neo4j_relationship["relationship_type"],
                confidence=neo4j_relationship["confidence"],
                evidence_text=neo4j_relationship["evidence_text"]
            )

            transport.db_client.db.add(relationship_record)

        # Commit PostgreSQL changes
        transport.db_client.db.commit()

        # Store relationships in Neo4j by chunk
        total_relationships = 0
        for chunk_id, chunk_relationships in relationships_by_chunk.items():
            try:
                # Log before storing relationships
                logger.info(f"Storing {len(chunk_relationships)} relationships for chunk {chunk_id}")
                if chunk_relationships:
                    sample = chunk_relationships[0]
                    logger.info(f"Sample: {sample['source_id']} --[{sample['relationship_type']}]--> {sample['target_id']}")
                
                result = transport.neo4j_client.store_relationships(chunk_id, chunk_relationships)
                total_relationships += result.get("relationship_count", 0)
                
                # Log after storing relationships
                logger.info(f"Stored {result.get('relationship_count', 0)} relationships for chunk {chunk_id}")
                if result.get("status") == "error":
                    logger.error(f"Error storing relationships: {result.get('error')}")
            except Exception as e:
                logger.error(f"Error storing relationships for chunk {chunk_id}: {e}")

        return total_relationships

    @staticmethod
    def find_similar_entities(query_text: str, entity_type: Optional[str] = None, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Find entities similar to a query text using vector similarity.

        Args:
            query_text: Query text
            entity_type: Optional entity type to filter by
            limit: Maximum number of results

        Returns:
            List[Dict[str, Any]]: List of similar entities
        """
        # Generate embedding for query
        query_embedding = NLPService.generate_embeddings([query_text])[0]

        # Search for similar entities in PostgreSQL
        with DataTransport() as transport:
            similar_entities = transport.find_similar_entities(
                text=query_text,
                embedding=query_embedding,
                entity_type=entity_type,
                threshold=0.7,  # Lower threshold for search queries
                limit=limit
            )

            return similar_entities
