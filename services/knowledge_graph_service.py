import logging
import time
import uuid
import re
import json
from typing import Dict, Any, Union, Optional, List
from google.cloud import aiplatform

from common.id_validator import validate_id_with_context, safe_uuid_conversion

from transport.data_transport import DataTransport
from transport.vertex_ai_batch_client import VertexAIBatchClient
from services.nlp_service import NLPService
from services.kg_entity_relationship_processor import EntityRelationshipProcessor
from common.database import ProcessingTask


# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class KnowledgeGraphService:
    """
    Service for building and maintaining knowledge graphs.
    Uses EntityRelationshipProcessor for handling entities and relationships.
    """

    def __init__(self, processor=None):
        """
        Initialize the knowledge graph service.

        Args:
            processor: Optional custom entity-relationship processor
        """
        self.processor = processor or EntityRelationshipProcessor()

    def _get_batch_job_state(self, batch_job_id: str) -> Optional[str]:
        """Gets the state of a Vertex AI batch prediction job."""
        try:
            vertex_client = VertexAIBatchClient()
            job_state = vertex_client.get_batch_job_state(batch_job_id)
            # Return the state name (e.g., 'JOB_STATE_SUCCEEDED') or None
            return job_state.name if job_state else None
        except Exception as e:
            logger.error(f"Error getting batch job state for {batch_job_id}: {e}")
            return None

    def build_knowledge_graph_from_document(self, document_id: Union[str, uuid.UUID],
                              task_id: Union[str, uuid.UUID],
                              async_mode: bool = True) -> Dict[str, Any]:
        """
        Build a knowledge graph from a document.

        Args:
            document_id: Document ID
            task_id: Processing task ID
            async_mode: Whether to process asynchronously

        Returns:
            Dict[str, Any]: Processing result
        """
        logger.info(f"Building knowledge graph for document: {document_id}, async_mode={async_mode}")

        # If async is requested, use the async method
        if async_mode:
            return self._build_async(document_id, task_id)

        # Otherwise use synchronous method
        try:
            with DataTransport() as transport:
                # Initialize Vertex AI client
                vertex_client = VertexAIBatchClient()
                # Use Neo4j client from DataTransport
                neo4j_client = transport.neo4j_client

                # 1. Get document and chunks
                transport.update_task_status(task_id=task_id, status="processing")
                document = transport.db_client.get_document(document_id)
                if not document:
                    raise ValueError(f"Document not found: {document_id}")

                # Use KG chunks for knowledge graph building
                chunks = transport.get_document_chunks(document_id, chunk_type="kg")
                if not chunks:
                    logger.warning(f"No chunks found for document: {document_id}")
                    return {"status": "warning", "message": "No document chunks found"}

                # 2. Store document in Neo4j
                neo4j_client.store_document(str(document.id), document.to_dict())

                # 3. Process KG chunks with Vertex AI
                logger.info(f"Processing {len(chunks)} KG chunks with Vertex AI")
                batch_job_info = vertex_client.batch_process_chunks(chunks, wait_for_results=True)
                chunk_results = vertex_client.get_batch_results(batch_job_info.get('job'))

                if not chunk_results:
                    logger.warning("No results returned from batch job")
                    return {"status": "success", "message": "No results from batch job"}

                # 4. Process chunk results with integrated processor
                entities, relationships = self.processor.process_batch_results(chunk_results, document_id)

                # 5. Deduplicate and normalize entities
                deduplicated_entities = NLPService.deduplicate_and_normalize_entities(
                    entities, str(document_id))

                # 6. Store entities in Neo4j
                # Group entities by chunk_id for batch processing
                entities_by_chunk = {}
                for entity in deduplicated_entities:
                    chunk_id = entity.get("chunk_id", "default_chunk")
                    if chunk_id not in entities_by_chunk:
                        entities_by_chunk[chunk_id] = []
                    entities_by_chunk[chunk_id].append(entity)

                # Store entities in Neo4j by chunk
                for chunk_id, chunk_entities in entities_by_chunk.items():
                    # Store entities for this chunk (convert chunk_id to string if needed)
                    neo4j_client.store_entities(str(chunk_id) if not isinstance(chunk_id, str) else chunk_id, chunk_entities)

                # Also store entities in PostgreSQL for searchability
                for entity in deduplicated_entities:
                    # Safely handle chunk_id conversion
                    chunk_id_value = None
                    if entity.get("chunk_id"):
                        try:
                            # Try to convert to UUID or use None
                            chunk_id_value = uuid.UUID(entity["chunk_id"])
                        except ValueError:
                            # Invalid UUID format, just use None
                            logger.warning(f"Invalid chunk_id format: {entity.get('chunk_id')}, using None instead")
                            chunk_id_value = None

                    # Check if entity already exists in PostgreSQL
                    entity_id = self._safe_uuid_conversion(entity["id"])
                    existing_entity = transport.db_client.get_entity(entity_id)

                    if not existing_entity:
                        # Store in PostgreSQL only if it doesn't already exist
                        try:
                            transport.db_client.create_entity(
                                id=entity_id,
                                document_id=self._safe_uuid_conversion(document_id),
                                kg_chunk_id=chunk_id_value,  # Use kg_chunk_id instead of chunk_id
                                text=entity["text"],
                                entity_type=entity["type"],
                                confidence=entity.get("score", 1.0)
                            )
                        except Exception as e:
                            logger.warning(f"Error creating entity {entity_id}: {e}")
                    else:
                        logger.info(f"Entity {entity_id} already exists, skipping creation")

                # 7. Store relationships in Neo4j (now have proper IDs)
                # Group relationships by chunk_id for batch processing
                relationships_by_chunk = {}
                for rel in relationships:
                    chunk_id = rel.get("chunk_id", "default_chunk")
                    if chunk_id not in relationships_by_chunk:
                        relationships_by_chunk[chunk_id] = []
                    relationships_by_chunk[chunk_id].append(rel)

                # Store relationships in Neo4j by chunk
                for chunk_id, chunk_relationships in relationships_by_chunk.items():
                    # Store relationships for this chunk (convert chunk_id to string if needed)
                    neo4j_client.store_relationships(str(chunk_id) if not isinstance(chunk_id, str) else chunk_id, chunk_relationships)

                # 8. Create relationships between sequential chunks
                self._create_chunk_relationships(chunks, document_id, neo4j_client)

                # 9. Complete processing
                result = {
                    "status": "success",
                    "document_id": str(document_id),
                    "entities_count": len(deduplicated_entities),
                    "relationships_count": len(relationships),
                    "batch_job_id": batch_job_info.get('batch_job_id')
                }

                transport.update_task_status(
                    task_id=task_id,
                    status="completed",
                    result=result
                )


                return result

        except Exception as e:
            logger.error(f"Error building knowledge graph: {e}", exc_info=True)

            try:
                with DataTransport() as transport:
                    transport.update_task_status(
                        task_id=task_id,
                        status="failed",
                        error=str(e)
                    )
            except Exception:
                pass

            raise e

    def _build_async(self, document_id: Union[str, uuid.UUID],
                    task_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
        """
        Start asynchronous knowledge graph building process.

        Args:
            document_id: Document ID
            task_id: Processing task ID

        Returns:
            Dict[str, Any]: Job status information
        """
        try:
            with DataTransport() as transport:
                # Initialize Vertex AI client
                vertex_client = VertexAIBatchClient()
                # Use Neo4j client from DataTransport
                neo4j_client = transport.neo4j_client

                # 1. Get document and chunks
                transport.update_task_status(task_id=task_id, status="processing")
                document = transport.db_client.get_document(document_id)
                if not document:
                    raise ValueError(f"Document not found: {document_id}")

                # Use KG chunks for knowledge graph building
                chunks = transport.get_document_chunks(document_id, chunk_type="kg")
                if not chunks:
                    logger.warning(f"No chunks found for document: {document_id}")
                    return {"status": "warning", "message": "No document chunks found"}

                # 2. Store document in Neo4j
                neo4j_client.store_document(str(document.id), document.to_dict())

                # 3. Submit batch job without waiting for results
                logger.info(f"Submitting async batch job for {len(chunks)} chunks")
                batch_job_info = vertex_client.batch_process_chunks(chunks=chunks, wait_for_results=False)

                # 4. Update task status with batch job info
                if batch_job_info.get('status') == 'submitted' and batch_job_info.get('batch_job_id'):
                    # If the batch job was created successfully, set the task status to batch_job_submitted
                    transport.update_task_status(
                        task_id=task_id,
                        status="batch_job_created",
                        result={
                            "batch_job_id": batch_job_info.get('batch_job_id'),
                            "batch_job_status": batch_job_info.get('status'),
                            "created_at": time.time(),
                            "async_processing": True
                        }
                    )
                else:
                    # If the status is neither error nor submitted, or there's no batch_job_id, set the task status to failed
                    transport.update_task_status(
                        task_id=task_id,
                        status="batch_job_failed",
                        result={
                            "status": "failed",
                            "error_message": f"Unexpected batch job status: {batch_job_info.get('status')}",
                            "created_at": time.time(),
                            "async_processing": True,
                            "batch_job_info": batch_job_info
                        }
                    )
                    return {
                        "status": "error",
                        "message": f"Unexpected batch job status: {batch_job_info.get('status')}"
                    }

                return {
                    "status": "processing",
                    "document_id": str(document_id),
                    "task_id": str(task_id),
                    "batch_job_id": batch_job_info.get('batch_job_id'),
                    "message": "Knowledge graph building started asynchronously"
                }

        except Exception as e:
            logger.error(f"Error starting async knowledge graph build: {e}", exc_info=True)

            try:
                with DataTransport() as transport:
                    transport.update_task_status(
                        task_id=task_id,
                        status="failed",
                        error=str(e)
                    )
            except Exception:
                pass

            raise e

    def _get_task_and_document_data(self, transport: DataTransport, task_id: uuid.UUID) -> tuple:
        """Fetches task, document, and chunks, validating along the way."""
        task = transport.db_client.get_processing_task(task_id)
        if not task:
            raise ValueError(f"Task {task_id} not found")

        batch_job_id = task.result.get("batch_job_id") if task.result else None
        if not batch_job_id:
            raise ValueError("No batch job ID found in task")

        document_id = task.document_id
        if not transport.db_client.get_document(document_id):
            raise ValueError(f"Document {document_id} not found")

        chunks = transport.get_document_chunks(document_id, chunk_type="kg")
        logger.info(f"Retrieved {len(chunks)} KG chunks for document {document_id}")
        # Log the chunk IDs from the database
        logger.info(f"Chunks from database for document {document_id}:")
        for i, chunk in enumerate(chunks[:5]):  # Log first 5 chunks
            logger.info(f"  DB Chunk {i+1}: ID={chunk.get('id')}, Text={chunk.get('text')[:50]}...")
        if len(chunks) > 5:
            logger.info(f"  ... and {len(chunks) - 5} more chunks")

        return task, document_id, chunks, batch_job_id

    def _fetch_and_process_batch_results(self, vertex_client: VertexAIBatchClient, task: ProcessingTask, chunks: list, document_id: uuid.UUID) -> tuple:
        """Fetches batch results, processes them, and deduplicates entities."""
        batch_job_id = task.result.get("batch_job_id")

        logger.info(f"Getting results for batch job: {batch_job_id}")
        if "batch_results" in task.result:
            logger.info(f"Using batch results from task result")
            batch_results_str = task.result.get("batch_results")
            chunk_results = self._process_batch_results(batch_results_str, chunks)
        else:
            logger.info(f"Getting batch results from Vertex AI")
            chunk_results = vertex_client.get_batch_results(batch_job_id, chunks)

        logger.info(f"Retrieved {len(chunk_results)} chunk results")
        # Log the chunk IDs from the batch results
        logger.info(f"Chunk IDs from batch results:")
        for i, result in enumerate(chunk_results[:5]):  # Log first 5 results
            logger.info(f"  Result {i+1}: chunk_id={result.get('chunk_id')}")
        if len(chunk_results) > 5:
            logger.info(f"  ... and {len(chunk_results) - 5} more results")
        logger.info(f"First chunk result: {chunk_results[0] if chunk_results else None}")

        if not chunk_results:
            logger.warning("No results returned from batch job")
            return [], [] # Return empty lists if no results

        logger.info("Processing entities and relationships")
        entities, relationships = self.processor.process_batch_results(chunk_results, document_id)
        logger.info(f"Extracted {len(entities)} entities and {len(relationships)} relationships")
        logger.info(f"First entity: {entities[0] if entities else None}")
        logger.info(f"First relationship: {relationships[0] if relationships else None}")

        deduplicated_entities = NLPService.deduplicate_and_normalize_entities(
            entities, str(document_id))

        return deduplicated_entities, relationships

    def _store_graph_data(self, transport: DataTransport, neo4j_client, document_id: uuid.UUID, chunks: list, entities: list, relationships: list):
        """Stores entities (Neo4j & PG), relationships (Neo4j), and creates chunk relationships."""
        # Store entities in Neo4j
        logger.info(f"Storing {len(entities)} entities in Neo4j")
        entities_by_chunk = {}
        for entity in entities:
            chunk_id = entity.get("chunk_id", "default_chunk")
            if chunk_id not in entities_by_chunk:
                entities_by_chunk[chunk_id] = []
            entities_by_chunk[chunk_id].append(entity)

        for chunk_id, chunk_entities in entities_by_chunk.items():
            neo4j_client.store_entities(str(chunk_id) if not isinstance(chunk_id, str) else chunk_id, chunk_entities)

        # Store entities in PostgreSQL
        logger.info(f"Storing {len(entities)} entities in PostgreSQL")
        for entity in entities:
            chunk_id_value = None
            if entity.get("chunk_id"):
                try:
                    chunk_id_value = uuid.UUID(entity["chunk_id"])
                except ValueError:
                    logger.warning(f"Invalid chunk_id format: {entity.get('chunk_id')}, using None instead")
                    chunk_id_value = None

            entity_id = self._safe_uuid_conversion(entity["id"])
            existing_entity = transport.db_client.get_entity(entity_id)

            if not existing_entity:
                try:
                    transport.db_client.create_entity(
                        id=entity_id,
                        document_id=self._safe_uuid_conversion(document_id),
                        kg_chunk_id=chunk_id_value,
                        text=entity["text"],
                        entity_type=entity["type"],
                        confidence=entity.get("score", 1.0)
                    )
                except Exception as e:
                    logger.warning(f"Error creating entity {entity_id}: {e}")
            else:
                logger.info(f"Entity {entity_id} already exists, skipping creation")

        # Store relationships in Neo4j
        logger.info(f"Storing {len(relationships)} relationships in Neo4j")
        relationships_by_chunk = {}
        for rel in relationships:
            chunk_id = rel.get("chunk_id", "default_chunk")
            if chunk_id not in relationships_by_chunk:
                relationships_by_chunk[chunk_id] = []
            relationships_by_chunk[chunk_id].append(rel)

        for chunk_id, chunk_relationships in relationships_by_chunk.items():
            neo4j_client.store_relationships(str(chunk_id) if not isinstance(chunk_id, str) else chunk_id, chunk_relationships)

        # Create relationships between sequential chunks
        self._create_chunk_relationships(chunks, document_id, neo4j_client)

    def _finalize_task(self, transport: DataTransport, task_id: uuid.UUID, task_result: dict, entities_count: int, relationships_count: int, document_id: uuid.UUID) -> dict:
        """Updates the task status to completed with the final results."""
        result = {
            **(task_result or {}),  # Preserve existing result data
            "status": "completed",  # Match the task status
            "document_id": str(document_id),
            "entities_count": entities_count,
            "relationships_count": relationships_count,
            "completed_at": time.time()
        }

        transport.update_task_status(
            task_id=task_id,
            status="completed",
            result=result
        )
        logger.info(f"Task {task_id} completed successfully.")
        return result

    def _handle_continuation_error(self, transport: DataTransport, task_id: uuid.UUID, error: Exception):
        """Handles errors during the continuation process."""
        logger.error(f"Error continuing async job for task {task_id}: {error}", exc_info=True)
        try:
            task = transport.db_client.get_processing_task(task_id)
            if task:
                transport.update_task_status(
                    task_id=task_id,
                    status="failed",
                    result={
                        **(task.result or {}),
                        "status": "failed",
                        "error_message": str(error),
                        "failed_at": time.time()
                    },
                    error=str(error)
                )
            else:
                transport.update_task_status(
                    task_id=task_id,
                    status="failed",
                    error=str(error)
                )
        except Exception as inner_e:
            logger.error(f"Error updating task status after failure: {inner_e}")

    def continue_knowledge_graph_building(self, task_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
        """
        Continue knowledge graph building after batch job completes. Refactored for clarity.

        Args:
            task_id: Processing task ID

        Returns:
            Dict[str, Any]: Processing result
        """
        if isinstance(task_id, str):
            try:
                task_id = uuid.UUID(task_id)
            except ValueError:
                 return {"status": "error", "message": f"Invalid task ID format: {task_id}"}

        transport = None # Initialize transport to None
        try:
            with DataTransport() as transport:
                # 1. Get Task, Document, Chunks, and Batch Job ID
                task, document_id, chunks, batch_job_id = self._get_task_and_document_data(transport, task_id)

                # Initialize clients
                vertex_client = VertexAIBatchClient()
                neo4j_client = transport.neo4j_client

                # 2. Fetch and Process Batch Results
                entities, relationships = self._fetch_and_process_batch_results(
                    vertex_client, task, chunks, document_id
                )

                # If no entities/relationships were found (e.g., empty batch results),
                # we can consider the task successful but with no data.
                if not entities and not relationships:
                     logger.warning(f"No entities or relationships extracted for task {task_id}. Completing task.")
                     # Finalize with counts 0
                     return self._finalize_task(transport, task_id, task.result, 0, 0, document_id)


                # 3. Store Graph Data (Entities, Relationships, Chunk Links)
                self._store_graph_data(
                    transport, neo4j_client, document_id, chunks, entities, relationships
                )

                # 4. Finalize Task
                return self._finalize_task(
                    transport, task_id, task.result, len(entities), len(relationships), document_id
                )

        except ValueError as ve: # Catch specific validation errors
             logger.error(f"Validation error during continuation for task {task_id}: {ve}")
             # Attempt to update task status if transport was initialized
             if transport:
                 self._handle_continuation_error(transport, task_id, ve)
             return {"status": "error", "message": str(ve)}
        except Exception as e:
            # Use the dedicated error handler
            if transport: # Ensure transport is available for error handling
                 self._handle_continuation_error(transport, task_id, e)
            else: # Log if transport couldn't be initialized
                 logger.error(f"Critical error before transport initialization for task {task_id}: {e}", exc_info=True)
            # Re-raise the exception after attempting to handle it
            raise e

    def check_batch_job_status(self, task_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
        """
        Check the status of a batch job and update the database.

        Args:
            task_id: Processing task ID

        Returns:
            Dict[str, Any]: Job status
        """
        if isinstance(task_id, str):
            task_id = uuid.UUID(task_id)

        with DataTransport() as transport:
            # Get task information
            task = transport.db_client.get_processing_task(task_id)
            if not task:
                return {"status": "error", "message": f"Task {task_id} not found"}

            batch_job_id = task.result.get("batch_job_id") if task.result else None
            if not batch_job_id:
                return {"status": "error", "message": "No batch job ID found in task"}

            try:
                # Get batch job from Vertex AI
                job = aiplatform.BatchPredictionJob.get(batch_job_id)

                # Update status based on job state
                if job.state == aiplatform.JobState.JOB_STATE_SUCCEEDED:
                    # Job completed, update task
                    transport.update_task_status(
                        task_id=task_id,
                        status="ready_to_continue",
                        result={
                            **task.result,
                            "status": "ready_to_continue",
                            "completed_at": time.time()
                        }
                    )
                    return {
                        "status": "ready_to_continue",
                        "message": "Batch job completed, ready to continue processing"
                    }
                elif job.state in [aiplatform.JobState.JOB_STATE_FAILED, aiplatform.JobState.JOB_STATE_CANCELLED]:
                    # Job failed
                    transport.update_task_status(
                        task_id=task_id,
                        status="failed",
                        result={
                            **task.result,
                            "status": "failed",
                            "error": f"Batch job failed with state: {job.state}"
                        }
                    )
                    return {
                        "status": "failed",
                        "message": f"Batch job failed with state: {job.state}"
                    }
                else:
                    # Job still running
                    transport.update_task_status(
                        task_id=task_id,
                        status="processing",
                        result={
                            **task.result,
                            "status": "processing",
                            "job_state": job.state,
                            "last_checked_at": time.time()
                        }
                    )
                    return {
                        "status": "processing",
                        "message": f"Batch job still running with state: {job.state}"
                    }
            except Exception as e:
                logger.error(f"Error checking batch job status: {e}")
                return {"status": "error", "message": str(e)}

    def _create_chunk_relationships(self, chunks, document_id, neo4j_client):
        """Create relationships between chunks based on index order."""
        chunk_data = []

        for chunk in chunks:
            chunk_index = chunk.get("chunk_index", chunk.get("metadata", {}).get("element_id", 0))
            chunk_data.append({
                "id": str(chunk["id"]),
                "chunk_index": chunk_index
            })

        neo4j_client.create_chunk_relationships(str(document_id), chunk_data)

    def _safe_uuid_conversion(self, id_value):
        """Safely convert a string to UUID or return the original value."""
        # Use the imported safe_uuid_conversion function
        return safe_uuid_conversion(id_value)

    def _process_batch_results(self, batch_results_str: str, chunks: list = None) -> list:
        """
        Process batch results from a string representation.

        Args:
            batch_results_str: String representation of batch results
            chunks: Optional list of chunks for reference (not used in current implementation)

        Returns:
            list: Processed chunk results
        """
        import json
        import re

        logger.info(f"Processing batch results string of length {len(batch_results_str)}")

        # Process the batch results
        entity_results = {}

        try:
            # Split the batch results string into lines
            lines = batch_results_str.strip().split('\n')
            logger.info(f"Batch results contains {len(lines)} lines")

            for line_idx, line in enumerate(lines):
                if not line:
                    continue

                try:
                    # Parse the line as JSON
                    prediction = json.loads(line)
                    logger.info(f"Processing prediction line {line_idx+1}/{len(lines)}, keys: {list(prediction.keys())}")

                    # Extract response text from the API response format
                    response = prediction.get('response', {})
                    candidates = response.get('candidates', [])

                    if not candidates:
                        logger.warning("No candidates found in prediction response")
                        continue

                    content = candidates[0].get('content', {})
                    parts = content.get('parts', [])

                    if not parts:
                        logger.warning("No parts found in prediction response content")
                        continue

                    prediction_text = parts[0].get('text', '')

                    # The prediction text format is typically a markdown code block with JSON
                    # Extract the JSON content from the markdown
                    if prediction_text.startswith('```json') and prediction_text.endswith('```'):
                        json_text = prediction_text[7:-3].strip()
                    else:
                        json_text = prediction_text

                    try:
                        # Parse the extracted JSON
                        extracted_data = json.loads(json_text)

                        # Try to find the chunk ID in the extracted data
                        chunk_id = extracted_data.get('chunk_id', '')

                        # Log chunk ID extraction attempt
                        if chunk_id:
                            logger.info(f"Found chunk_id directly in extracted data: {chunk_id}")
                        else:
                            logger.warning(f"No chunk_id found in extracted data, attempting to extract from request")

                        # If chunk ID is not in the extracted data, try to find it in the request
                        if not chunk_id and 'request' in prediction:
                            request = prediction.get('request', {})
                            contents = request.get('contents', [])
                            if contents and 'parts' in contents[0]:
                                request_parts = contents[0]['parts']
                                if request_parts:
                                    request_text = request_parts[0].get('text', '')
                                    # Extract chunk_id from the request text using multiple patterns
                                    chunk_id_patterns = [
                                        r'"chunk_id":\s*"([^"]+)"',  # JSON format
                                        r'chunk_id:\s*"([^"]+)"',      # YAML-like format
                                        r'Chunk ID:\s*([\w-]+)',         # Plain text format
                                        r'chunk ID:\s*([\w-]+)'          # Case insensitive
                                    ]

                                    for pattern in chunk_id_patterns:
                                        chunk_id_match = re.search(pattern, request_text, re.IGNORECASE)
                                        if chunk_id_match:
                                            chunk_id = chunk_id_match.group(1)
                                            logger.info(f"Extracted chunk_id from request text using pattern '{pattern}': {chunk_id}")
                                            break

                                    if not chunk_id and request_text:
                                        logger.error(f"Failed to extract chunk_id from request text using all patterns")
                                        # Log a portion of the request text for debugging
                                        logger.debug(f"Request text excerpt: {request_text[:200]}...")

                        # Validate and clean the chunk ID if found
                        if chunk_id:
                            # Use the ID validator with context and error on invalid
                            chunk_id = validate_id_with_context(chunk_id, "Batch results processing", logger, error_on_invalid=True)

                            # Add the result to entity_results
                            entity_results[chunk_id] = {
                                'chunk_id': chunk_id,
                                'entities': extracted_data.get('entities', []),
                                'relationships': extracted_data.get('relationships', []),
                                'error': extracted_data.get('error', '')
                            }
                            logger.info(f"Processed prediction for chunk {chunk_id} with {len(extracted_data.get('entities', []))} entities and {len(extracted_data.get('relationships', []))} relationships")
                        else:
                            # This is a critical error - we should not proceed without a chunk ID
                            logger.error("Could not find or extract a valid chunk ID from the prediction")
                            logger.error(f"Prediction keys: {list(prediction.keys())}")
                            logger.error(f"Extracted data keys: {list(extracted_data.keys())}")
                            # We'll raise an exception here to stop processing and alert the system
                            raise ValueError("Missing chunk ID in prediction result - cannot continue processing")
                    except json.JSONDecodeError as e:
                        logger.error(f"Error parsing JSON from prediction text: {e}\nText: {prediction_text[:100]}...")
                        continue

                except json.JSONDecodeError as e:
                    logger.error(f"Error parsing prediction line as JSON: {e}")
                    continue

        except Exception as e:
            logger.error(f"Error processing batch results: {e}")

        # Convert entity_results to a list
        results = list(entity_results.values())

        return results

    def query_knowledge_graph(self, query_text: str, limit: int = 10) -> Dict[str, Any]:
        """Query the knowledge graph using natural language."""
        try:
            with DataTransport() as transport:
                # Use Neo4j client from DataTransport
                results = transport.neo4j_client.query_knowledge_graph(query_text, limit)
                return {
                    "query": query_text,
                    "results": results,
                    "count": len(results)
                }
        except Exception as e:
            logger.error(f"Error querying knowledge graph: {e}")
            raise

    def check_and_continue_batch_jobs(self) -> Dict[str, Any]:
        """
        Periodic task to check for completed Vertex AI batch jobs and continue knowledge graph building.

        This task runs on a schedule (e.g., every minute) to:
        1. Find processing tasks with batch jobs in progress
        2. Check their status
        3. Continue knowledge graph building for completed jobs

        Returns:
            Dict[str, Any]: Summary of processed tasks
        """
        MAX_TASKS_PER_RUN = 10
        logger.info("Running periodic check for Vertex AI batch jobs")

        # First, check for any pending vertex_ai_batch_job tasks
        with DataTransport() as transport:
            # Query for pending vertex_ai_batch_job tasks
            vertex_ai_tasks = transport.db_client.db.query(ProcessingTask).filter(
                ProcessingTask.task_type == "vertex_ai_batch_job",
                ProcessingTask.status == "pending",
                ProcessingTask.result.contains({"batch_job_id": None}).is_(False)
            ).limit(MAX_TASKS_PER_RUN).all()

            if vertex_ai_tasks:
                logger.info(f"Found {len(vertex_ai_tasks)} pending vertex_ai_batch_job tasks")
                
                # Update status for vertex AI batch jobs that have related KG tasks completed/processing
                for task in vertex_ai_tasks:
                    batch_job_id = task.result.get("batch_job_id")
                    if not batch_job_id:
                        continue
                        
                    # Find related knowledge graph tasks
                    related_kg_tasks = transport.db_client.db.query(ProcessingTask).filter(
                        ProcessingTask.task_type == "knowledge_graph_building",
                        ProcessingTask.result.contains({"batch_job_id": batch_job_id})
                    ).all()
                    
                    # If we have related KG tasks that are not in pending state, update this vertex AI job
                    if related_kg_tasks:
                        for kg_task in related_kg_tasks:
                            if kg_task.status in ["batch_processing_started", "completed", "processing"]:
                                logger.info(f"Updating vertex_ai_batch_job task {task.id} to completed based on related KG task {kg_task.id} with status {kg_task.status}")
                                transport.update_task_status(
                                    task_id=task.id,
                                    status="completed",
                                    result={
                                        **(task.result or {}),
                                        "status": "completed",
                                        "completed_at": time.time(),
                                        "related_kg_task_id": str(kg_task.id),
                                        "related_kg_task_status": kg_task.status
                                    }
                                )
                                break

        # Now find processing tasks with batch jobs
        with DataTransport() as transport:
            # List all tasks with batch jobs for debugging
            try:
                from sqlalchemy import text
                logger.info("[DEBUG] Checking for all tasks with batch job IDs...")
                all_batch_query = text("SELECT id, status, task_type FROM processing_tasks WHERE result::text LIKE '%batch_job_id%' ORDER BY created_at DESC LIMIT 10")
                all_batch_jobs = transport.db_client.db.execute(all_batch_query).fetchall()
                logger.info(f"[DEBUG] Recent tasks with batch jobs: {all_batch_jobs}")
            except Exception as e:
                logger.error(f"[DEBUG] Error querying batch jobs: {e}", exc_info=True)

            # Query for processing tasks with batch jobs
            # Modified to include both "processing" and "batch_job_created" statuses
            from sqlalchemy import or_
            tasks = transport.db_client.db.query(ProcessingTask).filter(
                ProcessingTask.task_type == "knowledge_graph_building",
                or_(
                    ProcessingTask.status == "processing",
                    ProcessingTask.status == "batch_job_created" 
                ),
                ProcessingTask.result.contains({"batch_job_id": None}).is_(False),
                ProcessingTask.result.contains({"async_processing": None}).is_(False)
            ).limit(MAX_TASKS_PER_RUN).all()

            if not tasks:
                logger.info("No processing or created knowledge graph batch jobs found")
                return {"status": "success", "processed": 0, "completed": 0}

            logger.info(f"Found {len(tasks)} knowledge graph batch jobs to check")

            # Track statistics
            processed_count = 0
            completed_count = 0

            # Process each task
            for task in tasks:
                task_id = task.id
                logger.info(f"Checking batch job status for task {task_id} with status {task.status}")

                # Get the batch job ID
                batch_job_id = task.result.get("batch_job_id")

                if not batch_job_id:
                    logger.warning(f"No batch job ID found for task {task_id}")
                    continue

                # Check batch job status
                try:
                    # Initialize Vertex AI Batch client
                    from transport.vertex_ai_batch_client import VertexAIBatchClient
                    vertex_client = VertexAIBatchClient()

                    # Try to get the batch job status
                    try:
                        # Get the batch job state
                        job_state = vertex_client.get_batch_job_state(batch_job_id)
                        logger.info(f"Batch job {batch_job_id} state: {job_state}")
                        
                        # Import the JobState enum for comparison
                        from google.cloud.aiplatform.compat.types import job_state as gca_job_state
                        
                        # Check if job completed successfully
                        if job_state == gca_job_state.JobState.JOB_STATE_SUCCEEDED:
                            logger.info(f"Batch job {batch_job_id} completed successfully")

                            # Update task status
                            transport.update_task_status(
                                task_id=task_id,
                                status="batch_processing_started",
                                result={
                                    **task.result,
                                    "processing_started_at": time.time()
                                }
                            )

                            # Continue with knowledge graph building
                            self.continue_knowledge_graph_building(task_id)
                            completed_count += 1
                            
                            # Also update any vertex_ai_batch_job tasks with this batch_job_id
                            vertex_tasks = transport.db_client.db.query(ProcessingTask).filter(
                                ProcessingTask.task_type == "vertex_ai_batch_job",
                                ProcessingTask.result.contains({"batch_job_id": batch_job_id})
                            ).all()
                            
                            for vertex_task in vertex_tasks:
                                if vertex_task.status == "pending":
                                    logger.info(f"Updating vertex_ai_batch_job task {vertex_task.id} from pending to completed")
                                    transport.update_task_status(
                                        task_id=vertex_task.id,
                                        status="completed",
                                        result={
                                            **(vertex_task.result or {}),
                                            "status": "completed",
                                            "completed_at": time.time(),
                                            "related_kg_task_id": str(task_id)
                                        }
                                    )
                            
                        elif job_state in [gca_job_state.JobState.JOB_STATE_FAILED, gca_job_state.JobState.JOB_STATE_CANCELLED]:
                            logger.error(f"Batch job {batch_job_id} failed with state: {job_state}")
                            
                            # Update task status to reflect failure
                            transport.update_task_status(
                                task_id=task_id,
                                status="failed",
                                result={
                                    **task.result,
                                    "status": "failed",
                                    "error": f"Batch job failed with state: {job_state}",
                                    "failed_at": time.time()
                                }
                            )
                            
                            # Also update any vertex_ai_batch_job tasks with this batch_job_id
                            vertex_tasks = transport.db_client.db.query(ProcessingTask).filter(
                                ProcessingTask.task_type == "vertex_ai_batch_job",
                                ProcessingTask.result.contains({"batch_job_id": batch_job_id})
                            ).all()
                            
                            for vertex_task in vertex_tasks:
                                if vertex_task.status == "pending":
                                    logger.info(f"Updating vertex_ai_batch_job task {vertex_task.id} from pending to failed")
                                    transport.update_task_status(
                                        task_id=vertex_task.id,
                                        status="failed",
                                        result={
                                            **(vertex_task.result or {}),
                                            "status": "failed",
                                            "error": f"Batch job failed with state: {job_state}",
                                            "failed_at": time.time()
                                        }
                                    )
                        else:
                            logger.info(f"Batch job {batch_job_id} is still running with state: {job_state}")
                            
                            # If the task was in batch_job_created status, update to processing
                            if task.status == "batch_job_created":
                                transport.update_task_status(
                                    task_id=task_id,
                                    status="processing",
                                    result={
                                        **task.result,
                                        "status": "processing",
                                        "last_checked_at": time.time()
                                    }
                                )
                                logger.info(f"Updated task {task_id} status from batch_job_created to processing")

                        processed_count += 1
                    except Exception as e:
                        logger.error(f"Error checking batch job status: {e}")
                except Exception as e:
                    logger.error(f"Error initializing Vertex AI Batch client: {e}")

            return {
                "status": "success",
                "processed": processed_count,
                "completed": completed_count
            }
