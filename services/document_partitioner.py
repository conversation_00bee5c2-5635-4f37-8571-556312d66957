"""
Document partitioning service for different document types.
"""
import os
import logging
import tempfile
import requests
from typing import List, Any, Dict, Optional, Union
from urllib.parse import urlparse

# Import specific partition functions
from unstructured.partition.pdf import partition_pdf
from unstructured.partition.html import partition_html
from unstructured.partition.text import partition_text
from unstructured.partition.epub import partition_epub
from unstructured.partition.auto import partition as partition_auto

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DocumentPartitioner:
    """Service for partitioning documents of different types."""

    # Minimum chunk size in characters
    MIN_CHUNK_SIZE = 100

    @staticmethod
    def partition_document(file_path_or_url: str, content_type: Optional[str] = None) -> List[Any]:
        """
        Partition a document using the appropriate method based on file type or URL.

        Args:
            file_path_or_url: Path to the document file or URL
            content_type: MIME type of the document (optional)

        Returns:
            List[Any]: List of document elements
        """
        # Check if the input is a URL
        parsed_url = urlparse(file_path_or_url)
        is_url = bool(parsed_url.scheme and parsed_url.netloc)

        if is_url:
            logger.info(f"Detected URL: {file_path_or_url}")
            elements = DocumentPartitioner.partition_url(file_path_or_url)
            return DocumentPartitioner.filter_elements(elements)

        # If content_type is not provided, try to determine from file extension
        if not content_type:
            _, ext = os.path.splitext(file_path_or_url)
            ext = ext.lower()

            if ext == '.pdf':
                content_type = 'application/pdf'
            elif ext in ['.html', '.htm']:
                content_type = 'text/html'
            elif ext == '.epub':
                content_type = 'application/epub+zip'
            elif ext in ['.txt', '.md', '.rst']:
                content_type = 'text/plain'

        logger.info(f"Partitioning document {file_path_or_url} with content type {content_type}")

        # Use the appropriate partition function based on content type
        if content_type == 'application/pdf':
            logger.info(f"Using PDF partitioner for {file_path_or_url}")
            elements = DocumentPartitioner.partition_pdf(file_path_or_url)
            return DocumentPartitioner.filter_elements(elements)
        elif content_type == 'text/html':
            logger.info(f"Using HTML partitioner for {file_path_or_url}")
            elements = DocumentPartitioner.partition_html(file_path_or_url)
            return DocumentPartitioner.filter_elements(elements)
        elif content_type == 'application/epub+zip':
            logger.info(f"Using EPUB partitioner for {file_path_or_url}")
            elements = DocumentPartitioner.partition_epub(file_path_or_url)
            return DocumentPartitioner.filter_elements(elements)
        elif content_type == 'text/plain':
            logger.info(f"Using text partitioner for {file_path_or_url}")
            elements = DocumentPartitioner.partition_text(file_path_or_url)
            return DocumentPartitioner.filter_elements(elements)
        else:
            logger.info(f"Using auto partitioner for {file_path_or_url}")
            elements = DocumentPartitioner.partition_auto(file_path_or_url)
            return DocumentPartitioner.filter_elements(elements)

    @staticmethod
    def partition_pdf(file_path: str) -> List[Any]:
        """
        Partition a PDF document.

        Args:
            file_path: Path to the PDF file

        Returns:
            List[Any]: List of document elements
        """
        try:
            # Use unstructured's PDF partitioner with optimal settings
            elements = partition_pdf(
                filename=file_path,
                extract_images=True,
                infer_table_structure=True,
                chunking_strategy="by_title",
                max_characters=4000,
                new_after_n_chars=3800,
                combine_text_under_n_chars=2000,
                extract_image_block_types=["Image", "Table"]
            )
            logger.info(f"Successfully partitioned PDF document {file_path} into {len(elements)} elements")
            return elements
        except Exception as e:
            logger.error(f"Error partitioning PDF document {file_path}: {str(e)}")
            # Fall back to auto partitioner
            logger.info(f"Falling back to auto partitioner for {file_path}")
            return partition_auto(file_path)

    @staticmethod
    def partition_html(file_path: str) -> List[Any]:
        """
        Partition an HTML document.

        Args:
            file_path: Path to the HTML file

        Returns:
            List[Any]: List of document elements
        """
        try:
            # Use unstructured's HTML partitioner with optimal settings
            elements = partition_html(
                filename=file_path,
                chunking_strategy="by_title",
                max_characters=4000,
                new_after_n_chars=3800,
                combine_text_under_n_chars=2000
            )
            logger.info(f"Successfully partitioned HTML document {file_path} into {len(elements)} elements")
            return elements
        except Exception as e:
            logger.error(f"Error partitioning HTML document {file_path}: {str(e)}")
            # Fall back to auto partitioner
            logger.info(f"Falling back to auto partitioner for {file_path}")
            return partition_auto(file_path)

    @staticmethod
    def partition_text(file_path: str) -> List[Any]:
        """
        Partition a text document.

        Args:
            file_path: Path to the text file

        Returns:
            List[Any]: List of document elements
        """
        try:
            # Use unstructured's text partitioner with optimal settings
            elements = partition_text(
                filename=file_path,
                chunking_strategy="by_title",
                max_characters=4000,
                new_after_n_chars=3800,
                combine_text_under_n_chars=2000
            )
            logger.info(f"Successfully partitioned text document {file_path} into {len(elements)} elements")
            return elements
        except Exception as e:
            logger.error(f"Error partitioning text document {file_path}: {str(e)}")
            # Fall back to auto partitioner
            logger.info(f"Falling back to auto partitioner for {file_path}")
            return partition_auto(file_path)

    @staticmethod
    def partition_epub(file_path: str) -> List[Any]:
        """
        Partition an EPUB document.

        Args:
            file_path: Path to the EPUB file

        Returns:
            List[Any]: List of document elements
        """
        try:
            # Use unstructured's EPUB partitioner with optimal settings
            elements = partition_epub(
                filename=file_path,
                chunking_strategy="by_title",
                max_characters=4000,
                new_after_n_chars=3800,
                combine_text_under_n_chars=2000
            )
            logger.info(f"Successfully partitioned EPUB document {file_path} into {len(elements)} elements")
            return elements
        except Exception as e:
            logger.error(f"Error partitioning EPUB document {file_path}: {str(e)}")
            # Fall back to auto partitioner
            logger.info(f"Falling back to auto partitioner for {file_path}")
            return partition_auto(file_path)

    @staticmethod
    def partition_auto(file_path: str) -> List[Any]:
        """
        Partition a document using auto detection.

        Args:
            file_path: Path to the document file

        Returns:
            List[Any]: List of document elements
        """
        try:
            # Use unstructured's auto partitioner with optimal settings
            elements = partition_auto(
                filename=file_path,
                chunking_strategy="by_title",
                max_characters=4000,
                new_after_n_chars=3800,
                combine_text_under_n_chars=2000
            )
            logger.info(f"Successfully partitioned document {file_path} into {len(elements)} elements")
            return elements
        except Exception as e:
            logger.error(f"Error partitioning document {file_path}: {str(e)}")
            raise

    @staticmethod
    def filter_elements(elements: List[Any]) -> List[Any]:
        """
        Filter out elements that are too small or empty.

        Args:
            elements: List of document elements from unstructured.io

        Returns:
            List[Any]: Filtered list of elements
        """
        filtered_elements = []

        for element in elements:
            # Convert element to string to get its text content
            text = str(element).strip()

            # Skip empty elements or elements with very little text
            if not text or len(text) < DocumentPartitioner.MIN_CHUNK_SIZE:
                logger.info(f"Filtering out small element with {len(text)} characters: {text[:50]}...")
                continue

            filtered_elements.append(element)

        logger.info(f"Filtered {len(elements) - len(filtered_elements)} elements out of {len(elements)} total elements")
        return filtered_elements

    @staticmethod
    def partition_url(url: str) -> List[Any]:
        """
        Partition content from a URL.

        Args:
            url: URL to fetch and partition

        Returns:
            List[Any]: List of document elements
        """
        logger.info(f"Partitioning content from URL: {url}")

        try:
            # Determine the content type based on URL extension or response headers
            parsed_url = urlparse(url)
            path = parsed_url.path
            _, ext = os.path.splitext(path)
            ext = ext.lower()

            # Make a HEAD request to get content type if possible
            try:
                head_response = requests.head(url, timeout=10)
                content_type = head_response.headers.get('Content-Type', '')
                logger.info(f"Detected content type from headers: {content_type}")
            except Exception as e:
                logger.warning(f"Could not determine content type from headers: {str(e)}")
                content_type = ''

            # If content type is still unknown, try to determine from extension
            if not content_type or 'text/html' in content_type:
                if ext == '.pdf':
                    content_type = 'application/pdf'
                elif ext in ['.epub']:
                    content_type = 'application/epub+zip'
                elif ext in ['.txt', '.md', '.rst']:
                    content_type = 'text/plain'
                else:
                    # Default to HTML for most web content
                    content_type = 'text/html'

            logger.info(f"Processing URL {url} as {content_type}")

            # Fetch the content
            response = requests.get(url, timeout=30)
            response.raise_for_status()  # Raise exception for 4XX/5XX responses

            # Process based on content type
            if 'application/pdf' in content_type:
                # Save PDF to temporary file and process
                with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as temp_file:
                    temp_file.write(response.content)
                    temp_file_path = temp_file.name

                try:
                    elements = DocumentPartitioner.partition_pdf(temp_file_path)
                    os.unlink(temp_file_path)  # Clean up temp file
                    return DocumentPartitioner.filter_elements(elements)
                except Exception as e:
                    os.unlink(temp_file_path)  # Clean up temp file
                    raise e

            elif 'application/epub+zip' in content_type:
                # Save EPUB to temporary file and process
                with tempfile.NamedTemporaryFile(suffix='.epub', delete=False) as temp_file:
                    temp_file.write(response.content)
                    temp_file_path = temp_file.name

                try:
                    elements = DocumentPartitioner.partition_epub(temp_file_path)
                    os.unlink(temp_file_path)  # Clean up temp file
                    return DocumentPartitioner.filter_elements(elements)
                except Exception as e:
                    os.unlink(temp_file_path)  # Clean up temp file
                    raise e

            elif 'text/plain' in content_type:
                # Save text to temporary file and process
                with tempfile.NamedTemporaryFile(suffix='.txt', delete=False) as temp_file:
                    temp_file.write(response.content)
                    temp_file_path = temp_file.name

                try:
                    elements = DocumentPartitioner.partition_text(temp_file_path)
                    os.unlink(temp_file_path)  # Clean up temp file
                    return DocumentPartitioner.filter_elements(elements)
                except Exception as e:
                    os.unlink(temp_file_path)  # Clean up temp file
                    raise e

            else:  # Default to HTML processing
                # Save HTML to temporary file and process
                with tempfile.NamedTemporaryFile(suffix='.html', delete=False) as temp_file:
                    temp_file.write(response.content)
                    temp_file_path = temp_file.name

                try:
                    # Use the HTML partitioner
                    elements = partition_html(
                        filename=temp_file_path,
                        chunking_strategy="by_title",
                        max_characters=4000,
                        new_after_n_chars=3800,
                        combine_text_under_n_chars=2000
                    )
                    os.unlink(temp_file_path)  # Clean up temp file
                    logger.info(f"Successfully partitioned URL {url} into {len(elements)} elements")
                    return DocumentPartitioner.filter_elements(elements)
                except Exception as e:
                    os.unlink(temp_file_path)  # Clean up temp file
                    raise e

        except Exception as e:
            logger.error(f"Error partitioning URL {url}: {str(e)}")
            # Try a fallback approach for web content
            try:
                logger.info(f"Attempting fallback partitioning for URL {url}")
                response = requests.get(url, timeout=30)
                response.raise_for_status()

                # Save content to a temporary file and use auto partitioning
                with tempfile.NamedTemporaryFile(suffix='.html', delete=False) as temp_file:
                    temp_file.write(response.content)
                    temp_file_path = temp_file.name

                try:
                    elements = partition_auto(temp_file_path)
                    os.unlink(temp_file_path)  # Clean up temp file
                    logger.info(f"Fallback partitioning successful for URL {url}")
                    return DocumentPartitioner.filter_elements(elements)
                except Exception as fallback_error:
                    os.unlink(temp_file_path)  # Clean up temp file
                    logger.error(f"Fallback partitioning failed for URL {url}: {str(fallback_error)}")
                    raise fallback_error
            except Exception as fallback_error:
                logger.error(f"All partitioning attempts failed for URL {url}: {str(fallback_error)}")
                raise fallback_error
