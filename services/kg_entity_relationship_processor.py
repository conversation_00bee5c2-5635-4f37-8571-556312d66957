import logging
import re
import uuid
import json
from typing import Dict, List, Any, Tuple, Union, Optional

from common.id_validator import validate_id_with_context

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EntityRelationshipProcessor:
    """
    Integrated processor for extracting and linking entities and relationships.
    Handles various input formats and ensures relationships have proper entity IDs.
    """

    def __init__(self, log_level=logging.INFO):
        """
        Initialize the processor with configurable logging.

        Args:
            log_level: Logging level (default: INFO)
        """
        self.logger = logging.getLogger(__name__)
        self.logger.setLevel(log_level)

        # Internal state for entity mapping
        self.entity_map = {}  # Maps entity text to entity ID
        self.entity_cache = {}  # Cache for normalized entity text
        self.processed_entities_ids = set()  # Track processed entity IDs

    def process_batch_results(self, chunk_results: Any, document_id: Union[str, uuid.UUID]) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        Process batch results and extract both entities and relationships.

        Args:
            chunk_results: Results from batch processing
            document_id: Document identifier

        Returns:
            tuple: (entities, relationships with resolved IDs)
        """
        # Convert document_id to string if it's a UUID
        if isinstance(document_id, uuid.UUID):
            document_id = str(document_id)

        # Reset internal state for new processing
        self.entity_map = {}
        self.entity_cache = {}
        self.processed_entities_ids = set()

        # Extract raw entities and relationships
        self.logger.info(f"Extracting entities and relationships for document {document_id}")
        entities, relationships = self._extract_from_batch_results(chunk_results, document_id)

        # Log the extracted entities for debugging
        self.logger.info(f"Extracted {len(entities)} entities and {len(relationships)} relationships")
        for entity in entities[:5]:  # Log first 5 entities
            self.logger.debug(f"Entity: {entity.get('text')} ({entity.get('type')}) - ID: {entity.get('id')}")

        # Build entity map from extracted entities
        self._build_entity_map(entities)

        # Log the entity map for debugging
        self.logger.debug(f"Built entity map with {len(self.entity_map)} entries")
        entity_map_sample = list(self.entity_map.items())[:5]  # First 5 entries
        for text, entity_id in entity_map_sample:
            self.logger.debug(f"Entity map: '{text}' -> {entity_id}")

        # Process relationships with entity IDs
        self.logger.info(f"Resolving relationship IDs for {len(relationships)} relationships")
        processed_relationships = self._resolve_relationship_ids(relationships)

        self.logger.info(f"Processed {len(entities)} entities and {len(processed_relationships)} relationships")

        # Return both processed collections
        return entities, processed_relationships

    def _extract_from_batch_results(self, chunk_results: Any, document_id: str) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        Extract raw entities and relationships from various formats.

        Args:
            chunk_results: Results from batch processing
            document_id: Document identifier

        Returns:
            tuple: (raw entities, raw relationships)
        """
        all_entities = []
        all_relationships = []

        # Handle Vertex AI batch response format
        if isinstance(chunk_results, dict) and "predictions" in chunk_results:
            self.logger.info("Processing Vertex AI batch response format")
            return self._extract_from_vertex_batch(chunk_results, document_id)

        # Process individual results
        for result in chunk_results:
            # First try to extract chunk_id from the result directly
            chunk_id = result.get("chunk_id")

            # Log chunk ID extraction attempt
            if chunk_id:
                self.logger.info(f"Found chunk_id directly in result: {chunk_id}")
            else:
                self.logger.warning(f"No chunk_id found directly in result, attempting extraction from other sources")

            # If not found, try to extract from the response JSON
            if not chunk_id and "response" in result and "candidates" in result["response"]:
                candidates = result["response"]["candidates"]
                if candidates and "content" in candidates[0]:
                    content = candidates[0]["content"]
                    if "parts" in content and content["parts"]:
                        response_text = content["parts"][0].get("text", "")
                        # Try to parse as JSON
                        if response_text.startswith("```json") and response_text.endswith("```"):
                            try:
                                json_text = response_text[7:-3].strip()
                                response_json = json.loads(json_text)
                                if "chunk_id" in response_json:
                                    chunk_id = response_json.get("chunk_id")
                                    self.logger.info(f"Extracted chunk_id from response JSON: {chunk_id}")
                            except json.JSONDecodeError:
                                self.logger.warning(f"Failed to parse response text as JSON: {response_text[:100]}...")

            # If still not found, try to extract from the request text
            if not chunk_id and "request" in result and "contents" in result["request"]:
                request_contents = result["request"]["contents"]
                if request_contents and "parts" in request_contents[0]:
                    request_parts = request_contents[0]["parts"]
                    if request_parts:
                        request_text = request_parts[0].get("text", "")
                        # Extract chunk_id from the request text using multiple patterns
                        chunk_id_patterns = [
                            r'"chunk_id":\s*"([^"]+)"',  # JSON format
                            r'chunk_id:\s*"([^"]+)"',      # YAML-like format
                            r'Chunk ID:\s*([\w-]+)',         # Plain text format
                            r'chunk ID:\s*([\w-]+)'          # Case insensitive
                        ]

                        for pattern in chunk_id_patterns:
                            chunk_id_match = re.search(pattern, request_text, re.IGNORECASE)
                            if chunk_id_match:
                                chunk_id = chunk_id_match.group(1)
                                self.logger.info(f"Extracted chunk_id from request text using pattern '{pattern}': {chunk_id}")
                                break

                        if not chunk_id:
                            self.logger.error(f"Failed to extract chunk_id from request text using all patterns")
                            # Log a portion of the request text for debugging
                            self.logger.debug(f"Request text excerpt: {request_text[:200]}...")

            # Validate and clean the chunk ID if found
            if chunk_id:
                # Ensure chunk_id is properly formatted (no spaces, special chars, etc.)
                chunk_id = chunk_id.strip()
                if not re.match(r'^[\w\-]+$', chunk_id):
                    self.logger.warning(f"Extracted chunk_id '{chunk_id}' contains invalid characters, cleaning up")
                    # Clean up the chunk ID - remove invalid characters
                    chunk_id = re.sub(r'[^\w\-]', '', chunk_id)
            else:
                self.logger.error("Missing chunk_id in result, skipping")
                # Log more details about the result for debugging
                self.logger.debug(f"Result keys: {list(result.keys())}")
                continue

            # Check for nested Vertex AI response format
            if "prediction" in result and isinstance(result["prediction"], dict):
                entities, relationships = self._extract_from_vertex_prediction(result, chunk_id, document_id)
                all_entities.extend(entities)
                all_relationships.extend(relationships)
                continue

            # Check for Vertex AI response format (new format)
            if "response" in result and "candidates" in result["response"]:
                candidates = result["response"]["candidates"]
                if candidates and "content" in candidates[0]:
                    content = candidates[0]["content"]
                    parts = content.get("parts", [])

                    for part in parts:
                        if "text" in part:
                            try:
                                # Parse the JSON string in the text field
                                text = part["text"]

                                # Remove markdown code block markers if present
                                if text.startswith("```json") and text.endswith("```"):
                                    text = text[7:-3].strip()

                                # Parse the JSON
                                json_data = json.loads(text)

                                # Extract entities
                                entities = self._extract_entities_from_json(json_data, chunk_id, document_id)
                                all_entities.extend(entities)

                                # Extract relationships
                                relationships = self._extract_relationships_from_json(json_data, chunk_id, document_id)
                                all_relationships.extend(relationships)

                                self.logger.info(f"Extracted {len(entities)} entities and {len(relationships)} relationships from chunk {chunk_id}")
                            except json.JSONDecodeError as e:
                                self.logger.warning(f"Failed to parse JSON from text in chunk {chunk_id}: {e}\nText: {part['text'][:100]}...")
                    continue

            # Direct entities and relationships access
            if "entities" in result and isinstance(result["entities"], list):
                entities = self._process_direct_entities(result["entities"], chunk_id, document_id)
                all_entities.extend(entities)

                # Also check for relationships
                if "relationships" in result and isinstance(result["relationships"], list):
                    relationships = self._process_direct_relationships(result["relationships"], chunk_id, document_id)
                    all_relationships.extend(relationships)
                continue

            # Try to extract from markup text if no other method worked
            if "text" in result:
                entities, relationships = self._extract_from_markup(result["text"], chunk_id, document_id)
                all_entities.extend(entities)
                all_relationships.extend(relationships)

        return all_entities, all_relationships

    def _extract_from_vertex_batch(self, batch_results: Dict[str, Any], document_id: str) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        Extract entities and relationships from Vertex AI batch response format.

        Args:
            batch_results: Vertex AI batch response
            document_id: Document identifier

        Returns:
            tuple: (entities, relationships)
        """
        all_entities = []
        all_relationships = []

        predictions = batch_results["predictions"]
        for prediction in predictions:
            # Try to find the chunk ID in various places
            chunk_id = None

            # 1. Check if chunk_id is directly in the prediction
            if "chunk_id" in prediction:
                chunk_id = prediction.get("chunk_id")
                self.logger.info(f"Found chunk_id directly in prediction: {chunk_id}")

            # 2. Try to extract from request text
            if not chunk_id and "request" in prediction and "contents" in prediction["request"]:
                request_contents = prediction["request"]["contents"]
                if request_contents and "parts" in request_contents[0]:
                    request_parts = request_contents[0]["parts"]
                    if request_parts:
                        request_text = request_parts[0].get("text", "")
                        # Extract chunk_id from the request text using multiple patterns
                        chunk_id_patterns = [
                            r'"chunk_id":\s*"([^"]+)"',  # JSON format
                            r'chunk_id:\s*"([^"]+)"',      # YAML-like format
                            r'Chunk ID:\s*([\w-]+)',         # Plain text format
                            r'chunk ID:\s*([\w-]+)'          # Case insensitive
                        ]

                        for pattern in chunk_id_patterns:
                            chunk_id_match = re.search(pattern, request_text, re.IGNORECASE)
                            if chunk_id_match:
                                chunk_id = chunk_id_match.group(1)
                                self.logger.info(f"Extracted chunk_id from request text using pattern '{pattern}': {chunk_id}")
                                break

                        if not chunk_id and request_text:
                            self.logger.error(f"Failed to extract chunk_id from request text using all patterns")
                            # Log a portion of the request text for debugging
                            self.logger.debug(f"Request text excerpt: {request_text[:200]}...")

            # 3. Try to extract from response JSON
            if not chunk_id and "response" in prediction and "candidates" in prediction["response"]:
                candidates = prediction["response"]["candidates"]
                if candidates and "content" in candidates[0]:
                    content = candidates[0]["content"]
                    if "parts" in content and content["parts"]:
                        response_text = content["parts"][0].get("text", "")
                        # Try to parse as JSON
                        if response_text.startswith("```json") and response_text.endswith("```"):
                            try:
                                json_text = response_text[7:-3].strip()
                                response_json = json.loads(json_text)
                                if "chunk_id" in response_json:
                                    chunk_id = response_json.get("chunk_id")
                                    self.logger.info(f"Extracted chunk_id from response JSON: {chunk_id}")
                            except json.JSONDecodeError:
                                self.logger.warning(f"Failed to parse response text as JSON: {response_text[:100]}...")

            # Validate and clean the chunk ID if found
            if chunk_id:
                # Use the ID validator with context
                chunk_id = validate_id_with_context(chunk_id, "Vertex batch prediction", self.logger)
                if not chunk_id:
                    # Log more details about the prediction for debugging
                    self.logger.debug(f"Prediction keys: {list(prediction.keys())}")
                    continue
            else:
                self.logger.error("Missing chunk_id in prediction, skipping")
                # Log more details about the prediction for debugging
                self.logger.debug(f"Prediction keys: {list(prediction.keys())}")
                continue

            # Process prediction content
            if "response" in prediction and "candidates" in prediction["response"]:
                candidates = prediction["response"]["candidates"]
                if candidates and "content" in candidates[0]:
                    content = candidates[0]["content"]
                    parts = content.get("parts", [])

                    for part in parts:
                        if "text" in part:
                            try:
                                # Parse the JSON string in the text field
                                text = part["text"]

                                # Remove markdown code block markers if present
                                if text.startswith("```json") and text.endswith("```"):
                                    text = text[7:-3].strip()

                                # Parse the JSON
                                json_data = json.loads(text)

                                # Extract entities
                                entities = self._extract_entities_from_json(json_data, chunk_id, document_id)
                                all_entities.extend(entities)

                                # Extract relationships
                                relationships = self._extract_relationships_from_json(json_data, chunk_id, document_id)
                                all_relationships.extend(relationships)

                                self.logger.info(f"Extracted {len(entities)} entities and {len(relationships)} relationships from chunk {chunk_id}")
                            except json.JSONDecodeError as e:
                                self.logger.warning(f"Failed to parse JSON from text in chunk {chunk_id}: {e}\nText: {part['text'][:100]}...")

        return all_entities, all_relationships

    def _extract_from_vertex_prediction(self, result: Dict[str, Any], chunk_id: str, document_id: str) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        Extract entities and relationships from a Vertex AI prediction.

        Args:
            result: Prediction result
            chunk_id: Chunk identifier
            document_id: Document identifier

        Returns:
            tuple: (entities, relationships)
        """
        entities = []
        relationships = []

        try:
            content = result["prediction"].get("content", {})
            parts = content.get("parts", [])

            for part in parts:
                if "text" in part:
                    text = part["text"]
                    # Try to parse JSON from text
                    try:
                        json_data = json.loads(text)

                        # Extract entities
                        extracted_entities = self._extract_entities_from_json(json_data, chunk_id, document_id)
                        entities.extend(extracted_entities)

                        # Extract relationships
                        extracted_relationships = self._extract_relationships_from_json(json_data, chunk_id, document_id)
                        relationships.extend(extracted_relationships)
                    except json.JSONDecodeError:
                        self.logger.warning(f"Failed to parse JSON from text in chunk {chunk_id}")
        except Exception as e:
            self.logger.warning(f"Error extracting from Vertex AI format: {e}")

        return entities, relationships

    def _extract_entities_from_json(self, json_data: Dict[str, Any], chunk_id: str, document_id: str) -> List[Dict[str, Any]]:
        """
        Extract entities from JSON data.

        Args:
            json_data: JSON data containing entities
            chunk_id: Chunk identifier
            document_id: Document identifier

        Returns:
            list: Extracted entities
        """
        entities = []

        if "entities" in json_data and isinstance(json_data["entities"], list):
            for entity in json_data["entities"]:
                if "text" in entity:
                    entity_id = entity.get("id", str(uuid.uuid4()))

                    # Skip if we've already processed this entity ID
                    if entity_id in self.processed_entities_ids:
                        continue

                    # Get entity type and normalize it
                    entity_type = entity.get("type", "unknown")
                    normalized_type = self._normalize_entity_type(entity_type)

                    entity_obj = {
                        "id": entity_id,
                        "text": entity["text"],
                        "type": normalized_type,
                        "description": entity.get("significance", entity.get("description", "")),
                        "chunk_id": chunk_id,
                        "document_id": document_id,
                        "confidence": entity.get("confidence", 1.0)
                    }

                    entities.append(entity_obj)
                    self.processed_entities_ids.add(entity_id)
                    self.logger.debug(f"Extracted entity: {entity['text']} ({entity.get('type', 'unknown')})")

        return entities

    def _extract_relationships_from_json(self, json_data: Dict[str, Any], chunk_id: str, document_id: str) -> List[Dict[str, Any]]:
        """
        Extract relationships from JSON data.

        Args:
            json_data: JSON data containing relationships
            chunk_id: Chunk identifier
            document_id: Document identifier

        Returns:
            list: Extracted relationships
        """
        relationships = []

        if "relationships" in json_data and isinstance(json_data["relationships"], list):
            for rel in json_data["relationships"]:
                source_text = rel.get("source_text", "")
                target_text = rel.get("target_text", "")
                rel_type = rel.get("type", "RELATED_TO")

                if source_text and target_text:
                    rel_id = rel.get("id", str(uuid.uuid4()))
                    rel_obj = {
                        "id": rel_id,
                        "source_text": source_text,
                        "target_text": target_text,
                        "type": rel_type,
                        "evidence_text": rel.get("evidence_text", ""),
                        "chunk_id": chunk_id,
                        "document_id": document_id,
                        "confidence": rel.get("confidence", 1.0)
                    }

                    relationships.append(rel_obj)
                    self.logger.debug(f"Extracted relationship: {source_text} --{rel_type}--> {target_text}")

        return relationships

    def _process_direct_entities(self, entities: List[Dict[str, Any]], chunk_id: str, document_id: str) -> List[Dict[str, Any]]:
        """
        Process entities that are directly provided.

        Args:
            entities: List of entity dictionaries
            chunk_id: Chunk identifier
            document_id: Document identifier

        Returns:
            list: Processed entities
        """
        processed = []

        # Use the ID validator with context
        chunk_id = validate_id_with_context(chunk_id, "Entity processing", self.logger)
        if not chunk_id:
            return processed

        self.logger.info(f"Processing entities with validated chunk_id: {chunk_id}")

        for entity in entities:
            if "id" not in entity:
                entity["id"] = str(uuid.uuid4())

            # Skip if we've already processed this entity ID
            if entity["id"] in self.processed_entities_ids:
                continue

            entity["chunk_id"] = chunk_id
            entity["document_id"] = document_id

            # Ensure required fields are present
            if "text" not in entity:
                self.logger.warning(f"Entity missing 'text' field: {entity}")
                continue

            if "type" not in entity:
                entity["type"] = "unknown"
                self.logger.warning(f"Entity missing 'type' field, setting to 'unknown': {entity}")
            else:
                # Normalize entity type
                entity["type"] = self._normalize_entity_type(entity["type"])
                self.logger.debug(f"Normalized entity type to: {entity['type']}")

            processed.append(entity)
            self.processed_entities_ids.add(entity["id"])

        return processed

    def _process_direct_relationships(self, relationships: List[Dict[str, Any]], chunk_id: str, document_id: str) -> List[Dict[str, Any]]:
        """
        Process relationships that are directly provided.

        Args:
            relationships: List of relationship dictionaries
            chunk_id: Chunk identifier
            document_id: Document identifier

        Returns:
            list: Processed relationships
        """
        processed = []

        # Use the ID validator with context
        chunk_id = validate_id_with_context(chunk_id, "Relationship processing", self.logger)
        if not chunk_id:
            return processed

        self.logger.info(f"Processing relationships with validated chunk_id: {chunk_id}")

        for rel in relationships:
            if "id" not in rel:
                rel["id"] = str(uuid.uuid4())

            rel["chunk_id"] = chunk_id
            rel["document_id"] = document_id

            # Ensure required fields are present
            if "source_text" not in rel:
                self.logger.warning(f"Relationship missing 'source_text' field: {rel}")
                continue

            if "target_text" not in rel:
                self.logger.warning(f"Relationship missing 'target_text' field: {rel}")
                continue

            if "type" not in rel:
                rel["type"] = "RELATED_TO"
                self.logger.warning(f"Relationship missing 'type' field, setting to 'RELATED_TO': {rel}")

            processed.append(rel)

        return processed

    def _extract_from_markup(self, text: str, chunk_id: str, document_id: str) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        Extract entities and relationships from markup text.

        Args:
            text: Markup text
            chunk_id: Chunk identifier
            document_id: Document identifier

        Returns:
            tuple: (entities, relationships)
        """
        entities = []
        relationships = []

        if not text:
            return entities, relationships

        # Extract markdown-style links [EntityText](EntityType)
        markdown_entities = re.findall(r'\[([^\]]+)\]\(([^\)]+)\)', text)
        for match in markdown_entities:
            entity_text, entity_type = match
            entity_id = str(uuid.uuid4())
            entity_obj = {
                "id": entity_id,
                "text": entity_text,
                "type": entity_type,
                "chunk_id": chunk_id,
                "document_id": document_id
            }

            entities.append(entity_obj)
            self.logger.debug(f"Extracted entity from markup: {entity_text} ({entity_type})")

        # Extract HTML-style tags <entity type="EntityType">EntityText</entity>
        html_entities = re.findall(r'<entity\s+type=["\'](.*?)["\']>(.*?)</entity>', text)
        for match in html_entities:
            entity_type, entity_text = match
            entity_id = str(uuid.uuid4())
            entity_obj = {
                "id": entity_id,
                "text": entity_text,
                "type": entity_type,
                "chunk_id": chunk_id,
                "document_id": document_id
            }

            entities.append(entity_obj)
            self.logger.debug(f"Extracted entity from HTML markup: {entity_text} ({entity_type})")

        # Extract relationships from markup [SourceEntity]-[RelationType]->[TargetEntity]
        relationships_markup = re.findall(r'\[([^\]]+)\]-\[([^\]]+)\]->\[([^\]]+)\]', text)
        for match in relationships_markup:
            source_text, rel_type, target_text = match
            rel_id = str(uuid.uuid4())
            rel_obj = {
                "id": rel_id,
                "source_text": source_text,
                "target_text": target_text,
                "type": rel_type,
                "chunk_id": chunk_id,
                "document_id": document_id
            }

            relationships.append(rel_obj)
            self.logger.debug(f"Extracted relationship from markup: {source_text} --{rel_type}--> {target_text}")

        return entities, relationships

    def _build_entity_map(self, entities: List[Dict[str, Any]]) -> None:
        """
        Build internal mapping from entity text to IDs.

        Args:
            entities: List of entity dictionaries
        """
        self.logger.info(f"Building entity map from {len(entities)} entities")

        for entity in entities:
            # Get entity text and ID
            text = entity.get("text", "")
            entity_id = entity.get("id")

            if not text or not entity_id:
                continue

            # Add to entity map with various forms for better matching

            # Original text
            self._add_to_entity_map(text, entity_id)

            # Normalized version
            normalized = self._normalize_entity_name(text)
            self._add_to_entity_map(normalized, entity_id)

            # Handle variants (like "CR (Calorie Restriction)")
            if '(' in text:
                base_term = text.split('(')[0].strip()
                if base_term:
                    self._add_to_entity_map(base_term, entity_id)

                # Also try to extract the content inside parentheses as an abbreviation
                abbr_match = re.search(r'\(([^)]+)\)', text)
                if abbr_match:
                    abbreviation = abbr_match.group(1).strip()
                    if abbreviation and len(abbreviation) < 10:  # Only use short abbreviations
                        self._add_to_entity_map(abbreviation, entity_id)

    def _add_to_entity_map(self, text: str, entity_id: str) -> None:
        """
        Add entity to the entity map with case normalization.

        Args:
            text: Entity text
            entity_id: Entity ID
        """
        if not text:
            return

        # Normalize case for keys
        key = text.lower().strip()
        if key:
            self.entity_map[key] = entity_id

    def _resolve_relationship_ids(self, relationships: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Resolve entity IDs for relationships.

        Args:
            relationships: List of relationship dictionaries

        Returns:
            list: Relationships with resolved IDs
        """
        processed = []

        # Log entity map size for debugging
        self.logger.debug(f"Entity map contains {len(self.entity_map)} entries for relationship resolution")

        for rel in relationships:
            # Skip if already has both source_id and target_id
            if rel.get("source_id") and rel.get("target_id"):
                processed.append(rel)
                continue

            # Get source and target text
            source_text = rel.get("source_text", "")
            target_text = rel.get("target_text", "")

            # Skip invalid relationships
            if not source_text or not target_text:
                self.logger.warning(f"Skipping relationship with missing source or target text: {rel}")
                continue

            # Resolve source_id if needed
            if not rel.get("source_id"):
                source_id = self._get_entity_id(source_text)
                if source_id:
                    rel["source_id"] = source_id
                    self.logger.debug(f"Resolved source_id for '{source_text}': {source_id}")
                else:
                    # Try additional normalization for better matching
                    normalized_source = self._additional_normalization(source_text)
                    if normalized_source != source_text.lower().strip():
                        source_id = self._get_entity_id(normalized_source)
                        if source_id:
                            rel["source_id"] = source_id
                            self.logger.debug(f"Resolved source_id for '{source_text}' using additional normalization: {source_id}")
                        else:
                            self.logger.warning(f"Could not resolve source_id for text: {source_text} (normalized: {normalized_source})")
                    else:
                        self.logger.warning(f"Could not resolve source_id for text: {source_text}")

            # Resolve target_id if needed
            if not rel.get("target_id"):
                target_id = self._get_entity_id(target_text)
                if target_id:
                    rel["target_id"] = target_id
                    self.logger.debug(f"Resolved target_id for '{target_text}': {target_id}")
                else:
                    # Try additional normalization for better matching
                    normalized_target = self._additional_normalization(target_text)
                    if normalized_target != target_text.lower().strip():
                        target_id = self._get_entity_id(normalized_target)
                        if target_id:
                            rel["target_id"] = target_id
                            self.logger.debug(f"Resolved target_id for '{target_text}' using additional normalization: {target_id}")
                        else:
                            self.logger.warning(f"Could not resolve target_id for text: {target_text} (normalized: {normalized_target})")
                    else:
                        self.logger.warning(f"Could not resolve target_id for text: {target_text}")

            # Only include relationships with both IDs
            if rel.get("source_id") and rel.get("target_id"):
                processed.append(rel)
            else:
                missing = []
                if not rel.get("source_id"):
                    missing.append("source_id")
                if not rel.get("target_id"):
                    missing.append("target_id")

                self.logger.warning(f"Skipping relationship missing {', '.join(missing)}: {source_text} --{rel.get('type')}--> {target_text}")

        return processed

    def _get_entity_id(self, text: str) -> Optional[str]:
        """
        Get entity ID from text, trying various normalization techniques.

        Args:
            text: Entity text

        Returns:
            str: Entity ID if found, None otherwise
        """
        if not text:
            return None

        # Check if we already have this in the cache
        normalized = text.lower().strip()
        if normalized in self.entity_cache:
            return self.entity_cache[normalized]

        # Try direct lookup
        if normalized in self.entity_map:
            self.entity_cache[normalized] = self.entity_map[normalized]
            return self.entity_map[normalized]

        # Try normalized version
        full_normalized = self._normalize_entity_name(text)
        if full_normalized in self.entity_map:
            self.entity_cache[normalized] = self.entity_map[full_normalized]
            return self.entity_map[full_normalized]

        # Try removing text in parentheses
        if '(' in normalized:
            base_term = normalized.split('(')[0].strip()
            if base_term in self.entity_map:
                self.entity_cache[normalized] = self.entity_map[base_term]
                return self.entity_map[base_term]

        # Try as abbreviation - check if it matches any abbreviation
        for entity_text, entity_id in self.entity_map.items():
            abbr_match = re.search(r'\(([^)]+)\)', entity_text)
            if abbr_match:
                abbreviation = abbr_match.group(1).strip().lower()
                if abbreviation == normalized:
                    self.entity_cache[normalized] = entity_id
                    return entity_id

        # Not found after all attempts
        return None

    def _normalize_entity_name(self, name: str) -> str:
        """
        Normalize entity name for better matching.

        Args:
            name: Entity name

        Returns:
            str: Normalized name
        """
        if not name:
            return ""

        # Lowercase, trim whitespace
        normalized = name.lower().strip()

        # Remove parenthetical content
        normalized = re.sub(r'\s*\([^)]*\)', '', normalized)

        # Replace multiple spaces with a single space
        normalized = re.sub(r'\s+', ' ', normalized)

        # Remove common prefixes like "the", "a", "an"
        normalized = re.sub(r'^(the|a|an)\s+', '', normalized)

        return normalized

    def _normalize_entity_type(self, entity_type: str) -> str:
        """
        Normalize entity type to ensure consistency.

        Args:
            entity_type: Entity type to normalize

        Returns:
            str: Normalized entity type
        """
        if not entity_type:
            return "unknown"

        # Lowercase and trim whitespace
        normalized = entity_type.lower().strip()

        # Define standard entity types (singular form)
        standard_types = {
            # Singular to plural mapping
            "biomarker": "biomarkers",
            "disease": "diseases",
            "intervention": "interventions",
            "supplement": "supplements",
            "tracker": "trackers",
            "lifestyle factor": "lifestyle factors",
            "genetic factor": "genetic factors",
            "organization": "organizations/people",
            "person": "organizations/people",
            "organization/person": "organizations/people",

            # Ensure plural forms map to themselves
            "biomarkers": "biomarkers",
            "diseases": "diseases",
            "interventions": "interventions",
            "supplements": "supplements",
            "trackers": "trackers",
            "lifestyle factors": "lifestyle factors",
            "genetic factors": "genetic factors",
            "organizations/people": "organizations/people"
        }

        # Check for exact match in standard types
        if normalized in standard_types:
            return standard_types[normalized]

        # Check for singular/plural forms
        if normalized.endswith('s') and normalized[:-1] in standard_types:
            # Handle plural form (e.g., "biomarkers" -> "biomarkers")
            singular = normalized[:-1]
            return standard_types[singular]
        elif normalized + 's' in standard_types:
            # Handle singular form (e.g., "biomarker" -> "biomarkers")
            plural = normalized + 's'
            return standard_types[plural]

        # If no match found, return the original type
        return entity_type

    def _additional_normalization(self, text: str) -> str:
        """
        Apply additional normalization techniques for better entity matching.

        Args:
            text: Entity text to normalize

        Returns:
            str: Normalized text
        """
        if not text:
            return ""

        # Start with basic normalization
        normalized = text.lower().strip()

        # Remove common words that might be added/removed
        for word in ["the", "a", "an", "of", "and", "or", "in", "on", "at", "to", "for"]:
            normalized = re.sub(f"\b{word}\b", "", normalized)

        # Remove punctuation
        normalized = re.sub(r'[.,;:!?()\[\]{}"\'-]', "", normalized)

        # Replace multiple spaces with a single space
        normalized = re.sub(r'\s+', ' ', normalized).strip()

        # Try plural/singular forms
        if normalized.endswith("s"):
            # Try singular form
            singular = normalized[:-1]
            if singular in self.entity_map:
                return singular
        else:
            # Try plural form
            plural = normalized + "s"
            if plural in self.entity_map:
                return plural

        return normalized
