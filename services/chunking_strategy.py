"""
Chunking strategies for different use cases.
"""
import logging
from typing import List, Any, Dict, Optional
from enum import Enum

from unstructured.chunking.title import chunk_by_title
from unstructured.chunking.basic import chunk_elements

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class ChunkingPurpose(Enum):
    """Enum for different chunking purposes."""
    RAG = "rag"  # Retrieval Augmented Generation
    KG = "kg"    # Knowledge Graph


class ChunkingStrategy:
    """Service for applying different chunking strategies based on purpose."""

    # Minimum chunk size in characters
    MIN_CHUNK_SIZE = 100

    @staticmethod
    def chunk_for_purpose(elements: List[Any], purpose: ChunkingPurpose) -> List[Dict[str, Any]]:
        """
        Apply the appropriate chunking strategy based on purpose.

        Args:
            elements: List of document elements from unstructured.io
            purpose: Purpose of chunking (RAG or KG)

        Returns:
            List[Dict[str, Any]]: List of chunks with text and metadata
        """
        if purpose == ChunkingPurpose.RAG:
            return ChunkingStrategy.chunk_for_rag(elements)
        elif purpose == ChunkingPurpose.KG:
            return ChunkingStrategy.chunk_for_kg(elements)
        else:
            logger.warning(f"Unknown chunking purpose: {purpose}. Using RAG chunking as default.")
            return ChunkingStrategy.chunk_for_rag(elements)

    @staticmethod
    def chunk_for_rag(elements: List[Any]) -> List[Dict[str, Any]]:
        """
        Chunk elements optimized for RAG (Retrieval Augmented Generation).
        Creates smaller chunks ideal for precise retrieval.

        Args:
            elements: List of document elements from unstructured.io

        Returns:
            List[Dict[str, Any]]: List of chunks with text and metadata
        """
        logger.info("Applying RAG-optimized chunking strategy")

        # Apply RAG-optimized chunking
        chunked_elements = chunk_elements(
            elements,
            max_characters=2000,  # Maximum characters per chunk
            overlap=100  # Small overlap to maintain context across chunks
        )

        # Convert to standard chunk format
        chunks = []
        for i, element in enumerate(chunked_elements):
            # Extract metadata from element
            element_metadata = {
                "element_type": element.category,
                "element_id": i,
                "page_number": getattr(element, "page_number", None),
                "chunking_strategy": "rag",
            }

            # Get text content
            text = str(element).strip()

            # Skip chunks that are too small
            if len(text) < ChunkingStrategy.MIN_CHUNK_SIZE:
                logger.info(f"Skipping small RAG chunk with {len(text)} characters")
                continue

            # Add metadata from element if available
            if hasattr(element, "metadata"):
                if hasattr(element.metadata, "to_dict"):
                    element_metadata.update(element.metadata.to_dict())
                elif isinstance(element.metadata, dict):
                    element_metadata.update(element.metadata)

            # Add to chunks
            chunks.append({
                "text": text,
                "metadata": element_metadata
            })

        logger.info(f"Created {len(chunks)} RAG-optimized chunks")
        return chunks

    @staticmethod
    def chunk_for_kg(elements: List[Any]) -> List[Dict[str, Any]]:
        """
        Chunk elements optimized for KG (Knowledge Graph).
        Creates larger chunks that preserve semantic units for better entity extraction.

        Args:
            elements: List of document elements from unstructured.io

        Returns:
            List[Dict[str, Any]]: List of chunks with text and metadata
        """
        logger.info("Applying KG-optimized chunking strategy")

        # Apply KG-optimized chunking using chunk_by_title
        chunked_elements = chunk_by_title(
            elements,
            max_characters=8000  # Much larger chunks (~1500-2000 tokens)
        )

        # Convert to standard chunk format
        chunks = []
        for i, element in enumerate(chunked_elements):
            # Extract metadata from element
            element_metadata = {
                "element_type": element.category,
                "element_id": i,
                "page_number": getattr(element, "page_number", None),
                "chunking_strategy": "kg",
            }

            # Get text content
            text = str(element).strip()

            # Skip chunks that are too small
            if len(text) < ChunkingStrategy.MIN_CHUNK_SIZE:
                logger.info(f"Skipping small KG chunk with {len(text)} characters")
                continue

            # Add metadata from element if available
            if hasattr(element, "metadata"):
                if hasattr(element.metadata, "to_dict"):
                    element_metadata.update(element.metadata.to_dict())
                elif isinstance(element.metadata, dict):
                    element_metadata.update(element.metadata)

            # Add to chunks
            chunks.append({
                "text": text,
                "metadata": element_metadata
            })

        logger.info(f"Created {len(chunks)} KG-optimized chunks")
        return chunks

    @staticmethod
    def chunk_for_kg_alternative(elements: List[Any]) -> List[Dict[str, Any]]:
        """
        Alternative chunking strategy for KG using chunk_elements.

        Args:
            elements: List of document elements from unstructured.io

        Returns:
            List[Dict[str, Any]]: List of chunks with text and metadata
        """
        logger.info("Applying alternative KG-optimized chunking strategy")

        # Apply alternative KG-optimized chunking
        chunked_elements = chunk_elements(
            elements,
            max_characters=10000,  # Maximum characters per chunk
            overlap=200  # Larger overlap to ensure entity context spans chunks
        )

        # Convert to standard chunk format
        chunks = []
        for i, element in enumerate(chunked_elements):
            # Extract metadata from element
            element_metadata = {
                "element_type": element.category,
                "element_id": i,
                "page_number": getattr(element, "page_number", None),
                "chunking_strategy": "kg_alt",
            }

            # Get text content
            text = str(element).strip()

            # Skip chunks that are too small
            if len(text) < ChunkingStrategy.MIN_CHUNK_SIZE:
                logger.info(f"Skipping small KG (alt) chunk with {len(text)} characters")
                continue

            # Add metadata from element if available
            if hasattr(element, "metadata"):
                if hasattr(element.metadata, "to_dict"):
                    element_metadata.update(element.metadata.to_dict())
                elif isinstance(element.metadata, dict):
                    element_metadata.update(element.metadata)

            # Add to chunks
            chunks.append({
                "text": text,
                "metadata": element_metadata
            })

        logger.info(f"Created {len(chunks)} alternative KG-optimized chunks")
        return chunks
