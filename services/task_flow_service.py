"""
Task Flow Service for managing document processing workflows.

This service provides a unified interface for handling document processing tasks,
standardizing the workflow between different document types including regular documents
and PubMed articles.
"""
import logging
import uuid
from typing import Dict, Any, Union, Optional, List

from common.task_definitions import TaskType, TaskStatus, TaskTransitionMap, StandardTaskParams
from transport.data_transport import DataTransport

# Configure logging
logger = logging.getLogger(__name__)


class TaskFlowService:
    """
    Service for managing document processing workflows.
    
    This service handles the creation and transition of tasks in a standardized way,
    ensuring that all document types follow a consistent processing flow.
    """
    
    def __init__(self, transport=None):
        """Initialize with an optional transport instance."""
        self.transport = transport
    
    def _get_transport(self):
        """Get or create a DataTransport instance."""
        if self.transport is not None:
            return self.transport
        return DataTransport()
    
    def create_task(
        self,
        document_id: Union[str, uuid.UUID],
        task_type: str,
        source_type: str,
        metadata: Optional[Dict[str, Any]] = None,
        celery_task_id: Optional[str] = None,
        process_options: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Create a standardized processing task.
        
        Args:
            document_id: Document ID
            task_type: Type of processing task (from TaskType enum)
            source_type: Source type of the document (upload, url, pubmed)
            metadata: Additional metadata
            celery_task_id: Celery task ID
            process_options: Processing options
            
        Returns:
            Dict with task information
        """
        with self._get_transport() as transport:
            # Create standardized task metadata
            task_metadata = {}
            
            # Add source information
            if source_type:
                source_metadata = {"source_type": source_type}
                if metadata and "source_identifier" in metadata:
                    source_metadata["source_identifier"] = metadata["source_identifier"]
                task_metadata["source"] = source_metadata
            
            # Add process options
            if process_options:
                task_metadata["process_options"] = process_options
            
            # Add any additional metadata
            if metadata:
                # Remove source_identifier if it exists to avoid duplication
                if "source_identifier" in metadata:
                    metadata = {k: v for k, v in metadata.items() if k != "source_identifier"}
                # Add remaining metadata
                if metadata:
                    task_metadata.update(metadata)
            
            # Create the task
            task = transport.create_processing_task(
                document_id=document_id,
                task_type=task_type,
                celery_task_id=celery_task_id,
                metadata=task_metadata
            )
            
            return task
    
    def update_task_status(
        self,
        task_id: Union[str, uuid.UUID],
        status: str,
        result: Optional[Dict[str, Any]] = None,
        error: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Update a task's status with validation against allowed transitions.
        
        Args:
            task_id: Task ID
            status: New status
            result: Optional result data
            error: Optional error message
            
        Returns:
            Dict with updated task information
        """
        with self._get_transport() as transport:
            # Get current task
            task = transport.get_processing_task(task_id)
            if not task:
                raise ValueError(f"Task {task_id} not found")
            
            current_status = task.get("status")
            task_type = task.get("task_type")
            
            # Validate transition
            if not TaskTransitionMap.is_valid_transition(current_status, status, task_type):
                logger.warning(
                    f"Invalid status transition from {current_status} to {status} "
                    f"for task {task_id} of type {task_type}"
                )
                # Still allow the transition but log the warning
            
            # Update the task
            updated_task = transport.update_task_status(
                task_id=task_id,
                status=status,
                result=result,
                error=error
            )
            
            return updated_task
    
    def determine_document_task_type(self, document_metadata: Dict[str, Any]) -> str:
        """
        Determine the appropriate task type based on document metadata.
        
        Args:
            document_metadata: Document metadata
            
        Returns:
            Task type from TaskType enum
        """
        # Extract source type
        source_type = None
        if document_metadata and isinstance(document_metadata, dict):
            if "source" in document_metadata and isinstance(document_metadata["source"], dict):
                source_type = document_metadata["source"].get("type")
            elif "source_type" in document_metadata:
                source_type = document_metadata["source_type"]
        
        # Map source type to task type
        if source_type == "pubmed":
            return TaskType.PUBMED_PROCESSING.value
        elif source_type == "gdrive":
            return TaskType.DOCUMENT_PROCESSING.value
        
        # Default based on content type if available
        content_type = document_metadata.get("content_type")
        if content_type:
            if "pdf" in content_type:
                return TaskType.DOCUMENT_PROCESSING_PDF.value
            elif "epub" in content_type:
                return TaskType.DOCUMENT_PROCESSING_EPUB.value
            elif "text" in content_type:
                return TaskType.DOCUMENT_PROCESSING_TEXT.value
            elif "html" in content_type or content_type.startswith("application/xhtml"):
                return TaskType.DOCUMENT_PROCESSING_HTML.value
        
        # Default if nothing else matched
        return TaskType.DOCUMENT_PROCESSING.value
    
    def create_ingestion_task(
        self,
        document_id: Union[str, uuid.UUID],
        source_type: str,
        source_identifier: Optional[str] = None,
        celery_task_id: Optional[str] = None,
        additional_metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create a standardized ingestion task.
        
        Args:
            document_id: Document ID
            source_type: Source type (upload, url, pubmed)
            source_identifier: Source identifier (e.g., PMID for PubMed)
            celery_task_id: Celery task ID
            additional_metadata: Additional metadata
            
        Returns:
            Dict with task information
        """
        # Determine ingestion task type
        task_type = None
        if source_type == "pubmed":
            task_type = TaskType.PUBMED_INGESTION.value
        elif source_type == "gdrive":
            task_type = TaskType.GDRIVE_INGESTION.value
        elif source_type == "url":
            task_type = TaskType.URL_INGESTION.value
        else:
            task_type = TaskType.FILE_UPLOAD_INGESTION.value
        
        # Create base metadata
        metadata = {"source_identifier": source_identifier} if source_identifier else {}
        
        # Add additional metadata
        if additional_metadata:
            metadata.update(additional_metadata)
        
        # Create the task
        return self.create_task(
            document_id=document_id,
            task_type=task_type,
            source_type=source_type,
            metadata=metadata,
            celery_task_id=celery_task_id
        )
    
    def create_document_processing_task(
        self,
        document_id: Union[str, uuid.UUID],
        source_type: str,
        build_knowledge_graph: bool = True,
        chunking_strategy: str = "standard",
        use_batch_api: bool = True,
        priority: str = "normal",
        additional_metadata: Optional[Dict[str, Any]] = None,
        celery_task_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Create a standardized document processing task.
        
        Args:
            document_id: Document ID
            source_type: Source type (upload, url, pubmed)
            build_knowledge_graph: Whether to build knowledge graph
            chunking_strategy: Chunking strategy (standard, single_chunk, custom)
            use_batch_api: Whether to use batch API
            priority: Processing priority (normal, high, low)
            additional_metadata: Additional metadata
            celery_task_id: Celery task ID
            
        Returns:
            Dict with task information
        """
        # Determine document task type based on source type
        task_type = None
        if source_type == "pubmed":
            task_type = TaskType.PUBMED_PROCESSING.value
            if chunking_strategy == "standard":
                # Override chunking strategy for PubMed to ensure we use single chunk for KG
                chunking_strategy = "single_chunk"
        else:
            # Get document metadata to determine the appropriate task type
            with self._get_transport() as transport:
                document = transport.db_client.get_document(document_id)
                if document and document.doc_metadata:
                    task_type = self.determine_document_task_type(document.doc_metadata)
                else:
                    task_type = TaskType.DOCUMENT_PROCESSING.value
        
        # Create process options
        process_options = StandardTaskParams.create_process_options(
            build_knowledge_graph=build_knowledge_graph,
            chunking_strategy=chunking_strategy,
            use_batch_api=use_batch_api,
            priority=priority
        )
        
        # Create metadata
        metadata = {}
        if additional_metadata:
            metadata.update(additional_metadata)
        
        # Create the task
        return self.create_task(
            document_id=document_id,
            task_type=task_type,
            source_type=source_type,
            metadata=metadata,
            celery_task_id=celery_task_id,
            process_options=process_options
        )
    
    def create_knowledge_graph_task(
        self,
        document_id: Union[str, uuid.UUID],
        use_batch_api: bool = True,
        celery_task_id: Optional[str] = None,
        additional_metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create a standardized knowledge graph task.
        
        Args:
            document_id: Document ID
            use_batch_api: Whether to use batch API
            celery_task_id: Celery task ID
            additional_metadata: Additional metadata
            
        Returns:
            Dict with task information
        """
        # Create process options
        process_options = {
            "use_batch_api": use_batch_api
        }
        
        # Create base metadata
        metadata = {}
        
        # Add additional metadata
        if additional_metadata:
            metadata.update(additional_metadata)
        
        # Determine source type
        source_type = None
        with self._get_transport() as transport:
            document = transport.db_client.get_document(document_id)
            if document and document.doc_metadata:
                doc_metadata = document.doc_metadata
                if isinstance(doc_metadata, dict):
                    if "source" in doc_metadata and isinstance(doc_metadata["source"], dict):
                        source_type = doc_metadata["source"].get("type")
                    elif "source_type" in doc_metadata:
                        source_type = doc_metadata["source_type"]
        
        # Create the task
        return self.create_task(
            document_id=document_id,
            task_type=TaskType.KNOWLEDGE_GRAPH.value,
            source_type=source_type or "unknown",
            metadata=metadata,
            celery_task_id=celery_task_id,
            process_options=process_options
        )
    
    def get_task_process_options(self, task_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
        """
        Get process options from a task.
        
        Args:
            task_id: Task ID
            
        Returns:
            Dict with process options
        """
        with self._get_transport() as transport:
            task = transport.get_processing_task(task_id)
            if not task:
                raise ValueError(f"Task {task_id} not found")
            
            # Extract process options
            task_metadata = task.get("task_metadata", {})
            if not task_metadata:
                return {}
            
            return task_metadata.get("process_options", {})
    
    def update_document_status(
        self, 
        document_id: Union[str, uuid.UUID],
        status: str
    ) -> Dict[str, Any]:
        """
        Update a document's status.
        
        Args:
            document_id: Document ID
            status: New status
            
        Returns:
            Dict with document information
        """
        with self._get_transport() as transport:
            document = transport.db_client.update_document_status(
                document_id=document_id,
                status=status
            )
            return document.to_dict() if document else None