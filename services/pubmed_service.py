# services/pubmed_service.py

import logging
import time
import tempfile
import uuid
import os
from typing import List, Dict, Any
from datetime import datetime

from common.config import settings
from transport.data_transport import DataTransport
from transport.storage_client import StorageClient
from transport.database_client import DatabaseClient

# Try to import Bio, but don't fail if it's not available
try:
    from Bio import Entrez
except ImportError:
    Entrez = None

# Import PubMed utilities
try:
    from services.pubmed.pubmed_utils_extended import fetch_data
except ImportError:
    # Create a stub function if the module is not available
    def fetch_data(pmid, error_pmids):
        logging.error(f"PubMed utilities not available. Cannot fetch data for PMID: {pmid}")
        error_pmids.append(pmid)
        return None

# Configure logging
logger = logging.getLogger(__name__)

class PubMedService:

    def __enter__(self):
        # Initialize any resources needed
        return self

    def __exit__(self, *_):
        # Clean up resources
        pass

    def __init__(self):
        """Initialize PubMed service."""
        self.storage_client = StorageClient()
        self.logger = logger  # Use the module-level logger

        # Set default email for Entrez
        if Entrez is not None:
            Entrez.email = "<EMAIL>"  # Default email
        else:
            self.logger.error("Bio module not found. Please install it with 'pip install biopython'")
            raise ImportError("Bio module not found. Please install it with 'pip install biopython'")

    def set_entrez_email(self, email: str):
        """
        Set the email address for Entrez API requests.

        Parameters:
        email (str): The email address to use for Entrez API requests.
        """
        if Entrez is not None:
            Entrez.email = email
            logger.info(f"Entrez email set to: {email}")
        else:
            logger.error("Bio.Entrez module not available. Cannot set email.")
            raise ImportError("Bio.Entrez module not available")

    def search_articles(self, query: str, max_results: int = 20) -> List[Dict[str, Any]]:
        """
        Search PubMed for articles matching the query.

        Args:
            query: Search query
            max_results: Maximum number of results to return

        Returns:
            List[Dict[str, Any]]: List of article details
        """
        try:
            logger.info(f"Searching PubMed for articles matching query: {query}")

            # Use the search method to get PMIDs
            pmids = self.search(query=query, start_year=2020, end_year=2023)

            # Limit results based on max_results
            pmids = pmids[:max_results] if max_results > 0 else pmids

            # Get details for each PMID
            results = []
            for pmid in pmids:
                article_details = self.get_article_details(pmid)
                if article_details:
                    results.append(article_details)

            logger.info(f"Found {len(results)} articles for query '{query}'")
            return results
        except Exception as e:
            logger.error(f"Error searching PubMed: {str(e)}", exc_info=True)
            return []

    def search(self, query: str, start_year: int, end_year: int) -> List[str]:
        """
        Retrieve PMIDs for a PubMed query by splitting the search across years.

        Parameters:
        query (str): Base query string (without date limits)
        start_year (int): Earliest publication year to include
        end_year (int): Latest publication year to include

        Returns:
        all_pmids (list): List of PMIDs (as strings) from all years.
        """
        all_pmids = []
        for year in range(start_year, end_year + 1):
            # Construct a year-limited query using the publication date field [pdat]
            query_year = f'({query}) AND {year}[pdat]'
            # First, get the count for this year
            handle = Entrez.esearch(db="pubmed", term=query_year, retmax=0)
            record = Entrez.read(handle)
            count_year = int(record["Count"])
            print(f"Year {year}: {count_year} results")

            if count_year == 0:
                continue

            retstart = 0
            retmax = 10000  # max allowed per query
            while retstart < count_year:
                print(f"Fetching records {retstart + 1} to {min(retstart + retmax, count_year)} for year {year}")
                handle = Entrez.esearch(
                    db="pubmed",
                    term=query_year,
                    retmax=retmax,
                    retstart=retstart,
                    retmode="xml"
                )
                record = Entrez.read(handle)
                pmid_batch = record["IdList"]
                all_pmids.extend(pmid_batch)
                retstart += retmax
        return all_pmids

    def insert_article_details(self, pmid_list: List[str]) -> Dict[str, Any]:
        """
        Fetch article details from PubMed and insert them into the database.

        Args:
            pmid_list: List of PubMed IDs

        Returns:
            Dict[str, Any]: Result information
        """
        try:
            failed_pubmeds = []
            success_count = 0

            # Get details for each PMID
            for pmid in pmid_list:
                try:
                    # Get article details
                    article_details = self.get_article_details(pmid)
                    if not article_details:
                        logger.warning(f"No details found for PMID {pmid}")
                        failed_pubmeds.append(pmid)
                        continue

                    # Check if article already exists
                    with DataTransport() as transport:
                        existing_doc = transport.db_client.get_document_by_source('pubmed', pmid)
                        if existing_doc:
                            logger.info(f"Article with PMID {pmid} already exists in the database (ID: {existing_doc.id}).")
                            continue

                    # Create a temporary file with the abstract
                    abstract = article_details.get("abstract", "")
                    if not abstract:
                        logger.warning(f"No abstract found for PMID {pmid}")
                        failed_pubmeds.append(pmid)
                        continue

                    with tempfile.NamedTemporaryFile(mode="w+", delete=False, suffix=".txt") as temp_file:
                        temp_file.write(abstract)
                        temp_file_path = temp_file.name

                    try:
                        # Store document
                        with open(temp_file_path, "rb") as file_obj:
                            with DataTransport() as transport:
                                doc_result = transport.store_document(
                                    file_obj=file_obj,
                                    filename=f"{pmid}_abstract.txt",
                                    content_type="text/plain",
                                    source_type="pubmed",
                                    source_path=pmid,
                                    metadata=article_details.get("metadata", {}),
                                    title=article_details.get("title", f"PubMed Article {pmid}"),
                                    check_duplicates=True
                                )

                            document_id = doc_result["document_id"]
                            logger.info(f"Stored document for PMID {pmid}, Document ID: {document_id}")

                            # Create processing task
                            with DataTransport() as transport:
                                task_result = transport.create_processing_task(
                                    document_id=document_id,
                                    task_type="pubmed_processing"
                                )

                            logger.info(f"Created processing task {task_result['task_id']} for Document ID: {document_id}")
                            success_count += 1
                    finally:
                        import os
                        if os.path.exists(temp_file_path):
                            os.unlink(temp_file_path)

                except Exception as e:
                    logger.error(f"Error processing PMID {pmid}: {str(e)}", exc_info=True)
                    failed_pubmeds.append(pmid)

            logger.info(f"Inserted {success_count} articles into the database.")
            logger.info(f"Failed to process the following PMIDs: {failed_pubmeds}")

            return {
                "success_count": success_count,
                "failed_count": len(failed_pubmeds),
                "failed_pmids": failed_pubmeds
            }
        except Exception as e:
            logger.error(f"Error inserting articles: {str(e)}", exc_info=True)
            return {
                "success_count": 0,
                "failed_count": len(pmid_list),
                "failed_pmids": pmid_list,
                "error": str(e)
            }

    def fetch_details(self, id_list):
        ids = ','.join(id_list)
        handle = Entrez.efetch(db='pubmed',
                            retmode='xml',
                            id=ids)
        results = Entrez.read(handle)
        return results

    def get_pmcid_from_pmid(self, pmid):
        """Retrieve PMCID for a given PMID."""
        # Fetch the article details
        handle = Entrez.elink(dbfrom="pubmed", db="pmc", linkname="pubmed_pmc", id=pmid)
        record = Entrez.read(handle)
        handle.close()

        # Check if PMCID exists
        if record[0]["LinkSetDb"]:
            pmcid = record[0]["LinkSetDb"][0]["Link"][0]["Id"]
            return f"PMC{pmcid}",
        else:
            return "No PMCID found for this article"

    def get_article_details(self, pmid: str) -> Dict[str, Any]:
        """
        Get details for a PubMed article by its PMID.

        Args:
            pmid: PubMed ID

        Returns:
            Dict[str, Any]: Article details
        """
        try:
            self.logger.info(f"Fetching details for PubMed article with PMID: {pmid}")

            # Check if Entrez is available
            if Entrez is None:
                self.logger.error("Bio.Entrez module not available. Cannot fetch article details.")
                raise ImportError("Bio.Entrez module not available")

            # Fetch article details from PubMed
            handle = Entrez.efetch(db="pubmed", id=pmid, retmode="xml")
            results = Entrez.read(handle)
            handle.close()

            if not results.get("PubmedArticle"):
                self.logger.warning(f"No article found for PMID: {pmid}")
                return {}

            article = results["PubmedArticle"][0]

            # Extract basic information
            title = article["MedlineCitation"]["Article"]["ArticleTitle"]

            # Extract and combine all abstract sections
            abstract = None
            if "Abstract" in article["MedlineCitation"]["Article"]:
                abstract_text_elements = article["MedlineCitation"]["Article"]["Abstract"]["AbstractText"]
                if isinstance(abstract_text_elements, list):
                    # Combine all abstract sections
                    abstract_parts = []
                    for abstract_part in abstract_text_elements:
                        # Check if the abstract part has a label
                        if hasattr(abstract_part, 'attributes') and 'Label' in abstract_part.attributes:
                            label = abstract_part.attributes['Label']
                            abstract_parts.append(f"{label}: {abstract_part}")
                        else:
                            abstract_parts.append(str(abstract_part))
                    abstract = " ".join(abstract_parts)
                else:
                    # Single abstract section
                    abstract = str(abstract_text_elements)

            # Extract publication date
            publication_date = article["MedlineCitation"]["Article"]["Journal"]["JournalIssue"].get("PubDate", {})
            year = publication_date.get("Year", "1900")
            month = publication_date.get("Month", "01")
            day = publication_date.get("Day", "01")

            try:
                publication_date_str = f"{year}-{month}-{day}"
                publication_date = datetime.strptime(publication_date_str, "%Y-%m-%d").date().isoformat()
            except ValueError:
                publication_date = None

            # Extract journal
            journal = article["MedlineCitation"]["Article"]["Journal"]["Title"]

            # Extract authors
            authors = []
            if "AuthorList" in article["MedlineCitation"]["Article"]:
                for author in article["MedlineCitation"]["Article"]["AuthorList"]:
                    if "LastName" in author and "ForeName" in author:
                        authors.append({
                            "last_name": author["LastName"],
                            "fore_name": author["ForeName"],
                            "affiliation": author.get("AffiliationInfo", [{}])[0].get("Affiliation", "No affiliation") if "AffiliationInfo" in author else "No affiliation"
                        })

            # Extract DOI
            doi = None
            if "ELocationID" in article["MedlineCitation"]["Article"]:
                for id_obj in article["MedlineCitation"]["Article"]["ELocationID"]:
                    if id_obj.attributes.get("EIdType") == "doi":
                        doi = str(id_obj)
                        break

            # Compile article details
            article_details = {
                "pmid": pmid,
                "title": title,
                "abstract": abstract,
                "publication_date": publication_date,
                "journal": journal,
                "authors": authors,
                "doi": doi,
                "metadata": {
                    "pmid": pmid,
                    "title": title,
                    "journal": journal,
                    "publication_date": publication_date,
                    "source": "pubmed"
                }
            }

            self.logger.info(f"Successfully fetched details for PMID: {pmid}")
            return article_details

        except Exception as e:
            self.logger.error(f"Error fetching details for PMID {pmid}: {str(e)}", exc_info=True)
            return {}

    def add_articles_to_neo4j(self, articles: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Add a batch of articles to Neo4j."""
        start_time = time.time()
        self.logger.info(f"Adding {len(articles)} articles to Neo4j")

        try:
            with DataTransport() as transport:
                # For now, we'll just log the articles
                self.logger.info(f"Would add {len(articles)} articles to Neo4j")

                elapsed_time = time.time() - start_time
                self.logger.info(f"Successfully processed {len(articles)} articles (time: {elapsed_time:.2f}s)")

                return {
                    "success_count": len(articles),
                    "error_count": 0,
                    "elapsed_time": elapsed_time
                }
        except Exception as e:
            self.logger.error(f"Error adding articles to Neo4j: {str(e)}", exc_info=True)
            return {
                "success_count": 0,
                "error_count": len(articles),
                "elapsed_time": time.time() - start_time,
                "error": str(e)
            }

if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # Start testing
    logger.info("Starting PubMedService local testing")

    try:
        # Initialize service
        service = PubMedService()
        service.set_entrez_email("<EMAIL>")  # Set your email

        # Test configuration
        test_query = "CRISPR Cas9 cancer therapy"
        test_start_year = 2023
        test_end_year = 2023
        max_articles = 5  # Limit for testing

        # Test 1: Search for PMIDs
        logger.info("=== Test 1: Searching for PMIDs ===")
        logger.info(f"Query: '{test_query}' from {test_start_year} to {test_end_year}")
        pmids = service.search(test_query, test_start_year, test_end_year)

        if pmids:
            # Limit for testing
            test_pmids = pmids[:max_articles]
            logger.info(f"Found {len(pmids)} PMIDs, using first {len(test_pmids)} for testing")

            # Test 2: Fetch article details
            logger.info("=== Test 2: Fetching article details ===")

            # Get details for each PMID
            for pmid in test_pmids:
                article_details = service.get_article_details(pmid)
                if article_details:
                    logger.info(f"PMID: {pmid}")
                    logger.info(f"Title: {article_details.get('title')}")
                    abstract = article_details.get('abstract', '')
                    if abstract:
                        logger.info(f"Abstract: {abstract[:100]}...")  # Truncate long abstracts

            # Test 3: Insert articles into database (commented out for safety)
            logger.info("=== Test 3: Inserting articles into database ===")
            logger.info(f"Would insert {len(test_pmids)} articles (commented out for safety)")

            # Uncomment to actually insert data
            # insert_result = service.insert_article_details(test_pmids)
            # logger.info(f"Insertion results: {insert_result}")

            # Test 4: Get PMCID
            logger.info("=== Test 4: Converting PMID to PMCID ===")
            if test_pmids:
                sample_pmid = test_pmids[0]
                pmcid = service.get_pmcid_from_pmid(sample_pmid)
                logger.info(f"PMID {sample_pmid} -> {pmcid}")
        else:
            logger.warning(f"No PMIDs found for query: '{test_query}'")

        logger.info("PubMedService local testing completed successfully")

    except Exception as e:
        logger.error(f"Error during PubMedService testing: {str(e)}", exc_info=True)