# services/pubmed_utils_extended.py
import io
import logging
import requests
from bs4 import BeautifulSoup
from services.pubmed.pubmed_utils import  genericCitationLabelled, pubmed_central_v2, acsPublications, uchicagoPress, nejm, futureMedicine, science_direct, direct_pdf_link

# Import custom logger
from logger.logger import setup_logging
logger = setup_logging(__name__)
logger.setLevel("DEBUG")


def data_from_url(pdf_url: str, name: str, headers: str) -> None:
    """Get file as as data."""
    try:
        response = requests.get(pdf_url, headers=headers, allow_redirects=True)
        path = None
        # likely to be a pdf
        if response.apparent_encoding is None:
            path = f"{name}.pdf"
        else:
            path = f"{name}.html"
        data = io.BytesIO(response.content)
        data_length = len(response.content)
        return {"path": path, "data": data, "data_length": data_length}
    except Exception as e:
        logger.error(f"Error downloading object: {str(e)}", exc_info=True)

def fetch_data(pmid, errorPmids):
    finders, headers = get_finders_headers()
    uri = "http://eutils.ncbi.nlm.nih.gov/entrez/eutils/elink.fcgi?dbfrom=pubmed&id={0}&retmode=ref&cmd=prlinks".format(
        pmid
    )
    success = False
    dontTry = False

    # first, download the html from the page that is on the other side of the pubmed API
    req = requests.get(uri, headers=headers)
    if 'ovid' in req.url:
        logger.debug(
            "** Reprint {0} cannot be fetched as ovid is not supported by the requests package.".format(pmid)
        )
        errorPmids.write("{}\n".format(pmid))
        dontTry = True
        success = True

    soup = BeautifulSoup(req.content, 'lxml')
    #         return soup
    # loop through all finders until it finds one that return the pdf reprint
    if not dontTry:
        for finder in finders:
            logger.debug("Trying {0}".format(finder))
            pdf_url = None
            try:
                pdf_url = eval(finder)(req, soup, headers)
            except Exception as e:
                # Handle the error, e.g., log it, return a default value, etc.
                print(f"An error occurred: {e}")
                continue
            if type(pdf_url) != type(None):
                dataObj = data_from_url(pdf_url.strip(), pmid, headers)
                success = True
                logger.debug("** fetching of reprint {0} succeeded".format(pmid))
                return dataObj

    if not success:
        logger.debug("** Reprint {0} could not be fetched with the current finders.".format(pmid))
        errorPmids.append(pmid)
        return None

def get_finders_headers():
    finders = [
        'genericCitationLabelled',
        'pubmed_central_v2',
        'acsPublications',
        'uchicagoPress',
        'nejm',
        'futureMedicine',
        'science_direct',
        'direct_pdf_link',
    ]

    # Add headers
    headers = requests.utils.default_headers()
    headers['User-Agent'] = 'Mozilla/5.0 (X11; Linux x86_64) ' \
                            'AppleWebKit/537.36 (KHTML, like Gecko) ' \
                            'Chrome/56.0.2924.87 ' \
                            'Safari/537.36'
    return finders, headers