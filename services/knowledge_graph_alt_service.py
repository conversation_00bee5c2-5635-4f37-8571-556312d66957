"""
Knowledge Graph Batch Service for processing document chunks with parallel and local NLP methods.
This service contains methods moved from knowledge_graph_service.py to keep that file focused on
VertexAI batch processing only.
"""
import logging
import uuid
from typing import Dict, List, Any, Optional, Union
from concurrent.futures import Thread<PERSON>oolExecutor

from transport.data_transport import DataTransport
from transport.vertex_ai_client import VertexAIClient
from transport.vertex_ai_batch_client import VertexAIBatch<PERSON>lient
from transport.neo4j_client import Neo4jClient
from services.nlp_service import NLPService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class KnowledgeGraphAltService:
    """Service for knowledge graph batch operations with parallel processing and local NLP."""

    @staticmethod
    def build_knowledge_graph_with_parallel_processing(document_id: Union[str, uuid.UUID], processing_task_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
        """
        Build a knowledge graph from a document using parallel processing with Vertex AI online API.

        Args:
            document_id: Document ID
            processing_task_id: Processing task ID

        Returns:
            Dict[str, Any]: Processing result
        """
        logger.info(f"Building knowledge graph for document with parallel processing: {document_id}")

        try:
            # Initialize clients
            vertex_client = VertexAIClient()
            neo4j_client = Neo4jClient()

            with DataTransport() as transport:
                # Update task status
                transport.update_task_status(
                    task_id=processing_task_id,
                    status="processing",
                    result={
                        "use_parallel": True
                    }
                )

                # Get document metadata
                document = transport.db_client.get_document(document_id)
                if not document:
                    raise ValueError(f"Document not found: {document_id}")

                # Store document in Neo4j
                neo4j_client.store_document(str(document.id), document.to_dict())

                # Get document chunks
                chunks = transport.get_document_chunks(document_id)

                # Process chunks with Vertex AI using parallel processing
                logger.info(f"Processing {len(chunks)} chunks with Vertex AI parallel processing")

                # Update task status with processing info
                transport.update_task_status(
                    task_id=processing_task_id,
                    status="processing",
                    result={
                        "use_parallel": True,
                        "chunks_count": len(chunks)
                    }
                )

                # Process synchronously with parallel API
                chunk_results = vertex_client.process_chunks_in_parallel(chunks)

                # Store results in Neo4j
                total_entities = 0
                total_relationships = 0

                logger.info(f"Storing {len(chunk_results)} chunk results in Neo4j")
                for result in chunk_results:
                    chunk_id = result.get("chunk_id")
                    chunk_data = next((c for c in chunks if str(c["id"]) == chunk_id), {})

                    if not chunk_data:
                        logger.warning(f"Chunk data not found for chunk ID: {chunk_id}")
                        continue

                    # Store chunk in Neo4j
                    neo4j_client.store_chunk(chunk_id, str(document.id), chunk_data)

                    # Process entities
                    entities = result.get("entities", [])
                    if entities:
                        # Add IDs to entities if not present
                        for entity in entities:
                            if "id" not in entity:
                                entity["id"] = str(uuid.uuid4())

                        # Store entities in Neo4j
                        entity_result = neo4j_client.store_entities(chunk_id, entities)
                        total_entities += entity_result.get("entity_count", 0)

                    # Process relationships
                    relationships = result.get("relationships", [])
                    if relationships:
                        # Add IDs to relationships if not present
                        for rel in relationships:
                            if "id" not in rel:
                                rel["id"] = str(uuid.uuid4())

                            # Map source and target indices to entity IDs
                            source_idx = rel.get("source")
                            target_idx = rel.get("target")

                            if source_idx is not None and target_idx is not None and 0 <= source_idx < len(entities) and 0 <= target_idx < len(entities):
                                rel["source_id"] = entities[source_idx].get("id")
                                rel["target_id"] = entities[target_idx].get("id")
                            else:
                                # Skip invalid relationships
                                continue

                        # Filter out relationships with missing source or target IDs
                        valid_relationships = [r for r in relationships if "source_id" in r and "target_id" in r]

                        if valid_relationships:
                            # Store relationships in Neo4j
                            rel_result = neo4j_client.store_relationships(chunk_id, valid_relationships)
                            total_relationships += rel_result.get("relationship_count", 0)

                # Create relationships between chunks based on index order
                logger.info("Creating relationships between chunks based on index order")
                chunk_data_for_relationships = [
                    {"id": str(chunk["id"]), "chunk_index": chunk["chunk_index"]}
                    for chunk in chunks
                ]
                neo4j_client.create_chunk_relationships(str(document.id), chunk_data_for_relationships)

                # Prepare result
                result = {
                    "status": "success",
                    "document_id": str(document_id),
                    "entities_count": total_entities,
                    "relationships_count": total_relationships,
                    "use_parallel": True
                }

                # Update task status
                transport.update_task_status(
                    task_id=processing_task_id,
                    status="completed",
                    result=result
                )

                # Close Neo4j client
                neo4j_client.close()

                return result

        except Exception as e:
            logger.error(f"Error building knowledge graph with parallel processing: {e}")

            # Update task status if possible
            try:
                with DataTransport() as transport:
                    transport.update_task_status(
                        task_id=processing_task_id,
                        status="failed",
                        error=str(e)
                    )
            except Exception as update_error:
                logger.error(f"Error updating task status: {update_error}")

            # Close Neo4j client if it exists
            if 'neo4j_client' in locals():
                neo4j_client.close()

            raise

    @staticmethod
    def build_knowledge_graph_with_local_nlp(document_id: Union[str, uuid.UUID], processing_task_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
        """
        Build a knowledge graph from a document using local NLP service instead of Vertex AI.

        Args:
            document_id: Document ID
            processing_task_id: Processing task ID

        Returns:
            Dict[str, Any]: Processing result
        """
        logger.info(f"Building knowledge graph for document with local NLP: {document_id}")

        try:
            # Initialize Neo4j client
            neo4j_client = Neo4jClient()

            with DataTransport() as transport:
                # Update task status
                transport.update_task_status(
                    task_id=processing_task_id,
                    status="processing",
                    result={
                        "use_local_nlp": True
                    }
                )

                # Get document metadata
                document = transport.db_client.get_document(document_id)
                if not document:
                    raise ValueError(f"Document not found: {document_id}")

                # Store document in Neo4j
                neo4j_client.store_document(str(document.id), document.to_dict())

                # Get document chunks
                chunks = transport.get_document_chunks(document_id)

                # Update task status with processing info
                transport.update_task_status(
                    task_id=processing_task_id,
                    status="processing",
                    result={
                        "use_local_nlp": True,
                        "chunks_count": len(chunks)
                    }
                )

                # Process chunks with local NLP service
                total_entities = 0
                total_relationships = 0

                # Extract entities from each chunk
                chunk_entities_map = {}

                for chunk in chunks:
                    chunk_id = str(chunk["id"])
                    chunk_text = chunk["text"]

                    # Store chunk in Neo4j
                    neo4j_client.store_chunk(chunk_id, str(document.id), chunk)

                    # Extract biomedical entities using local NLP service
                    entities = NLPService.extract_biomedical_entities(chunk_text)

                    # Add IDs to entities if not present
                    for entity in entities:
                        if "id" not in entity:
                            entity["id"] = str(uuid.uuid4())

                    # Store entities in Neo4j
                    if entities:
                        entity_result = neo4j_client.store_entities(chunk_id, entities)
                        total_entities += entity_result.get("entity_count", 0)

                    # Store for relationship extraction
                    chunk_entities_map[chunk_id] = entities

                # Extract relationships between entities
                for chunk_id, entities in chunk_entities_map.items():
                    if len(entities) < 2:
                        continue

                    # Create relationships based on co-occurrence
                    relationships = []
                    for i in range(len(entities)):
                        for j in range(i+1, len(entities)):
                            relationship = {
                                "id": str(uuid.uuid4()),
                                "source_id": entities[i]["id"],
                                "target_id": entities[j]["id"],
                                "type": "CO_OCCURS_WITH",
                                "confidence": 0.8
                            }
                            relationships.append(relationship)

                    # Store relationships in Neo4j
                    if relationships:
                        rel_result = neo4j_client.store_relationships(chunk_id, relationships)
                        total_relationships += rel_result.get("relationship_count", 0)

                # Create relationships between chunks based on index order
                logger.info("Creating relationships between chunks based on index order")
                chunk_data_for_relationships = [
                    {"id": str(chunk["id"]), "chunk_index": chunk["chunk_index"]}
                    for chunk in chunks
                ]
                neo4j_client.create_chunk_relationships(str(document.id), chunk_data_for_relationships)

                # Prepare result
                result = {
                    "status": "success",
                    "document_id": str(document_id),
                    "entities_count": total_entities,
                    "relationships_count": total_relationships,
                    "use_local_nlp": True
                }

                # Update task status
                transport.update_task_status(
                    task_id=processing_task_id,
                    status="completed",
                    result=result
                )

                # Close Neo4j client
                neo4j_client.close()

                return result

        except Exception as e:
            logger.error(f"Error building knowledge graph with local NLP: {e}")

            # Update task status if possible
            try:
                with DataTransport() as transport:
                    transport.update_task_status(
                        task_id=processing_task_id,
                        status="failed",
                        error=str(e)
                    )
            except Exception as update_error:
                logger.error(f"Error updating task status: {update_error}")

            # Close Neo4j client if it exists
            if 'neo4j_client' in locals():
                neo4j_client.close()

            raise
