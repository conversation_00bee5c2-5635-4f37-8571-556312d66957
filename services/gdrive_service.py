"""
Google Drive service for the longevity platform.
"""
import logging
import os
import io
import csv
import uuid
import hashlib
from datetime import datetime
from typing import List, Dict, Any, Optional, Set

# Google Drive API
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.http import MediaIoBaseDownload

from sqlalchemy.orm import Session

from common.config import settings
from common.database import GoogleDriveProcessedFile
from transport.data_transport import DataTransport

# Configure logging
logger = logging.getLogger(__name__)


class GoogleDriveService:
    """
    Service for interacting with Google Drive.
    """
    def __init__(self, credentials_file: Optional[str] = None, db_session: Optional[Session] = None):
        """Initialize Google Drive service."""
        self.logger = logger
        self.credentials_file = credentials_file or settings.GOOGLE_APPLICATION_CREDENTIALS
        self.drive_folder_id = getattr(settings, 'GOOGLE_DRIVE_FOLDER_ID', None)
        self.service = None
        self.db_session = db_session
        self.transport = None

    def __enter__(self):
        """Context manager enter method."""
        # Initialize DataTransport
        self.transport = DataTransport(self.db_session)
        self.transport.__enter__()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit method."""
        if self.transport:
            self.transport.__exit__(exc_type, exc_val, exc_tb)

    def _initialize_service(self):
        """Initialize the Google Drive API service."""
        try:
            if not self.credentials_file:
                raise ValueError("Google Drive credentials file not provided")
            
            if not os.path.exists(self.credentials_file):
                raise FileNotFoundError(f"Google Drive credentials file not found: {self.credentials_file}")

            self.logger.info(f"Initializing Google Drive service with credentials: {self.credentials_file}")
            
            # Create credentials from service account file
            credentials = service_account.Credentials.from_service_account_file(
                self.credentials_file,
                scopes=['https://www.googleapis.com/auth/drive.readonly']
            )
            
            # Build the service
            self.service = build('drive', 'v3', credentials=credentials)
            self.logger.info("Google Drive service initialized successfully")
            return True
        except Exception as e:
            self.logger.error(f"Error initializing Google Drive service: {str(e)}", exc_info=True)
            return False

    def _ensure_service(self):
        """Ensure the Google Drive service is initialized."""
        if self.service is None:
            return self._initialize_service()
        return True

    def _ensure_transport(self):
        """Ensure the DataTransport is initialized."""
        if not self.transport:
            self.transport = DataTransport(self.db_session)
            self.transport.__enter__()
        return True

    def mark_file_processed(self, file_id: str, file_metadata: Dict[str, Any], document_id: Optional[str] = None, 
                            status: str = "processed", result: Optional[Dict[str, Any]] = None):
        """
        Mark a file as processed by adding it to the database.
        
        Args:
            file_id: Google Drive file ID
            file_metadata: File metadata from Google Drive
            document_id: Optional ID of the ingested document
            status: Processing status
            result: Optional processing result
        """
        self._ensure_transport()
        return self.transport.mark_gdrive_file_processed(
            file_id=file_id,
            file_metadata=file_metadata,
            document_id=document_id,
            status=status,
            result=result
        )

    def is_file_processed(self, file_id: str) -> bool:
        """
        Check if a file has already been processed by looking it up in the database.
        
        Args:
            file_id: Google Drive file ID
            
        Returns:
            bool: True if file has been processed, False otherwise
        """
        self._ensure_transport()
        return self.transport.is_gdrive_file_processed(file_id)

    def list_unprocessed_files(self, folder_id: Optional[str] = None, recursive: bool = True) -> List[Dict[str, Any]]:
        """
        List all unprocessed files in the specified Google Drive folder.
        
        Args:
            folder_id: Optional Google Drive folder ID. If None, uses the default folder.
            recursive: Whether to recursively search for files in subfolders
            
        Returns:
            List[Dict[str, Any]]: List of file metadata for unprocessed files
        """
        if not self._ensure_service():
            self.logger.error("Google Drive service not initialized")
            return []
            
        folder_id = folder_id or self.drive_folder_id
        if not folder_id:
            self.logger.error("No Google Drive folder ID provided")
            return []
            
        try:
            self.logger.info(f"Listing files in Google Drive folder: {folder_id} (recursive: {recursive})")
            
            all_files = []
            
            # First, get direct files from the folder
            query = f"'{folder_id}' in parents and trashed = false"
            results = self.service.files().list(
                q=query,
                fields="files(id, name, mimeType, modifiedTime, md5Checksum, parents)",
                pageSize=1000
            ).execute()
            
            files = results.get('files', [])
            self.logger.info(f"Found {len(files)} files directly in folder {folder_id}")
            
            # Add all non-folder files to our list
            for file in files:
                if file.get('mimeType') != 'application/vnd.google-apps.folder':
                    all_files.append(file)
            
            # If recursive mode is on, find all subfolders and search them too
            if recursive:
                # Get all subfolder IDs
                subfolders = [file for file in files if file.get('mimeType') == 'application/vnd.google-apps.folder']
                self.logger.info(f"Found {len(subfolders)} subfolders in folder {folder_id}")
                
                # Recursively search each subfolder
                for subfolder in subfolders:
                    subfolder_id = subfolder.get('id')
                    subfolder_name = subfolder.get('name')
                    self.logger.info(f"Searching subfolder: {subfolder_name} (ID: {subfolder_id})")
                    
                    # Recursively search this subfolder
                    subfolder_files = self.list_unprocessed_files(subfolder_id, recursive=True)
                    
                    # Add all files found in the subfolder to our list
                    all_files.extend(subfolder_files)
            
            self.logger.info(f"Found total of {len(all_files)} files in folder {folder_id} and its subfolders")
            
            self._ensure_transport()
            
            # Get processed file IDs from the database using DataTransport
            processed_file_ids = self.transport.get_processed_gdrive_file_ids()
            
            # Filter out already processed files
            unprocessed_files = [
                file for file in all_files if file.get('id') not in processed_file_ids
            ]
            
            self.logger.info(f"Found {len(unprocessed_files)} unprocessed files")
            return unprocessed_files
        except Exception as e:
            self.logger.error(f"Error listing files in Google Drive folder: {str(e)}", exc_info=True)
            return []

    def get_processed_file_stats(self) -> Dict[str, Any]:
        """
        Get statistics about processed files from the database.
        
        Returns:
            Dict[str, Any]: Statistics about processed files
        """
        self._ensure_transport()
        return self.transport.get_gdrive_processed_file_stats()

    def download_file(self, file_id: str) -> Optional[io.BytesIO]:
        """
        Download a file from Google Drive.
        
        Args:
            file_id: Google Drive file ID
            
        Returns:
            io.BytesIO: File contents as BytesIO object, or None on error
        """
        if not self._ensure_service():
            return None
            
        try:
            self.logger.info(f"Downloading file with ID: {file_id}")
            
            # Get the file metadata
            file_metadata = self.service.files().get(fileId=file_id).execute()
            file_name = file_metadata.get('name', 'unknown')
            
            # Create request to download the file
            request = self.service.files().get_media(fileId=file_id)
            
            # Create a BytesIO object to store the downloaded file
            file_content = io.BytesIO()
            
            # Download the file
            downloader = MediaIoBaseDownload(file_content, request)
            done = False
            while not done:
                status, done = downloader.next_chunk()
                self.logger.info(f"Download progress: {int(status.progress() * 100)}%")
            
            # Reset the pointer to the beginning of the file
            file_content.seek(0)
            
            self.logger.info(f"Successfully downloaded file: {file_name}")
            return file_content
        except Exception as e:
            self.logger.error(f"Error downloading file: {str(e)}", exc_info=True)
            return None

    def process_url_csv_file(self, file_id: str, file_content: io.BytesIO, file_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a _url.csv file containing URLs to ingest.
        
        Args:
            file_id: Google Drive file ID
            file_content: File content as BytesIO
            file_metadata: File metadata from Google Drive
            
        Returns:
            Dict[str, Any]: Processing results
        """
        try:
            self.logger.info(f"Processing _url.csv file: {file_id}")
            
            # Read the CSV file
            file_content.seek(0)
            csv_reader = csv.reader(io.TextIOWrapper(file_content, encoding='utf-8'))
            
            # Extract URLs (assuming first column contains URLs)
            urls = []
            for row in csv_reader:
                if row and len(row) > 0 and row[0].strip():
                    url = row[0].strip()
                    if url.startswith('http'):
                        urls.append(url)
            
            self.logger.info(f"Found {len(urls)} URLs in CSV file")
            
            # Ingest each URL
            queued_tasks = []
            failed_urls = []
            
            for url in urls:
                try:
                    self.logger.info(f"Queueing ingestion for URL: {url}")
                    
                    # Queue the URL ingestion task
                    ingest_result = self._ingest_url(url)
                    
                    if ingest_result and ingest_result.get("task_id"):
                        queued_tasks.append({
                            "url": url,
                            "task_id": ingest_result["task_id"]
                        })
                    else:
                        failed_urls.append(url)
                except Exception as e:
                    self.logger.error(f"Error queueing ingestion for URL {url}: {str(e)}", exc_info=True)
                    failed_urls.append(url)
            
            success_count = len(queued_tasks)
            self.logger.info(f"Queued {success_count} URL ingestions, {len(failed_urls)} failed to queue")
            
            # Create result data
            result = {
                "file_id": file_id,
                "total_urls": len(urls),
                "queued_count": success_count,
                "failed_to_queue_count": len(failed_urls),
                "failed_urls": failed_urls,
                "queued_tasks": queued_tasks
            }
            
            # Mark the file as processed (or partially processed if some failed to queue)
            status = "processed" if len(failed_urls) == 0 else "partial_failure"
            self.mark_file_processed(
                file_id=file_id,
                file_metadata=file_metadata,
                status=status,
                result=result
            )
            
            return result
        except Exception as e:
            self.logger.error(f"Error processing URL CSV file {file_id}: {str(e)}", exc_info=True)
            
            # Mark the file as failed in the database
            self.mark_file_processed(
                file_id=file_id,
                file_metadata=file_metadata,
                status="failed",
                result={"error": str(e)}
            )
            
            return {
                "file_id": file_id,
                "error": str(e),
                "queued_count": 0,
                "failed_to_queue_count": len(urls)
            }

    def process_document_file(self, file_id: str, file_content: io.BytesIO, file_metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a document file from Google Drive.
        
        Args:
            file_id: Google Drive file ID
            file_content: File content as BytesIO
            file_metadata: File metadata from Google Drive
            
        Returns:
            Dict[str, Any]: Processing results
        """
        try:
            file_name = file_metadata.get('name', f'gdrive_{file_id}')
            mime_type = file_metadata.get('mimeType', 'application/octet-stream')
            
            self.logger.info(f"Processing document file: {file_name} (ID: {file_id})")
            
            # Determine content type
            content_type = mime_type
            if mime_type == 'application/vnd.google-apps.document':
                content_type = 'application/pdf'
                file_name += '.pdf'
            elif mime_type == 'application/vnd.google-apps.spreadsheet':
                content_type = 'text/csv'
                file_name += '.csv'
            
            # Ingest the document
            ingest_result = self._ingest_document(file_content, file_name, content_type, {
                'source': 'google_drive',
                'file_id': file_id,
                'modified_time': file_metadata.get('modifiedTime'),
                'mime_type': mime_type
            })
            
            # Check if task was queued successfully (ingest_result contains task_id)
            if ingest_result and ingest_result.get("task_id"):
                task_id = ingest_result.get("task_id")
                self.logger.info(f"Successfully queued ingestion for document: {file_name}, task ID: {task_id}")
                
                # Mark the file as "processing" or "queued" instead of waiting for a document_id
                # The actual document_id will be set later by the callback task
                self.mark_file_processed(
                    file_id=file_id,
                    file_metadata=file_metadata,
                    document_id=None,
                    status="queued",
                    result={"task_id": task_id}
                )
                
                return {
                    "file_id": file_id,
                    "file_name": file_name,
                    "task_id": task_id,
                    "success": True
                }
            else:
                self.logger.error(f"Failed to queue ingestion for document: {file_name}")
                
                # Mark the file as failed in the database
                self.mark_file_processed(
                    file_id=file_id,
                    file_metadata=file_metadata,
                    status="failed",
                    result={"error": "Failed to queue ingestion task"}
                )
                
                return {
                    "file_id": file_id,
                    "file_name": file_name,
                    "success": False,
                    "error": "Failed to queue ingestion task"
                }
        except Exception as e:
            self.logger.error(f"Error processing document file {file_id}: {str(e)}", exc_info=True)
            
            # Mark the file as failed in the database
            self.mark_file_processed(
                file_id=file_id,
                file_metadata=file_metadata,
                status="failed",
                result={"error": str(e)}
            )
            
            return {
                "file_id": file_id,
                "file_name": file_metadata.get('name', f'gdrive_{file_id}'),
                "success": False,
                "error": str(e)
            }

    def _ingest_url(self, url: str) -> Optional[Dict[str, Any]]:
        """
        Ingest a URL using the document worker.
        
        Args:
            url: URL to ingest
            
        Returns:
            Optional[Dict[str, Any]]: Dictionary containing task ID or None on error
        """
        try:
            from workers.celery_app import celery_app
            
            # Queue the ingestion task
            task = celery_app.send_task(
                "workers.document.ingest_document",
                kwargs={
                    "use_batch_api": True,
                    "source_type": "url",
                    "source_path": url,
                    "metadata": {
                        "source": "gdrive_import",
                        "import_time": datetime.now().isoformat()
                    },
                    "callback_task": "workers.document.process_document",
                    "build_knowledge_graph": True,
                }
            )
            
            self.logger.info(f"URL ingestion queued with task ID {task.id}")
            return {"task_id": task.id}
        except Exception as e:
            self.logger.error(f"Error queueing URL ingestion: {str(e)}", exc_info=True)
            return None

    def _ingest_document(self, file_content: io.BytesIO, filename: str, content_type: str, metadata: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Ingest a document file.
        
        Args:
            file_content: File content as BytesIO
            filename: Filename
            content_type: Content type
            metadata: File metadata
            
        Returns:
            Optional[Dict[str, Any]]: Dictionary containing task ID or None on error
        """
        try:
            from workers.celery_app import celery_app
            
            # Queue the ingestion task
            task = celery_app.send_task(
                "workers.document.ingest_document",
                kwargs={
                    "use_batch_api": True,
                    "source_type": "file",
                    "source_path": "gdrive",
                    "metadata": metadata,
                    "file_content": file_content.read(),
                    "filename": filename,
                    "content_type": content_type,
                    "callback_task": "workers.document.process_document",
                    "build_knowledge_graph": True,
                }
            )
            
            self.logger.info(f"Document ingestion queued with task ID {task.id}")
            return {"task_id": task.id}
        except Exception as e:
            self.logger.error(f"Error queueing document ingestion: {str(e)}", exc_info=True)
            return None

    def check_and_ingest_new_documents(self, folder_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Check for new documents in the Google Drive folder and ingest them.
        
        Args:
            folder_id: Optional Google Drive folder ID. If None, uses the default folder.
            
        Returns:
            Dict[str, Any]: Processing results
        """
        folder_id = folder_id or self.drive_folder_id
        
        try:
            self.logger.info(f"Checking for new documents in Google Drive folder: {folder_id}")
            
            # List unprocessed files
            unprocessed_files = self.list_unprocessed_files(folder_id)
            if not unprocessed_files:
                self.logger.info("No new documents found")
                return {
                    "status": "success",
                    "message": "No new documents found",
                    "processed_count": 0
                }
            
            self.logger.info(f"Found {len(unprocessed_files)} new documents to process")
            
            # Process each file
            processed_count = 0
            url_csv_count = 0
            regular_doc_count = 0
            failed_count = 0
            
            for file_metadata in unprocessed_files:
                file_id = file_metadata.get('id')
                file_name = file_metadata.get('name', '')
                
                try:
                    # Download the file
                    file_content = self.download_file(file_id)
                    if not file_content:
                        self.logger.error(f"Failed to download file: {file_name} (ID: {file_id})")
                        
                        # Mark the file as failed
                        self.mark_file_processed(
                            file_id=file_id,
                            file_metadata=file_metadata,
                            status="failed",
                            result={"error": "Failed to download file"}
                        )
                        
                        failed_count += 1
                        continue
                    
                    # Check if this is a URL CSV file
                    if file_name.endswith('_url.csv'):
                        result = self.process_url_csv_file(file_id, file_content, file_metadata)
                        url_csv_count += 1
                    else:
                        result = self.process_document_file(file_id, file_content, file_metadata)
                        regular_doc_count += 1
                    
                    if result.get('success', False) or result.get('queued_count', 0) > 0:
                        processed_count += 1
                    else:
                        failed_count += 1
                        
                except Exception as e:
                    self.logger.error(f"Error processing file {file_name} (ID: {file_id}): {str(e)}", exc_info=True)
                    
                    # Mark the file as failed
                    self.mark_file_processed(
                        file_id=file_id,
                        file_metadata=file_metadata,
                        status="failed",
                        result={"error": str(e)}
                    )
                    
                    failed_count += 1
            
            self.logger.info(f"Processed {processed_count} files: {url_csv_count} URL CSV files, {regular_doc_count} regular documents, {failed_count} failed")
            
            return {
                "status": "success",
                "message": f"Processed {processed_count} new documents",
                "processed_count": processed_count,
                "url_csv_count": url_csv_count,
                "regular_doc_count": regular_doc_count,
                "failed_count": failed_count
            }
        except Exception as e:
            self.logger.error(f"Error checking for new documents: {str(e)}", exc_info=True)
            return {
                "status": "error",
                "message": f"Error checking for new documents: {str(e)}",
                "processed_count": 0
            }


if __name__ == "__main__":
    # Configure logging for local testing
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    logger.info("Starting GoogleDriveService local testing")
    
    try:
        # Initialize service
        with GoogleDriveService() as service:
            # List files in the folder
            folder_id = getattr(settings, 'GOOGLE_DRIVE_FOLDER_ID', None)
            if folder_id:
                files = service.list_unprocessed_files(folder_id)
                logger.info(f"Found {len(files)} unprocessed files in folder {folder_id}")
                
                # Print file details
                for file in files[:5]:  # Limit to 5 files for readability
                    logger.info(f"File: {file.get('name')} (ID: {file.get('id')}, Type: {file.get('mimeType')})")
                
                # Get statistics
                stats = service.get_processed_file_stats()
                logger.info(f"Processed file statistics: {stats}")
            else:
                logger.warning("No Google Drive folder ID provided in settings")
            
            logger.info("GoogleDriveService local testing completed")
    
    except Exception as e:
        logger.error(f"Error during GoogleDriveService testing: {str(e)}", exc_info=True)