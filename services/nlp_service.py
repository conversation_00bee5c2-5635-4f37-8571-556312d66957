"""
NLP service for text processing, embeddings, and entity extraction.
"""
import logging
import os
import uuid
import tempfile
from typing import Dict, List, Any, Optional, Union, Tuple, Set
from collections import defaultdict

import numpy as np
from transformers import AutoTokenizer, AutoModelForTokenClassification, pipeline
from sentence_transformers import SentenceTransformer
import torch
from sklearn.metrics.pairwise import cosine_similarity

from services.document_partitioner import DocumentPartitioner

from common.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Model caches
_EMBEDDING_MODEL = None  # E5-Large-V2 model for semantic search (primary embedding model)
_BIOBERT_NER = None  # BioBERT NER model for entity extraction
_SAPBERT_MODEL = None  # SapBERT model for entity normalization

# Entity types of interest for longevity research
LONGEVITY_ENTITY_TYPES = {
    "GENE": "gene_protein",
    "PROTEIN": "gene_protein",
    "DISEASE": "disease",
    "CHEMICAL": "chemical_drug",
    "DRUG": "chemical_drug",
    "CELL_TYPE": "cell",
    "CELL_LINE": "cell",
    "SPECIES": "species",
    "DNA": "genetic_element",
    "RNA": "genetic_element",
    "PATHWAY": "pathway",
    "CELLULAR_COMPONENT": "cellular_component",
    "ORGANISM": "organism",
    "TISSUE": "tissue",
    "BIOLOGICAL_PROCESS": "biological_process",
    "MOLECULAR_FUNCTION": "molecular_function",
    "PHENOTYPE": "phenotype",
    "AGE": "age",
    "INTERVENTION": "intervention",
    "BIOMARKER": "biomarker",
    "LIFESPAN": "lifespan_measure",
}

# Dictionary mapping entity types to potential relationship types
ENTITY_RELATION_TYPES = {
    ("gene_protein", "disease"): ["associated_with", "causes", "treats", "biomarker_for"],
    ("gene_protein", "gene_protein"): ["interacts_with", "regulates", "inhibits", "activates"],
    ("gene_protein", "pathway"): ["participates_in", "regulates"],
    ("gene_protein", "biological_process"): ["involved_in", "regulates"],
    ("chemical_drug", "disease"): ["treats", "causes", "prevents"],
    ("chemical_drug", "gene_protein"): ["targets", "inhibits", "activates"],
    ("intervention", "lifespan_measure"): ["increases", "decreases", "affects"],
    ("biomarker", "age"): ["indicates", "correlates_with"],
    ("biological_process", "lifespan_measure"): ["affects", "regulates"],
}


class NLPService:
    """Service for NLP operations including text processing, embeddings, and entity extraction."""

    @staticmethod
    def get_embedding_model():
        """
        Get or load E5-Large-V2 embedding model for semantic search.

        This is the primary embedding model used for all semantic search functionality.

        Returns:
            SentenceTransformer: E5-Large-V2 embedding model
        """
        global _EMBEDDING_MODEL

        if _EMBEDDING_MODEL is None:
            logger.info(f"Loading E5-Large-V2 embedding model: {settings.EMBEDDING_MODEL}")
            _EMBEDDING_MODEL = SentenceTransformer(settings.EMBEDDING_MODEL)
            logger.info("E5-Large-V2 embedding model loaded successfully")

        return _EMBEDDING_MODEL

    @staticmethod
    def get_biobert_ner():
        """
        Get or initialize the BioBERT NER pipeline.

        Returns:
            pipeline: NER pipeline for biomedical text
        """
        global _BIOBERT_NER

        if _BIOBERT_NER is None:
            logger.info("Loading BioBERT NER pipeline")
            # We'll use a BioBERT model fine-tuned for biomedical NER
            # For this example, we're using a public model - in production you'd use a more specialized one
            model_name = "dmis-lab/biobert-base-cased-v1.1"

            try:
                tokenizer = AutoTokenizer.from_pretrained(model_name)
                model = AutoModelForTokenClassification.from_pretrained(model_name)

                # For demonstration - in production you would use a model specifically fine-tuned for biomedical NER
                _BIOBERT_NER = pipeline(
                    "ner",
                    model=model,
                    tokenizer=tokenizer,
                    aggregation_strategy="simple"
                )
                logger.info("BioBERT NER pipeline loaded successfully")
            except Exception as e:
                logger.error(f"Error loading BioBERT NER: {str(e)}")
                # Fallback to a standard NER model
                _BIOBERT_NER = pipeline("ner", aggregation_strategy="simple")
                logger.info("Fallback NER pipeline loaded")

        return _BIOBERT_NER

    @staticmethod
    def get_sapbert_model():
        """
        Get or initialize the SapBERT model for entity normalization.

        Returns:
            SentenceTransformer: SapBERT model
        """
        global _SAPBERT_MODEL

        if _SAPBERT_MODEL is None:
            logger.info("Loading SapBERT model")
            try:
                # We'll use SapBERT for biomedical entity normalization
                model_name = "cambridgeltl/SapBERT-from-PubMedBERT-fulltext"
                _SAPBERT_MODEL = SentenceTransformer(model_name)
                logger.info("SapBERT model loaded successfully")
            except Exception as e:
                logger.error(f"Error loading SapBERT: {str(e)}")
                # Fallback to a general-purpose embedding model
                _SAPBERT_MODEL = SentenceTransformer("all-MiniLM-L6-v2")
                logger.info("Fallback embedding model loaded")

        return _SAPBERT_MODEL

    @staticmethod
    def extract_text_from_file(file_path_or_url: str, content_type: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Extract text elements from a file or URL using DocumentPartitioner.

        Args:
            file_path_or_url: Path to the file or URL
            content_type: Optional content type

        Returns:
            List[Dict[str, Any]]: List of text elements with metadata
        """
        try:
            # Extract content using DocumentPartitioner
            elements = DocumentPartitioner.partition_document(
                file_path_or_url=file_path_or_url,
                content_type=content_type
            )

            # Convert elements to a standard format
            text_elements = NLPService.create_text_elements(elements)

            return text_elements

        except Exception as e:
            logger.error(f"Error extracting text from {file_path_or_url}: {str(e)}")
            raise

    @staticmethod
    def create_text_elements(elements) -> List[Dict[str, Any]]:
        """
        Create standardized text elements from unstructured.io elements.

        Args:
            elements: List of elements from unstructured.io

        Returns:
            List[Dict[str, Any]]: List of standardized text elements
        """
        text_elements = []

        for i, element in enumerate(elements):
            # Extract metadata from element
            element_metadata = {
                "element_type": element.category,
                "element_id": i,
                "page_number": getattr(element, "page_number", None),
            }

            # Special handling for tables, titles, etc.
            if hasattr(element, "metadata"):
                element_metadata.update(element.metadata)

            # Add to text elements
            text_elements.append({
                "text": str(element),
                "metadata": element_metadata
            })

        return text_elements

    @staticmethod
    def generate_embeddings(texts: List[str]) -> np.ndarray:
        """
        Generate embeddings for a list of texts using E5-Large-V2 model.

        This method adds the 'passage:' prefix to each text, which is required
        for optimal performance with E5-Large-V2 when embedding document passages.

        Args:
            texts: List of text strings

        Returns:
            np.ndarray: Array of embeddings optimized for semantic search
        """
        model = NLPService.get_embedding_model()

        # Add 'passage:' prefix for E5-Large-V2
        prefixed_texts = [f"passage: {text}" for text in texts]

        # Generate embeddings with normalization (important for E5 models)
        embeddings = model.encode(prefixed_texts, show_progress_bar=False, normalize_embeddings=True)

        # Return the numpy array directly
        return embeddings

    @staticmethod
    def generate_query_embedding(query: str) -> np.ndarray:
        """
        Generate embedding for a search query using E5-Large-V2 model.

        This method adds the 'query:' prefix to the text, which is required
        for optimal performance with E5-Large-V2 when embedding search queries.

        Args:
            query: Search query text

        Returns:
            np.ndarray: Query embedding optimized for semantic search
        """
        return NLPService.generate_semantic_search_embeddings([query], is_query=True)[0]

    @staticmethod
    def generate_semantic_search_embeddings(texts: List[str], is_query: bool = False) -> np.ndarray:
        """
        Generate embeddings for semantic search using E5-Large-V2 model.

        E5-Large-V2 requires specific prefixes for optimal performance:
        - 'query: ' for search queries
        - 'passage: ' for document passages

        Args:
            texts: List of text strings
            is_query: Whether the texts are search queries (True) or passages (False)

        Returns:
            np.ndarray: Array of embeddings optimized for semantic search
        """
        model = NLPService.get_embedding_model()

        # Add appropriate prefixes for E5 model
        prefix = "query: " if is_query else "passage: "
        prefixed_texts = [f"{prefix}{text}" for text in texts]

        # Generate embeddings with normalization (important for E5 models)
        embeddings = model.encode(prefixed_texts, show_progress_bar=False, normalize_embeddings=True)

        # Return the numpy array directly
        return embeddings

    @staticmethod
    def get_embeddings(texts: List[str]) -> np.ndarray:
        """
        Generate embeddings for a list of texts using the BioBERT model.

        This function uses BioBERT, which is specifically trained on biomedical text,
        making it more suitable for biomedical and healthcare applications.

        Args:
            texts: List of text strings

        Returns:
            np.ndarray: Array of embeddings from BioBERT
        """
        # Get the BioBERT model
        biobert_model_name = "pritamdeka/S-BioBert-snli-multinli-stsb"
        model = SentenceTransformer(biobert_model_name)

        # Generate embeddings in batches
        embeddings = model.encode(texts, show_progress_bar=False)

        # Return the numpy array directly
        return embeddings

    @staticmethod
    def extract_biomedical_entities(text: str) -> List[Dict[str, Any]]:
        """
        Extract biomedical entities from text using BioBERT NER.

        Args:
            text: Input text

        Returns:
            List[Dict[str, Any]]: List of extracted biomedical entities
        """
        # Skip empty text or text that's too short for meaningful entity extraction
        if not text or len(text.strip()) == 0:
            return []

        # Minimum text length for meaningful entity extraction
        MIN_TEXT_LENGTH = 100
        if len(text.strip()) < MIN_TEXT_LENGTH:
            logger.info(f"Text too short for entity extraction: {len(text.strip())} characters")
            return []

        try:
            # Get BioBERT NER pipeline
            ner = NLPService.get_biobert_ner()

            # Extract entities
            entities = ner(text)

            # Process and categorize entities
            biomedical_entities = []

            for entity in entities:
                # Map entity type to longevity-specific category
                entity_type = entity.get('entity_group', 'MISC').upper()
                mapped_type = LONGEVITY_ENTITY_TYPES.get(entity_type)

                if not mapped_type:
                    # Try to guess the entity type based on common patterns in longevity research
                    if NLPService._looks_like_gene(entity['word']):
                        mapped_type = "gene_protein"
                    elif NLPService._looks_like_chemical(entity['word']):
                        mapped_type = "chemical_drug"
                    elif NLPService._looks_like_disease(entity['word']):
                        mapped_type = "disease"
                    else:
                        # Skip entities we can't categorize
                        continue

                # Create entity object
                # Keep numpy types as is
                biomedical_entity = {
                    'id': str(uuid.uuid4()),
                    'text': entity['word'],
                    'type': mapped_type,
                    'original_type': entity_type,
                    'score': entity.get('score', 0.0),
                    'start': entity.get('start', 0),
                    'end': entity.get('end', 0),
                    'normalized_id': None,  # Will be filled in normalization step
                }

                biomedical_entities.append(biomedical_entity)

            return biomedical_entities

        except Exception as e:
            logger.error(f"Error extracting biomedical entities: {str(e)}")
            return []

    @staticmethod
    def normalize_entities(entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Normalize entities using SapBERT.

        Args:
            entities: List of extracted entities

        Returns:
            List[Dict[str, Any]]: List of normalized entities
        """
        if not entities:
            return []

        try:
            # Get SapBERT model
            model = NLPService.get_sapbert_model()

            # Group entities by type
            entities_by_type = defaultdict(list)
            for entity in entities:
                entities_by_type[entity['type']].append(entity)

            # Process each type separately
            normalized_entities = []

            for entity_type, type_entities in entities_by_type.items():
                # Extract entity texts
                texts = [entity['text'] for entity in type_entities]

                # Generate embeddings
                embeddings = model.encode(texts, convert_to_tensor=True)

                # Compute similarity matrix
                similarity_matrix = cosine_similarity(embeddings.cpu().numpy())

                # Cluster similar entities
                clusters = NLPService._cluster_entities(type_entities, similarity_matrix, threshold=0.85)

                # Create normalized entities from clusters
                for cluster in clusters:
                    # Use the highest scoring entity as the representative
                    best_entity = max(cluster, key=lambda e: e['score'])

                    # Collect all chunk references
                    chunk_ids = set()
                    for entity in cluster:
                        if 'chunk_id' in entity:
                            chunk_ids.add(entity['chunk_id'])

                    # Create normalized entity
                    # Keep numpy types as is
                    normalized_entity = {
                        'id': best_entity['id'],
                        'text': best_entity['text'],
                        'type': entity_type,
                        'score': best_entity['score'],
                        'chunks': list(chunk_ids),
                        'document_id': best_entity.get('document_id'),
                        'variants': list(set(e['text'] for e in cluster)),
                        'normalized_id': None,  # Would be filled with external database ID
                    }

                    normalized_entities.append(normalized_entity)

            return normalized_entities

        except Exception as e:
            logger.error(f"Error normalizing entities: {str(e)}")
            # Fall back to basic deduplication
            return NLPService._basic_deduplicate_entities(entities)

    @staticmethod
    def deduplicate_and_normalize_entities(entities: List[Dict[str, Any]], document_id: str = None) -> List[Dict[str, Any]]:
        """
        Deduplicate and normalize entities by checking for similar entities in PostgreSQL.

        This method performs the following steps:
        1. Generate embeddings for all entities
        2. Check for similar entities in PostgreSQL
        3. For similar entities, use the existing entity ID
        4. For new entities, insert them into PostgreSQL and return the new ID
        5. Normalize entity names

        Args:
            entities: List of entities to deduplicate and normalize
            document_id: Optional document ID to associate with new entities

        Returns:
            List[Dict[str, Any]]: Deduplicated and normalized entities with proper IDs
        """
        if not entities:
            return []

        logger.info(f"Deduplicating and normalizing {len(entities)} entities")

        # Generate embeddings for all entities
        entity_texts = [entity["text"] for entity in entities]
        embeddings = NLPService.generate_embeddings(entity_texts)

        # Add embeddings to entities
        for i, entity in enumerate(entities):
            entity["embedding"] = embeddings[i]

        # Create DataTransport instance for database operations
        from transport.data_transport import DataTransport
        with DataTransport() as transport:
            # Process each entity
            deduplicated_entities = []

            for entity in entities:
                # Find similar entities in PostgreSQL
                # Convert numpy array to list if needed
                embedding = entity["embedding"]
                if isinstance(embedding, np.ndarray):
                    embedding = embedding.tolist()

                similar_entities = transport.find_similar_entities(
                    text=entity["text"],
                    embedding=embedding,
                    entity_type=entity.get("type"),
                    threshold=0.92,  # High threshold for similarity
                    limit=5
                )

                if similar_entities:
                    # Found similar entity, use it instead
                    similar_entity = similar_entities[0]  # Most similar entity

                    # Update entity with normalized information
                    entity["id"] = similar_entity["id"]
                    entity["normalized_id"] = similar_entity["id"]
                    entity["normalized_text"] = similar_entity["text"]
                    entity["is_duplicate"] = True
                    entity["global_id"] = similar_entity["id"]
                    entity["original_id"] = entity.get("id")  # Keep track of original ID
                else:
                    # No similar entity found, this is a new entity
                    # Make sure it has a document_id
                    if document_id and "document_id" not in entity:
                        entity["document_id"] = document_id

                    # Normalize the entity name
                    entity["normalized_text"] = NLPService._normalize_entity_name(entity["text"])
                    entity["normalized_id"] = entity["id"]
                    entity["is_duplicate"] = False
                    entity["global_id"] = entity["id"]

                    # Store the new entity in PostgreSQL
                    if document_id:
                        # Handle chunk_id - if it's not a valid UUID, log a warning and set to None
                        chunk_id = None
                        if entity.get("chunk_id"):
                            try:
                                # Try to convert to UUID
                                chunk_id = uuid.UUID(entity["chunk_id"])
                            except ValueError:
                                # Not a valid UUID, log a warning and continue without a chunk_id
                                logger.warning(f"Invalid chunk_id format: {entity['chunk_id']}. Expected a valid UUID. Continuing without chunk association.")

                        # Create entity record
                        entity_record = transport.db_client.create_entity(
                            id=uuid.UUID(entity["id"]) if isinstance(entity["id"], str) else entity["id"],
                            document_id=uuid.UUID(document_id) if isinstance(document_id, str) else document_id,
                            kg_chunk_id=chunk_id,  # Will be None if not a valid UUID
                            text=entity["text"],
                            entity_type=entity["type"],
                            normalized_id=entity.get("normalized_id"),
                            start_pos=entity.get("start", 0),
                            end_pos=entity.get("end", 0),
                            confidence=entity.get("score", 1.0),
                            entity_metadata={
                                "variants": entity.get("variants", []),
                                "embedding": entity["embedding"].tolist() if isinstance(entity.get("embedding"), np.ndarray) else entity.get("embedding")
                            }
                        )

                        # Update entity with database ID
                        entity["id"] = str(entity_record.id)
                        entity["normalized_id"] = str(entity_record.id)
                        entity["global_id"] = str(entity_record.id)

                deduplicated_entities.append(entity)

            logger.info(f"Deduplication and normalization complete. {len(deduplicated_entities)} entities processed.")
            return deduplicated_entities

    @staticmethod
    def _normalize_entity_name(entity_text: str) -> str:
        """
        Normalize entity name by removing common prefixes/suffixes and standardizing format.

        Args:
            entity_text: Original entity text

        Returns:
            str: Normalized entity name
        """
        # Convert to uppercase for gene/protein names
        if NLPService._looks_like_gene(entity_text):
            return entity_text.upper()

        # Standardize disease names (capitalize first letter of each word)
        if NLPService._looks_like_disease(entity_text):
            return ' '.join(word.capitalize() for word in entity_text.split())

        # Standardize chemical/drug names
        if NLPService._looks_like_chemical(entity_text):
            # Remove common prefixes like "the", "a", etc.
            words = entity_text.split()
            if words and words[0].lower() in ["the", "a", "an"]:
                words = words[1:]
            return ' '.join(word.capitalize() for word in words)

        # Default normalization (capitalize first letter)
        return entity_text.strip().capitalize()

    @staticmethod
    def extract_relationships(entities: List[Dict[str, Any]], chunk_texts: Dict[str, str]) -> List[Dict[str, Any]]:
        """
        Extract relationships between entities based on co-occurrence and patterns.

        Args:
            entities: List of normalized entities
            chunk_texts: Dictionary mapping chunk IDs to chunk texts

        Returns:
            List[Dict[str, Any]]: List of extracted relationships
        """
        relationships = []

        # Create a mapping of entities by chunk
        entities_by_chunk = defaultdict(list)
        for entity in entities:
            for chunk_id in entity.get('chunks', []):
                entities_by_chunk[chunk_id].append(entity)

        # Process each chunk to find relationships
        for chunk_id, chunk_entities in entities_by_chunk.items():
            # Skip chunks with too few entities
            if len(chunk_entities) < 2:
                continue

            # Get chunk text
            chunk_text = chunk_texts.get(chunk_id, "")

            # Extract relationships based on co-occurrence
            chunk_relationships = NLPService._extract_cooccurrence_relationships(
                chunk_entities,
                chunk_text,
                chunk_id
            )

            relationships.extend(chunk_relationships)

        # Deduplicate relationships
        unique_relationships = NLPService._deduplicate_relationships(relationships)

        return unique_relationships

    @staticmethod
    def compute_similarity(query_embedding: np.ndarray, embeddings: Union[List[np.ndarray], np.ndarray]) -> np.ndarray:
        """
        Compute similarity between query embedding and a list of embeddings.

        For E5-Large-V2 embeddings that are already normalized, dot product equals cosine similarity.
        This method works efficiently with normalized embeddings from E5-Large-V2.

        Args:
            query_embedding: Query embedding (normalized)
            embeddings: List of embeddings or 2D numpy array of embeddings to compare against (normalized)

        Returns:
            np.ndarray: Array of similarity scores
        """
        # Convert to numpy arrays
        query_np = np.array(query_embedding)
        embeddings_np = np.array(embeddings)

        # Ensure embeddings_np is 2D
        if embeddings_np.ndim == 1 and query_np.ndim == 1:
            # Single embedding, reshape to 2D
            embeddings_np = embeddings_np.reshape(1, -1)
        elif embeddings_np.ndim > 2:
            # Handle unexpected dimensions
            raise ValueError(f"Embeddings must be 1D or 2D, got {embeddings_np.ndim}D")

        # For normalized embeddings, dot product equals cosine similarity
        # This is more efficient than using cosine_similarity from sklearn
        similarities = np.dot(embeddings_np, query_np)

        # Return numpy array directly
        return similarities

    @staticmethod
    def semantic_search(query: str, passage_embeddings: np.ndarray, passages: List[Dict[str, Any]], top_k: int = 5) -> List[Dict[str, Any]]:
        """
        Perform semantic search using E5-Large-V2 embeddings.

        Args:
            query: Search query
            passage_embeddings: Precomputed passage embeddings from E5-Large-V2
            passages: List of passage dictionaries
            top_k: Number of results to return

        Returns:
            List[Dict[str, Any]]: Top-k semantically similar passages with scores
        """
        # Generate query embedding with 'query:' prefix using the embedding model (E5-Large-V2)
        query_embedding = NLPService.generate_query_embedding(query)

        # Compute similarities using dot product (efficient for normalized embeddings)
        similarities = np.dot(passage_embeddings, query_embedding)

        # Get top-k indices
        top_indices = np.argsort(similarities)[-top_k:][::-1]

        # Prepare results
        results = []
        for idx in top_indices:
            result = passages[idx].copy()
            result['similarity_score'] = float(similarities[idx])
            results.append(result)

        return results

    # Helper methods

    @staticmethod
    def _cluster_entities(entities: List[Dict[str, Any]], similarity_matrix: np.ndarray, threshold: float = 0.92) -> List[List[Dict[str, Any]]]:
        """
        Cluster similar entities based on similarity matrix.

        Args:
            entities: List of entities
            similarity_matrix: Matrix of cosine similarities
            threshold: Similarity threshold for clustering

        Returns:
            List[List[Dict[str, Any]]]: List of entity clusters
        """
        n = len(entities)

        # Keep track of which entities have been assigned to clusters
        assigned = [False] * n
        clusters = []

        for i in range(n):
            if assigned[i]:
                continue

            # Start a new cluster with this entity
            cluster = [entities[i]]
            assigned[i] = True

            # Find similar entities
            for j in range(n):
                if not assigned[j] and similarity_matrix[i, j] >= threshold:
                    cluster.append(entities[j])
                    assigned[j] = True

            clusters.append(cluster)

        return clusters

    @staticmethod
    def _basic_deduplicate_entities(entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Basic deduplication of entities based on text and type.

        Args:
            entities: List of entities

        Returns:
            List[Dict[str, Any]]: List of deduplicated entities
        """
        # Create a dictionary to track unique entities
        unique_entities = {}

        for entity in entities:
            # Create a key for deduplication (lowercase text + type)
            key = (entity['text'].lower(), entity['type'])
            if not entity['text'] or len(entity['text'].strip()) < 2:
                continue  # Skip very short entity mentions

            # If the entity doesn't exist or has a higher score, update it
            if key not in unique_entities or entity['score'] > unique_entities[key]['score']:
                # Create a copy with chunk references
                if key in unique_entities and 'chunks' in unique_entities[key]:
                    # Preserve existing chunk references
                    chunks = unique_entities[key]['chunks']
                    if 'chunk_id' in entity and entity['chunk_id'] not in chunks:
                        chunks.append(entity['chunk_id'])
                else:
                    # Create new chunk references
                    chunks = [entity['chunk_id']] if 'chunk_id' in entity else []

                # Create a deduplicated entity
                deduplicated_entity = {
                    'id': entity['id'],
                    'text': entity['text'],
                    'type': entity['type'],
                    'score': entity['score'],
                    'chunks': chunks,
                    'document_id': entity.get('document_id'),
                    'variants': [entity['text']],
                    'normalized_id': None,
                }

                unique_entities[key] = deduplicated_entity
            elif 'chunk_id' in entity and key in unique_entities and 'chunks' in unique_entities[key]:
                # Add chunk reference if it doesn't exist
                if entity['chunk_id'] not in unique_entities[key]['chunks']:
                    unique_entities[key]['chunks'].append(entity['chunk_id'])

                # Add to variants if different text
                if entity['text'] not in unique_entities[key]['variants']:
                    unique_entities[key]['variants'].append(entity['text'])

        # Return as a list
        return list(unique_entities.values())

    @staticmethod
    def _extract_cooccurrence_relationships(entities: List[Dict[str, Any]], text: str, chunk_id: str) -> List[Dict[str, Any]]:
        """
        Extract relationships based on entity co-occurrence and patterns.

        Args:
            entities: List of entities in a chunk
            text: Chunk text
            chunk_id: Chunk ID

        Returns:
            List[Dict[str, Any]]: List of relationships
        """
        relationships = []
        text_lower = text.lower()

        # Keywords that suggest specific relationship types
        relation_keywords = {
            "activates": ["activate", "activates", "activation", "stimulates", "induces", "upregulates"],
            "inhibits": ["inhibit", "inhibits", "inhibition", "blocks", "suppresses", "downregulates"],
            "increases": ["increase", "increases", "elevated", "higher", "enhances", "extends", "prolongs"],
            "decreases": ["decrease", "decreases", "reduced", "lower", "diminishes", "shortens"],
            "associated_with": ["associated", "linked", "correlated", "related", "connection"],
            "treats": ["treats", "treatment", "therapy", "therapeutic", "ameliorates"],
            "causes": ["causes", "induces", "leads to", "results in", "contributes to"],
            "targets": ["targets", "binds", "interacts with"],
        }

        # Check all entity pairs
        for i, entity1 in enumerate(entities):
            for j, entity2 in enumerate(entities):
                if i == j:
                    continue

                entity1_type = entity1['type']
                entity2_type = entity2['type']

                # Check if this entity type pair typically has relationships
                possible_relations = ENTITY_RELATION_TYPES.get((entity1_type, entity2_type), [])
                if not possible_relations:
                    possible_relations = ENTITY_RELATION_TYPES.get((entity2_type, entity1_type), [])
                    if possible_relations:
                        # Swap entities if the relation direction is reversed
                        entity1, entity2 = entity2, entity1
                        entity1_type, entity2_type = entity2_type, entity1_type

                if not possible_relations:
                    # Default to generic co-occurrence
                    possible_relations = ["co_occurs_with"]

                # Look for relationship keywords in text between the entities
                relationship_type = "co_occurs_with"  # Default
                confidence = 0.5  # Default confidence

                # Find evidence for specific relationship types
                for rel_type, keywords in relation_keywords.items():
                    if rel_type in possible_relations:
                        # Check if any keywords are present
                        for keyword in keywords:
                            if keyword in text_lower:
                                relationship_type = rel_type
                                confidence = 0.7  # Higher confidence for keyword match
                                break

                # Create relationship object
                relationship = {
                    'id': str(uuid.uuid4()),
                    'source_id': entity1['id'],
                    'source_text': entity1['text'],
                    'source_type': entity1_type,
                    'target_id': entity2['id'],
                    'target_text': entity2['text'],
                    'target_type': entity2_type,
                    'relationship_type': relationship_type,
                    'confidence': confidence,
                    'chunk_id': chunk_id,
                    'evidence_text': text
                }

                relationships.append(relationship)

        return relationships

    @staticmethod
    def _deduplicate_relationships(relationships: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Deduplicate relationships and merge evidence.

        Args:
            relationships: List of extracted relationships

        Returns:
            List[Dict[str, Any]]: List of unique relationships
        """
        # Create a dictionary to track unique relationships
        unique_relationships = {}

        for rel in relationships:
            # Create a key for deduplication
            key = (rel['source_id'], rel['target_id'], rel['relationship_type'])

            if key in unique_relationships:
                # Update existing relationship
                existing_rel = unique_relationships[key]

                # Update confidence to maximum
                existing_rel['confidence'] = max(existing_rel['confidence'], rel['confidence'])

                # Add chunk reference if not already present
                if rel['chunk_id'] not in existing_rel['chunk_ids']:
                    existing_rel['chunk_ids'].append(rel['chunk_id'])
            else:
                # Create new deduplicated relationship
                deduplicated_rel = {
                    'id': rel['id'],
                    'source_id': rel['source_id'],
                    'source_text': rel['source_text'],
                    'source_type': rel['source_type'],
                    'target_id': rel['target_id'],
                    'target_text': rel['target_text'],
                    'target_type': rel['target_type'],
                    'relationship_type': rel['relationship_type'],
                    'confidence': rel['confidence'],
                    'chunk_ids': [rel['chunk_id']],
                    'evidence_text': rel['evidence_text']
                }

                unique_relationships[key] = deduplicated_rel

        # Return as a list
        return list(unique_relationships.values())


    @staticmethod
    def _looks_like_gene(text: str) -> bool:
        """
        Check if text looks like a gene symbol.

        Args:
            text: Entity text

        Returns:
            bool: True if it looks like a gene
        """
        # Very basic pattern matching - in production you'd use a more sophisticated approach
        if len(text) <= 1:
            return False

        # Common gene name patterns (simplified)
        if text.isupper() and len(text) <= 6:
            return True
        if text.startswith(('TP', 'BRCA', 'PTEN', 'SOD', 'IL', 'TNF')):
            return True

        return False

    @staticmethod
    def _looks_like_chemical(text: str) -> bool:
        """
        Check if text looks like a chemical or drug.

        Args:
            text: Entity text

        Returns:
            bool: True if it looks like a chemical
        """
        # Very basic pattern matching - in production you'd use a more sophisticated approach
        text_lower = text.lower()

        # Common drug suffixes
        drug_suffixes = ('cin', 'zole', 'mab', 'ib', 'ide', 'ine', 'min', 'lin')
        if any(text_lower.endswith(suffix) for suffix in drug_suffixes):
            return True

        # Common supplement names
        supplements = ('resveratrol', 'nmn', 'nad+', 'quercetin', 'metformin', 'rapamycin')
        if text_lower in supplements:
            return True

        return False

    @staticmethod
    def _looks_like_disease(text: str) -> bool:
        """
        Check if text looks like a disease.

        Args:
            text: Entity text

        Returns:
            bool: True if it looks like a disease
        """
        # Very basic pattern matching - in production you'd use a more sophisticated approach
        text_lower = text.lower()

        # Common disease suffixes
        disease_suffixes = ('itis', 'osis', 'oma', 'pathy', 'disease', 'syndrome')
        if any(text_lower.endswith(suffix) for suffix in disease_suffixes):
            return True

        # Common aging-related diseases
        diseases = ('cancer', 'alzheimer', 'parkinson', 'diabetes', 'hypertension', 'atherosclerosis')
        if any(disease in text_lower for disease in diseases):
            return True

        return False
