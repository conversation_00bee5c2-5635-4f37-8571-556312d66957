#!/usr/bin/env python
"""
Simple parser for Vertex AI batch responses.
"""
import json
import re
import uuid
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def fix_json_string(json_str):
    """
    Fix common JSON formatting issues.

    Args:
        json_str: JSON string to fix

    Returns:
        Fixed JSON string
    """
    # Remove markdown code block markers if present
    clean_text = json_str.strip()
    if clean_text.startswith("```") and "```" in clean_text[3:]:
        parts = clean_text.split("```", 2)
        if len(parts) >= 2:
            clean_text = parts[1]
            if clean_text.startswith("json"):
                clean_text = clean_text[4:].strip()

    # Fix missing commas between objects in arrays
    clean_text = re.sub(r'}\s*{', '}, {', clean_text)

    # Fix missing commas between properties
    clean_text = re.sub(r'"\s*"', '", "', clean_text)

    # Fix trailing commas
    clean_text = re.sub(r',\s*}', '}', clean_text)
    clean_text = re.sub(r',\s*]', ']', clean_text)

    return clean_text

def extract_json_from_text(text):
    """
    Extract JSON from text.

    Args:
        text: Text containing JSON

    Returns:
        Extracted JSON as dict if successful, None otherwise
    """
    # Try to find JSON content using regex pattern matching
    json_pattern = r'```json\s*({[\s\S]*?})\s*```'
    match = re.search(json_pattern, text, re.DOTALL)

    if not match:
        # If no JSON block with markers, try to find a JSON object directly
        json_pattern = r'({[\s\S]*?"chunk_id"[\s\S]*?})'
        match = re.search(json_pattern, text, re.DOTALL)

        if not match:
            # Try to find any JSON object
            json_pattern = r'({[\s\S]*?"entities"[\s\S]*?})'
            match = re.search(json_pattern, text, re.DOTALL)

    if match:
        json_str = match.group(1)

        try:
            # Try to parse the extracted JSON
            fixed_json = fix_json_string(json_str)
            return json.loads(fixed_json)
        except json.JSONDecodeError as e:
            logger.warning(f"JSON parsing error after fixing: {str(e)}")
            return None

    # If no JSON object found with regex, try to parse the entire text
    try:
        fixed_text = fix_json_string(text)
        return json.loads(fixed_text)
    except json.JSONDecodeError:
        return None

def normalize_entity_type(entity_type: str) -> str:
    """
    Normalize entity type to ensure consistency.

    Args:
        entity_type: Entity type to normalize

    Returns:
        str: Normalized entity type
    """
    if not entity_type:
        return "unknown"

    # Lowercase and trim whitespace
    normalized = entity_type.lower().strip()

    # Define standard entity types (singular form)
    standard_types = {
        # Singular to plural mapping
        "biomarker": "biomarkers",
        "disease": "diseases",
        "intervention": "interventions",
        "supplement": "supplements",
        "tracker": "trackers",
        "lifestyle factor": "lifestyle factors",
        "genetic factor": "genetic factors",
        "organization": "organizations/people",
        "person": "organizations/people",
        "organization/person": "organizations/people",

        # Ensure plural forms map to themselves
        "biomarkers": "biomarkers",
        "diseases": "diseases",
        "interventions": "interventions",
        "supplements": "supplements",
        "trackers": "trackers",
        "lifestyle factors": "lifestyle factors",
        "genetic factors": "genetic factors",
        "organizations/people": "organizations/people"
    }

    # Check for exact match in standard types
    if normalized in standard_types:
        return standard_types[normalized]

    # Check for singular/plural forms
    if normalized.endswith('s') and normalized[:-1] in standard_types:
        # Handle plural form (e.g., "biomarkers" -> "biomarkers")
        singular = normalized[:-1]
        return standard_types[singular]
    elif normalized + 's' in standard_types:
        # Handle singular form (e.g., "biomarker" -> "biomarkers")
        plural = normalized + 's'
        return standard_types[plural]

    # If no match found, return the original type
    return entity_type

def extract_chunk_id(response_data):
    """
    Extract chunk ID from response data.

    Args:
        response_data: Response data

    Returns:
        Chunk ID if found, None otherwise
    """
    # Check if chunk_id is directly in the response
    if "chunk_id" in response_data:
        return response_data["chunk_id"]

    # Try to extract from response text
    if "response" in response_data and "candidates" in response_data["response"]:
        candidates = response_data["response"]["candidates"]
        if candidates and "content" in candidates[0] and "parts" in candidates[0]["content"]:
            parts = candidates[0]["content"]["parts"]
            if parts and "text" in parts[0]:
                text = parts[0]["text"]

                # Try to find chunk_id in JSON format
                chunk_id_match = re.search(r'"chunk_id":\s*"([^"]+)"', text)
                if chunk_id_match:
                    return chunk_id_match.group(1)

                # Try to find chunk_id in plain text format
                chunk_id_match = re.search(r'Chunk ID: ([^\s]+)', text)
                if chunk_id_match:
                    return chunk_id_match.group(1)

    # Try to extract from request text
    if "request" in response_data and "contents" in response_data["request"]:
        contents = response_data["request"]["contents"]
        if contents and "parts" in contents[0]:
            parts = contents[0]["parts"]
            if parts and "text" in parts[0]:
                text = parts[0]["text"]

                # Try to find chunk_id in JSON format
                chunk_id_match = re.search(r'"chunk_id":\s*"([^"]+)"', text)
                if chunk_id_match:
                    return chunk_id_match.group(1)

                # Try to find chunk_id in plain text format
                chunk_id_match = re.search(r'Chunk ID: ([^\s]+)', text)
                if chunk_id_match:
                    return chunk_id_match.group(1)

    return None

def parse_response(response_data, document_id):
    """
    Parse a Vertex AI batch response.

    Args:
        response_data: Response data
        document_id: Document ID

    Returns:
        Tuple of (chunk_id, entities, relationships, errors)
    """
    errors = []
    entities = []
    relationships = []

    # Extract chunk ID
    chunk_id = extract_chunk_id(response_data)
    if not chunk_id:
        errors.append("Failed to extract chunk ID from response")
        return None, entities, relationships, errors

    # Extract response text
    response_text = None
    if "response" in response_data and "candidates" in response_data["response"]:
        candidates = response_data["response"]["candidates"]
        if candidates and "content" in candidates[0] and "parts" in candidates[0]["content"]:
            parts = candidates[0]["content"]["parts"]
            if parts and "text" in parts[0]:
                response_text = parts[0]["text"]

    if not response_text:
        errors.append("No response text found")
        return chunk_id, entities, relationships, errors

    # Extract JSON from response text
    json_data = extract_json_from_text(response_text)
    if not json_data:
        errors.append("Failed to extract JSON from response text")
        return chunk_id, entities, relationships, errors

    # Extract entities
    if "entities" in json_data and isinstance(json_data["entities"], list):
        for entity_data in json_data["entities"]:
            entity_text = entity_data.get("text", "")
            if not entity_text:
                errors.append(f"Entity missing text: {entity_data}")
                continue

            # Get entity type and normalize it
            entity_type = entity_data.get("type", "unknown")
            normalized_type = normalize_entity_type(entity_type)
            entity_id = str(uuid.uuid4())

            entity_obj = {
                "id": entity_id,
                "text": entity_text,
                "type": normalized_type,
                "chunk_id": chunk_id,
                "document_id": document_id
            }

            entities.append(entity_obj)
    else:
        errors.append("No entities found in response")

    # Extract relationships
    if "relationships" in json_data and isinstance(json_data["relationships"], list):
        for rel_data in json_data["relationships"]:
            source_text = rel_data.get("source_text", "")
            target_text = rel_data.get("target_text", "")

            if not source_text or not target_text:
                errors.append(f"Relationship missing source_text or target_text: {rel_data}")
                continue

            rel_type = rel_data.get("type", "RELATED_TO")
            # Format relationship type
            rel_type = rel_type.upper().replace(" ", "_")

            evidence_text = rel_data.get("evidence_text", "")
            rel_id = str(uuid.uuid4())

            rel_obj = {
                "id": rel_id,
                "source_text": source_text,
                "target_text": target_text,
                "type": rel_type,
                "evidence_text": evidence_text,
                "chunk_id": chunk_id,
                "document_id": document_id
            }

            relationships.append(rel_obj)

    return chunk_id, entities, relationships, errors

def parse_batch_file(file_path, document_id):
    """
    Parse a batch file containing Vertex AI responses.

    Args:
        file_path: Path to the batch file
        document_id: Document ID

    Returns:
        Tuple of (entities, relationships, errors)
    """
    all_entities = []
    all_relationships = []
    all_errors = []

    try:
        with open(file_path, 'r') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    # Parse the JSON object
                    response_data = json.loads(line)

                    # Parse the response
                    chunk_id, entities, relationships, errors = parse_response(response_data, document_id)

                    if errors:
                        for error in errors:
                            all_errors.append(f"Line {line_num}: {error}")

                    if chunk_id:
                        all_entities.extend(entities)
                        all_relationships.extend(relationships)

                except json.JSONDecodeError as e:
                    all_errors.append(f"Line {line_num}: JSON parsing error: {str(e)}")
                except Exception as e:
                    all_errors.append(f"Line {line_num}: Error processing line: {str(e)}")
    except Exception as e:
        all_errors.append(f"Error reading file {file_path}: {str(e)}")

    return all_entities, all_relationships, all_errors

if __name__ == "__main__":
    # Test the parser with the sample file
    sample_file_path = Path(__file__).parent.parent.parent / "tests" / "data" / "fixed_sample_vertex_ai_batch_response.json"

    if not sample_file_path.exists():
        sample_file_path = Path(__file__).parent.parent.parent / "tests" / "data" / "sample_vertex_ai_batch_response.json"

    if not sample_file_path.exists():
        logger.error("Sample file not found")
        exit(1)

    logger.info(f"Parsing sample file: {sample_file_path}")

    document_id = "test-document-id"
    entities, relationships, errors = parse_batch_file(sample_file_path, document_id)

    logger.info(f"Extracted {len(entities)} entities and {len(relationships)} relationships")

    if errors:
        logger.warning(f"Encountered {len(errors)} errors:")
        for error in errors[:10]:  # Show first 10 errors
            logger.warning(f"  {error}")
        if len(errors) > 10:
            logger.warning(f"  ... and {len(errors) - 10} more errors")

    # Print some sample entities and relationships
    if entities:
        logger.info("Sample entities:")
        for entity in entities[:5]:
            logger.info(f"  {entity['text']} ({entity['type']})")

    if relationships:
        logger.info("Sample relationships:")
        for rel in relationships[:5]:
            logger.info(f"  {rel['source_text']} --{rel['type']}--> {rel['target_text']}")
