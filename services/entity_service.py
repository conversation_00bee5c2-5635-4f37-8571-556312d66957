"""
Entity extraction service.
"""
import logging
from typing import Dict, Any, Union
import uuid

from transport.data_transport import DataTransport
from services.nlp_service import NLPService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class EntityService:
    """Service for entity extraction and knowledge graph building."""

    @staticmethod
    def extract_entities_from_document(document_id: Union[str, uuid.UUID], processing_task_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
        """
        Extract entities from a document.

        Args:
            document_id: Document ID
            processing_task_id: Processing task ID

        Returns:
            Dict[str, Any]: Processing result
        """
        logger.info(f"Extracting entities from document: {document_id}")

        try:
            with DataTransport() as transport:
                # Update task status
                transport.update_task_status(
                    task_id=processing_task_id,
                    status="processing"
                )

                # Get document KG chunks for entity extraction
                chunks = transport.get_document_chunks(document_id, chunk_type="kg")

                # Extract entities from each chunk
                entities = []
                chunk_texts = {}

                # Minimum chunk size for entity extraction
                MIN_CHUNK_SIZE = 100

                # Filter out small chunks
                filtered_chunks = []
                for chunk in chunks:
                    if len(chunk["text"]) >= MIN_CHUNK_SIZE:
                        filtered_chunks.append(chunk)
                    else:
                        logger.info(f"Skipping small chunk with {len(chunk['text'])} characters for entity extraction")

                logger.info(f"Filtered {len(chunks) - len(filtered_chunks)} chunks out of {len(chunks)} total chunks")
                chunks = filtered_chunks

                for chunk in chunks:
                    chunk_id = chunk["id"]
                    text = chunk["text"]
                    chunk_texts[chunk_id] = text

                    # Extract biomedical entities
                    chunk_entities = NLPService.extract_biomedical_entities(text)

                    # Add document and chunk references
                    for entity in chunk_entities:
                        entity["document_id"] = str(document_id)
                        entity["chunk_id"] = chunk_id

                    entities.extend(chunk_entities)

                # Normalize entities
                normalized_entities = NLPService.normalize_entities(entities)

                # Extract relationships
                relationships = NLPService.extract_relationships(normalized_entities, chunk_texts)

                # Store entities and relationships
                transport.store_entities(document_id, normalized_entities)
                transport.store_relationships(document_id, relationships)

                # Prepare result
                result = {
                    "status": "success",
                    "document_id": str(document_id),
                    "entities_count": len(normalized_entities),
                    "relationships_count": len(relationships)
                }

                # Update task status
                transport.update_task_status(
                    task_id=processing_task_id,
                    status="completed",
                    result=result
                )

                return result
        except Exception as e:
            logger.error(f"Error extracting entities: {e}")
            raise
