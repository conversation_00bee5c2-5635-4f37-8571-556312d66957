"""
Document service for processing and analyzing documents.
"""
import os
import tempfile
import logging
import uuid
from typing import Dict, List, Any, Optional, Union
import numpy as np

from transport.data_transport import DataTransport
from services.nlp_service import NLPService
from services.document_partitioner import DocumentPartitioner
from services.chunking_strategy import ChunkingStrategy, ChunkingPurpose
from transport.storage_client import StorageClient  # Import StorageClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configure NLP service comment


class DocumentService:
    """Service for document processing operations."""

    @staticmethod
    def process_document(document_id: Union[str, uuid.UUID], processing_task_id: Union[str, uuid.UUID], use_batch_api: bool = False, build_knowledge_graph: bool = True) -> Dict[str, Any]:
        """
        Process a document: extract text, create chunks, and generate embeddings.

        Args:
            document_id: Document ID
            processing_task_id: Processing task ID
            use_batch_api: Whether to use Vertex AI batch API for processing
            build_knowledge_graph: Whether to trigger knowledge graph building after processing

        Returns:
            Dict[str, Any]: Processing result
        """
        logger.info(f"Processing document: {document_id}")

        with DataTransport() as transport:
            try:
                # Update task status
                transport.update_task_status(
                    task_id=processing_task_id,
                    status="processing"
                )

                # Get document content
                content, metadata = transport.get_document_content(document_id)

                # Create a temporary file
                with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(metadata['filename'])[1]) as temp_file:
                    temp_file.write(content)
                    temp_file_path = temp_file.name

                try:
                    # Extract content using DocumentPartitioner with appropriate method for file type
                    elements = DocumentPartitioner.partition_document(
                        file_path_or_url=temp_file_path,
                        content_type=metadata.get('content_type')
                    )

                    # Create RAG chunks using the RAG chunking strategy
                    rag_chunks = ChunkingStrategy.chunk_for_purpose(elements, ChunkingPurpose.RAG)

                    # Create KG chunks using the KG chunking strategy
                    kg_chunks = ChunkingStrategy.chunk_for_purpose(elements, ChunkingPurpose.KG)

                    # Log the number of chunks created
                    logger.info(f"Created {len(rag_chunks)} RAG chunks and {len(kg_chunks)} KG chunks")

                    # Generate embeddings for RAG chunks
                    if rag_chunks:
                        rag_texts = [chunk["text"] for chunk in rag_chunks]
                        rag_embeddings = NLPService.generate_semantic_search_embeddings(rag_texts, is_query=False)

                        # Add embeddings to RAG chunks
                        for i, embedding in enumerate(rag_embeddings):
                            rag_chunks[i]["embedding"] = embedding.tolist()

                        # Store RAG chunks
                        rag_result = transport.store_document_chunks(document_id, rag_chunks, chunk_type="rag")
                        logger.info(f"Stored {rag_result['chunks_count']} RAG chunks")
                    else:
                        logger.warning("No RAG chunks created")

                    # Generate embeddings for KG chunks
                    if kg_chunks:
                        kg_texts = [chunk["text"] for chunk in kg_chunks]
                        kg_embeddings = NLPService.generate_semantic_search_embeddings(kg_texts, is_query=False)

                        # Add embeddings to KG chunks
                        for i, embedding in enumerate(kg_embeddings):
                            kg_chunks[i]["embedding"] = embedding.tolist()

                        # Store KG chunks
                        kg_result = transport.store_document_chunks(document_id, kg_chunks, chunk_type="kg")
                        logger.info(f"Stored {kg_result['chunks_count']} KG chunks")
                    else:
                        logger.warning("No KG chunks created")

                    # Update document status
                    transport.db_client.update_document_status(
                        document_id=document_id,
                        status="processed"
                    )

                    # Get total chunk counts
                    rag_count = len(rag_chunks) if rag_chunks else 0
                    kg_count = len(kg_chunks) if kg_chunks else 0
                    total_chunks = rag_count + kg_count

                    # Update task status
                    transport.update_task_status(
                        task_id=processing_task_id,
                        status="completed",
                        result={
                            "document_id": str(document_id),
                            "rag_chunks_count": rag_count,
                            "kg_chunks_count": kg_count,
                            "total_chunks_count": total_chunks,
                            "status": "success",
                        }
                    )

                    logger.info(f"Document {document_id} processed successfully with {rag_count} RAG chunks and {kg_count} KG chunks")

                    # Upload the document to storage bucket
                    storage_location = None
                    try:
                        storage_client = StorageClient()
                        # Get document content again for upload (or use the content we already have)
                        content, metadata = transport.get_document_content(document_id)
                        
                        # Generate a storage path based on document ID and filename
                        filename = metadata.get('filename', f'document-{document_id}')
                        storage_path = f"documents/{document_id}/{filename}"
                        
                        # Upload document as binary
                        storage_location = storage_client.upload_binary(
                            content=content,
                            path=storage_path,
                            content_type=metadata.get('content_type', 'application/octet-stream')
                        )
                        
                        logger.info(f"Document {document_id} uploaded to storage: {storage_location}")
                        
                        # Update document metadata with storage location
                        transport.db_client.update_document_metadata(
                            document_id=document_id,
                            metadata_updates={"storage_location": storage_location}
                        )
                    except Exception as upload_err:
                        logger.error(f"Error uploading document {document_id} to storage: {str(upload_err)}")
                        # Continue processing even if upload fails
                        storage_location = None

                    # Upload extracted figures to GCS
                    figures_directory = metadata.get('figures_directory')
                    if figures_directory and os.path.exists(figures_directory):
                        try:
                            storage_client = StorageClient()
                            figure_paths = []

                            for root, _, files in os.walk(figures_directory):
                                for file in files:
                                    local_path = os.path.join(root, file)
                                    relative_path = os.path.relpath(local_path, figures_directory)
                                    gcs_path = f"documents/{document_id}/figures/{relative_path}"

                                    # Upload the figure to GCS
                                    storage_client.upload_binary(
                                        content=open(local_path, 'rb').read(),
                                        path=gcs_path,
                                        content_type="image/png"
                                    )
                                    figure_paths.append(gcs_path)

                            logger.info(f"Uploaded {len(figure_paths)} figures for document {document_id}")
                        except Exception as e:
                            logger.error(f"Error uploading figures for document {document_id}: {str(e)}")
                    else:
                        logger.info(f"No figures directory found for document {document_id}")

                    # Prepare the result
                    result = {
                        "document_id": str(document_id),
                        "rag_chunks_count": rag_count,
                        "kg_chunks_count": kg_count,
                        "total_chunks_count": total_chunks,
                        "status": "success",
                        "build_knowledge_graph": build_knowledge_graph,
                        "storage_location": storage_location
                    }

                    return result

                finally:
                    # Clean up temporary file
                    if os.path.exists(temp_file_path):
                        os.unlink(temp_file_path)

            except Exception as e:
                logger.error(f"Error processing document {document_id}: {str(e)}")

                # Update task status
                transport.update_task_status(
                    task_id=processing_task_id,
                    status="failed",
                    error=str(e)
                )

                # Update document status
                transport.db_client.update_document_status(
                    document_id=document_id,
                    status="processing_failed"
                )

                raise

    @staticmethod
    def create_chunks_from_elements(elements) -> List[Dict[str, Any]]:
        """
        Create chunks from unstructured.io elements.

        Args:
            elements: List of elements from unstructured.io

        Returns:
            List[Dict[str, Any]]: List of chunk objects
        """
        chunks = []
        for i, element in enumerate(elements):
            # Get the text content of the element
            text = str(element).strip()

            # Skip empty elements
            if not text:
                logger.info(f"Skipping empty element {i}")
                continue

            # Extract metadata from element
            element_metadata = {
                "element_type": element.category,
                "element_id": i,
                "page_number": getattr(element, "page_number", None),
                "text_length": len(text)
            }

            # Special handling for tables, titles, etc.
            if hasattr(element, "metadata"):
                # Handle the ElementMetadata object from unstructured.io
                if hasattr(element.metadata, "to_dict"):
                    # New unstructured.io version uses ElementMetadata class
                    element_metadata.update(element.metadata.to_dict())
                elif isinstance(element.metadata, dict):
                    # Old unstructured.io version uses dict
                    element_metadata.update(element.metadata)

            # Add to chunks
            chunks.append({
                "text": text,
                "metadata": element_metadata
            })

        return chunks

    @staticmethod
    def ingest_document(source_type: str, source_path: str, metadata: Optional[Dict[str, Any]] = None, task_id: Optional[str] = None, file_obj=None, filename=None, content_type=None, title: Optional[str] = None) -> Dict[str, Any]:
        """
        Ingest a document from a URL, filesystem path, or file object.

        Args:
            source_type: Type of source (url, file)
            source_path: Path or URL
            metadata: Optional metadata
            task_id: Optional Celery task ID
            file_obj: Optional file object for file uploads
            filename: Optional filename for file uploads
            content_type: Optional content type for file uploads
            title: Optional document title (if available)

        Returns:
            Dict[str, Any]: Ingestion result
        """
        with DataTransport() as transport:
            try:
                # Create or update metadata
                if metadata is None:
                    metadata = {}

                metadata.update({
                    "source_type": source_type,
                    "source_path": source_path,
                    "celery_task_id": task_id
                })

                # Store document based on source type
                if source_type == "url":
                    # For URLs, we can use our DocumentPartitioner to handle different content types
                    logger.info(f"Ingesting document from URL: {source_path}")

                    # If filename is not provided, extract it from the URL
                    if not filename:
                        from urllib.parse import urlparse
                        parsed_url = urlparse(source_path)
                        path = parsed_url.path
                        filename = os.path.basename(path) or "document.html"

                        # If filename has no extension, add .html
                        if not os.path.splitext(filename)[1]:
                            filename += ".html"

                    document_result = transport.store_document_from_url(
                        url=source_path,
                        metadata=metadata,
                        title=title,
                        check_duplicates=True
                    )
                elif source_type == "file" and file_obj and filename:
                    document_result = transport.store_document(
                        file_obj=file_obj,
                        filename=filename,
                        content_type=content_type or "application/octet-stream",
                        source_path=source_path,
                        metadata=metadata,
                        title=title,
                        check_duplicates=True
                    )
                else:
                    raise ValueError(f"Unsupported source type: {source_type} or missing required parameters")

                document_id = document_result["document_id"]
                is_duplicate = document_result.get("is_duplicate", False)

                # If this is a duplicate document, we don't need to create a new processing task
                if is_duplicate:
                    logger.info(f"Document {document_id} is a duplicate, skipping processing task creation")
                    return {
                        "document_id": document_id,
                        "status": "already_exists",
                        "is_duplicate": True
                    }

                # Create processing task for new documents
                task_result = transport.create_processing_task(
                    document_id=document_id,
                    task_type="document_processing",
                    celery_task_id=task_id
                )

                return {
                    "document_id": document_id,
                    "status": "ingested",
                    "processing_task_id": task_result["task_id"],
                    "task_id": task_result["task_id"],
                    "is_duplicate": False
                }

            except Exception as e:
                logger.error(f"Error ingesting document from {source_path}: {str(e)}")
                raise

    @staticmethod
    def find_similar_documents(query_text: str, limit: int = 10, min_similarity: float = 0.0,
                              filter_document_id: Optional[Union[str, uuid.UUID]] = None) -> Dict[str, Any]:
        """
        Find similar documents using a text query with E5-Large-V2 embeddings.

        This method uses pgvector's similarity search to find document chunks
        that are semantically similar to the query text.

        Args:
            query_text: Query text
            limit: Maximum number of results
            min_similarity: Minimum similarity threshold (0.0 to 1.0)
            filter_document_id: Optional document ID to filter results by

        Returns:
            Dict[str, Any]: Search results with similar document chunks
        """
        try:
            # Generate embedding for query using E5-Large-V2 with query prefix
            query_embedding = NLPService.generate_query_embedding(query_text)

            # Search for similar chunks using pgvector similarity search
            with DataTransport() as transport:
                results = transport.search_similar_chunks(
                    embedding=query_embedding.tolist(),
                    limit=limit,
                    min_similarity=min_similarity,
                    filter_document_id=filter_document_id
                )

            return {
                "query": query_text,
                "results": results,
                "count": len(results)
            }

        except Exception as e:
            logger.error(f"Error finding similar documents with query '{query_text}': {str(e)}")
            raise

    @staticmethod
    def find_similar_by_chunk_id(chunk_id: Union[str, uuid.UUID], limit: int = 10,
                                min_similarity: float = 0.0, filter_document_id: Optional[Union[str, uuid.UUID]] = None) -> Dict[str, Any]:
        """
        Find chunks similar to a specific chunk using pgvector similarity search.

        This method is useful for finding related content to a specific chunk
        without needing to generate a new embedding.

        Args:
            chunk_id: ID of the chunk to find similar chunks for
            limit: Maximum number of results
            min_similarity: Minimum similarity threshold (0.0 to 1.0)
            filter_document_id: Optional document ID to filter results by

        Returns:
            Dict[str, Any]: Search results with similar document chunks
        """
        try:
            # Find similar chunks using pgvector similarity search
            with DataTransport() as transport:
                # Get the chunk to include its text in the response
                chunk = transport.db_client.get_chunk(chunk_id)
                if not chunk:
                    raise ValueError(f"Chunk with ID {chunk_id} not found")

                # Find similar chunks
                results = transport.find_similar_chunks_by_id(
                    chunk_id=chunk_id,
                    limit=limit,
                    min_similarity=min_similarity,
                    filter_document_id=filter_document_id
                )

            return {
                "source_chunk": {
                    "chunk_id": str(chunk.id),
                    "document_id": str(chunk.document_id),
                    "text": chunk.text
                },
                "results": results,
                "count": len(results)
            }

        except Exception as e:
            logger.error(f"Error finding similar chunks for chunk ID '{chunk_id}': {str(e)}")
            raise