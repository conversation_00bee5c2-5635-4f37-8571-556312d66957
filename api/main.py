"""
Main FastAPI application for longevity platform API.
"""
import logging
from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from typing import Dict, Any

from common.database import get_db, init_db, create_pgvector_extension
from common.config import settings
from api.routers import documents, nlp, knowledge_graph, entity_orchestrator, entities, tasks, pubmed
from api.routes import neo4j_routes
from workers import celery_app

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Setup database tables and extensions using lifespan context manager
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup: initialize database
    logger.info("Initializing database...")
    init_db()

    # Create pgvector extension
    db = next(get_db())
    try:
        create_pgvector_extension(db)
    finally:
        db.close()

    logger.info("Database initialized successfully")

    yield

    # Shutdown: cleanup resources if needed
    logger.info("Shutting down...")

# Create FastAPI app with lifespan
app = FastAPI(
    title="Longevity Research Platform API",
    description="API for processing and analyzing longevity research documents",
    version="0.1.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(documents.router, prefix="/api/documents", tags=["documents"])
app.include_router(nlp.router, prefix="/api/nlp", tags=["nlp"])
app.include_router(knowledge_graph.router, prefix="/api/knowledge-graph", tags=["knowledge-graph"])
app.include_router(entity_orchestrator.router, prefix="/api/entity-orchestrator", tags=["entity-orchestrator"])
app.include_router(entities.router, tags=["entities"])
app.include_router(tasks.router, prefix="/api/tasks", tags=["tasks"])
app.include_router(neo4j_routes.router, prefix="/api", tags=["neo4j"])
app.include_router(pubmed.router, prefix="/pubmed", tags=["pubmed"])


@app.get("/")
def read_root():
    """Root endpoint."""
    return {
        "name": "Longevity Research Platform API",
        "version": "0.1.0",
        "status": "online"
    }

@app.get("/health", response_model=Dict[str, Any])
async def health_check():
    """
    Check the health of the knowledge graph system.
    """
    try:
        # Check database connection
        db_status = "healthy"
        try:
            from transport.data_transport import DataTransport  # Ensure this import is correct
            with DataTransport() as transport:
                transport.db_client.check_connection()
        except Exception as e:
            db_status = f"unhealthy: {str(e)}"

        # Check Neo4j connection
        neo4j_status = "healthy"
        try:
            from transport.neo4j_client import Neo4jClient
            client = Neo4jClient()
            client.check_connection()
            client.close()
        except Exception as e:
            neo4j_status = f"unhealthy: {str(e)}"

        # Check Celery connection
        celery_status = "healthy"
        try:
            i = celery_app.control.inspect()
            if not i.ping():
                celery_status = "unhealthy: No response from workers"
        except Exception as e:
            celery_status = f"unhealthy: {str(e)}"

        return {
            "status": "ok" if all(s == "healthy" for s in [db_status, neo4j_status, celery_status]) else "degraded",
            "components": {
                "database": db_status,
                "neo4j": neo4j_status,
                "celery": celery_status
            }
        }
    except Exception as e:
        return {
            "status": "error",
            "message": str(e)
        }

@app.get("/config")
def get_config():
    """Get public configuration."""
    return {
        "chunk_size": settings.CHUNK_SIZE,
        "chunk_overlap": settings.CHUNK_OVERLAP,
        "embedding_model": settings.EMBEDDING_MODEL,
        "embedding_dimension": settings.EMBEDDING_DIMENSION,
        "max_document_size_mb": settings.MAX_DOCUMENT_SIZE_MB,
        "vertex_ai_model": getattr(settings, 'VERTEX_AI_MODEL_NAME', 'Not configured')
    }