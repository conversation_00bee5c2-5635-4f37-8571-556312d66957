"""
API dependencies for FastAPI.
"""
from typing import Generator
from fastapi import Depends
from sqlalchemy.orm import Session

from common.database import get_db
from transport.data_transport import DataTransport


def get_data_transport(db: Session = Depends(get_db)) -> Generator[DataTransport, None, None]:
    """
    Get DataTransport instance with database session.
    
    Args:
        db: Database session
        
    Returns:
        Generator[DataTransport, None, None]: DataTransport instance
    """
    transport = DataTransport(db)
    try:
        yield transport
    finally:
        # Close transport when done
        if hasattr(transport, 'db_session') and transport.db_session:
            transport.db_session.close()
