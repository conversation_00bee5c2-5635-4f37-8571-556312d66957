"""
Simple API for testing.
"""
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title="Longevity Research Platform API Test")

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def read_root():
    """Root endpoint."""
    return {
        "name": "Longevity Research Platform API Test",
        "version": "0.1.0",
        "status": "online"
    }

@app.get("/health")
def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "environment": "test"
    }

@app.post("/api/documents/upload")
async def upload_document():
    """Mock document upload endpoint."""
    return {
        "document_id": "test-document-id",
        "filename": "test-document.txt",
        "content_type": "text/plain",
        "status": "uploaded"
    }

@app.get("/api/documents/{document_id}")
async def get_document(document_id: str):
    """Mock document status endpoint."""
    return {
        "document_id": document_id,
        "filename": "test-document.txt",
        "content_type": "text/plain",
        "status": "processed",
        "chunks_count": 5,
        "entities_count": 10
    }

@app.get("/api/documents/{document_id}/entities")
async def get_document_entities(document_id: str):
    """Mock document entities endpoint."""
    return [
        {
            "id": "entity-1",
            "document_id": document_id,
            "chunk_id": "chunk-1",
            "text": "diabetes",
            "entity_type": "DISEASE",
            "confidence": 0.95
        },
        {
            "id": "entity-2",
            "document_id": document_id,
            "chunk_id": "chunk-1",
            "text": "hypertension",
            "entity_type": "DISEASE",
            "confidence": 0.92
        },
        {
            "id": "entity-3",
            "document_id": document_id,
            "chunk_id": "chunk-2",
            "text": "metformin",
            "entity_type": "MEDICATION",
            "confidence": 0.88
        }
    ]

@app.get("/api/documents/{document_id}/relationships")
async def get_document_relationships(document_id: str):
    """Mock document relationships endpoint."""
    return [
        {
            "id": "relationship-1",
            "document_id": document_id,
            "source_id": "entity-1",
            "target_id": "entity-3",
            "relationship_type": "TREATS",
            "confidence": 0.85
        }
    ]

@app.get("/api/entities/search")
async def search_entities(query: str):
    """Mock entity search endpoint."""
    return {
        "query": query,
        "results": [
            {
                "id": "entity-1",
                "text": "diabetes",
                "entity_type": "DISEASE",
                "similarity": 0.95
            }
        ]
    }
