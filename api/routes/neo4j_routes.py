"""
API routes for Neo4j operations.
"""
from typing import Dict, Any, List
from fastapi import APIRouter, HTTPException
from neo4j import GraphDatabase
import logging

from common.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/neo4j",
    tags=["neo4j"],
    responses={404: {"description": "Not found"}},
)

@router.get("/document/{document_id}/verify", response_model=Dict[str, Any])
async def verify_document_relationships(document_id: str):
    """
    Verify relationships between a document and its chunks in Neo4j.

    Args:
        document_id: Document ID

    Returns:
        Dict[str, Any]: Verification results
    """
    # Connect to Neo4j
    driver = GraphDatabase.driver(
        settings.NEO4J_URI,
        auth=(settings.NEO4J_USER, settings.NEO4J_PASSWORD)
    )

    try:
        # Check if document exists
        document_query = """
        MATCH (d:Document {id: $document_id})
        RETURN d.id as id
        """

        # Count chunks for document with PART_OF relationship
        chunks_query = """
        MATCH (c:Chunk)-[:PART_OF]->(d:Document {id: $document_id})
        RETURN count(c) as chunk_count
        """

        # Count chunks that should belong to this document but don't have PART_OF relationship
        document_orphaned_chunks_query = """
        MATCH (c:Chunk)
        WHERE c.document_id = $document_id AND NOT (c)-[:PART_OF]->(:Document {id: $document_id})
        RETURN count(c) as orphaned_count
        """

        # Count all orphaned chunks in the database
        all_orphaned_chunks_query = """
        MATCH (c:Chunk)
        WHERE NOT (c)-[:PART_OF]->(:Document)
        RETURN count(c) as orphaned_count
        """

        # Get sample of chunks for document
        sample_chunks_query = """
        MATCH (c:Chunk)-[:PART_OF]->(d:Document {id: $document_id})
        RETURN c.id as id, c.chunk_index as chunk_index
        ORDER BY c.chunk_index
        LIMIT 5
        """

        # Get sample of orphaned chunks for this document
        sample_orphaned_query = """
        MATCH (c:Chunk)
        WHERE c.document_id = $document_id AND NOT (c)-[:PART_OF]->(:Document {id: $document_id})
        RETURN c.id as id, c.chunk_index as chunk_index
        LIMIT 5
        """

        results = {
            "document_id": document_id,
            "document_exists": False,
            "chunk_count": 0,
            "document_orphaned_chunks": 0,
            "all_orphaned_chunks": 0,
            "sample_chunks": [],
            "sample_orphaned_chunks": []
        }

        with driver.session() as session:
            # Check document
            logger.info(f"Checking if document {document_id} exists in Neo4j")
            document_result = session.run(document_query, document_id=document_id)
            document_record = document_result.single()
            results["document_exists"] = document_record is not None

            # Count chunks with proper relationships
            if results["document_exists"]:
                logger.info(f"Counting chunks with PART_OF relationship to document {document_id}")
                chunks_result = session.run(chunks_query, document_id=document_id)
                chunks_record = chunks_result.single()
                results["chunk_count"] = chunks_record["chunk_count"] if chunks_record else 0

                # Get sample chunks
                sample_result = session.run(sample_chunks_query, document_id=document_id)
                for record in sample_result:
                    results["sample_chunks"].append({
                        "id": record["id"],
                        "chunk_index": record["chunk_index"]
                    })

            # Count orphaned chunks for this document
            logger.info(f"Counting orphaned chunks for document {document_id}")
            doc_orphaned_result = session.run(document_orphaned_chunks_query, document_id=document_id)
            doc_orphaned_record = doc_orphaned_result.single()
            results["document_orphaned_chunks"] = doc_orphaned_record["orphaned_count"] if doc_orphaned_record else 0

            # Get sample of orphaned chunks for this document
            if results["document_orphaned_chunks"] > 0:
                sample_orphaned_result = session.run(sample_orphaned_query, document_id=document_id)
                for record in sample_orphaned_result:
                    results["sample_orphaned_chunks"].append({
                        "id": record["id"],
                        "chunk_index": record["chunk_index"]
                    })

            # Count all orphaned chunks
            logger.info("Counting all orphaned chunks in Neo4j")
            all_orphaned_result = session.run(all_orphaned_chunks_query)
            all_orphaned_record = all_orphaned_result.single()
            results["all_orphaned_chunks"] = all_orphaned_record["orphaned_count"] if all_orphaned_record else 0

        return results
    except Exception as e:
        logger.error(f"Error verifying document relationships: {e}")
        raise HTTPException(status_code=500, detail=f"Error verifying document relationships: {str(e)}")
    finally:
        driver.close()

@router.post("/document/{document_id}/fix", response_model=Dict[str, Any])
async def fix_document_relationships(document_id: str):
    """
    Fix relationships between a document and its chunks in Neo4j.

    Args:
        document_id: Document ID

    Returns:
        Dict[str, Any]: Fix results
    """
    # Connect to Neo4j
    driver = GraphDatabase.driver(
        settings.NEO4J_URI,
        auth=(settings.NEO4J_USER, settings.NEO4J_PASSWORD)
    )

    try:
        # First get verification results before fixing
        before_results = await verify_document_relationships(document_id)

        # If there are no orphaned chunks, return early
        if before_results["document_orphaned_chunks"] == 0:
            return {
                "document_id": document_id,
                "before": before_results,
                "after": before_results,
                "fixed": {
                    "document_id": document_id,
                    "document_created": before_results["document_exists"],
                    "chunks_fixed": 0,
                    "fixed_chunks": []
                }
            }

        # First ensure document exists
        create_document_query = """
        MERGE (d:Document {id: $document_id})
        RETURN d.id as id
        """

        # Find chunks that have document_id property but no PART_OF relationship
        find_chunks_query = """
        MATCH (c:Chunk)
        WHERE c.document_id = $document_id AND NOT (c)-[:PART_OF]->(:Document {id: $document_id})
        RETURN c.id as id
        """

        # Create PART_OF relationship for all chunks in a single query
        create_relationships_query = """
        MATCH (c:Chunk)
        WHERE c.id IN $chunk_ids
        MATCH (d:Document {id: $document_id})
        MERGE (c)-[:PART_OF]->(d)
        RETURN count(c) as fixed_count
        """

        fix_results = {
            "document_id": document_id,
            "document_created": False,
            "chunks_fixed": 0,
            "fixed_chunks": []
        }

        with driver.session() as session:
            # Ensure document exists
            logger.info(f"Ensuring document {document_id} exists in Neo4j")
            document_result = session.run(create_document_query, document_id=document_id)
            document_record = document_result.single()
            fix_results["document_created"] = document_record is not None

            if not fix_results["document_created"]:
                logger.error(f"Failed to create document {document_id}")
                raise HTTPException(status_code=500, detail=f"Failed to create document {document_id}")

            # Find orphaned chunks
            logger.info(f"Finding orphaned chunks for document {document_id}")
            chunks_result = session.run(find_chunks_query, document_id=document_id)
            chunk_ids = [record["id"] for record in chunks_result]

            if not chunk_ids:
                logger.info(f"No orphaned chunks found for document {document_id}")
                return {
                    "document_id": document_id,
                    "before": before_results,
                    "after": before_results,
                    "fixed": fix_results
                }

            # Fix all orphaned chunks in a single query
            logger.info(f"Fixing {len(chunk_ids)} orphaned chunks for document {document_id}")
            fix_result = session.run(
                create_relationships_query,
                chunk_ids=chunk_ids,
                document_id=document_id
            )
            record = fix_result.single()

            if record:
                fix_results["chunks_fixed"] = record["fixed_count"]
                fix_results["fixed_chunks"] = chunk_ids

            logger.info(f"Fixed {fix_results['chunks_fixed']} orphaned chunks for document {document_id}")

        # Get verification results after fixing
        after_results = await verify_document_relationships(document_id)

        return {
            "document_id": document_id,
            "before": before_results,
            "after": after_results,
            "fixed": fix_results
        }
    except Exception as e:
        logger.error(f"Error fixing document relationships: {e}")
        raise HTTPException(status_code=500, detail=f"Error fixing document relationships: {str(e)}")
    finally:
        driver.close()

@router.get("/documents/verify", response_model=List[Dict[str, Any]])
async def verify_all_documents():
    """
    Verify relationships for all documents in Neo4j.

    Returns:
        List[Dict[str, Any]]: Verification results for all documents
    """
    # Connect to Neo4j
    driver = GraphDatabase.driver(
        settings.NEO4J_URI,
        auth=(settings.NEO4J_USER, settings.NEO4J_PASSWORD)
    )

    try:
        # Get all documents directly from the database
        documents = []
        with driver.session() as session:
            result = session.run("MATCH (d:Document) RETURN d.id as id, d.filename as filename")
            for record in result:
                documents.append({
                    "id": record["id"],
                    "filename": record.get("filename", "Unknown")
                })

        # Verify each document
        results = []
        for document in documents:
            document_id = document["id"]
            verification = await verify_document_relationships(document_id)
            verification["filename"] = document["filename"]
            results.append(verification)

        return results
    except Exception as e:
        logger.error(f"Error verifying all documents: {e}")
        raise HTTPException(status_code=500, detail=f"Error verifying all documents: {str(e)}")
    finally:
        driver.close()

@router.post("/documents/fix", response_model=List[Dict[str, Any]])
async def fix_all_documents():
    """
    Fix relationships for all documents in Neo4j.

    Returns:
        List[Dict[str, Any]]: Fix results for all documents
    """
    # Connect to Neo4j
    driver = GraphDatabase.driver(
        settings.NEO4J_URI,
        auth=(settings.NEO4J_USER, settings.NEO4J_PASSWORD)
    )

    try:
        # Get all documents directly from the database
        documents = []
        with driver.session() as session:
            result = session.run("MATCH (d:Document) RETURN d.id as id, d.filename as filename")
            for record in result:
                documents.append({
                    "id": record["id"],
                    "filename": record.get("filename", "Unknown")
                })

        # Fix each document
        results = []
        for document in documents:
            document_id = document["id"]
            fix_result = await fix_document_relationships(document_id)
            fix_result["filename"] = document["filename"]
            results.append(fix_result)

        return results
    except Exception as e:
        logger.error(f"Error fixing all documents: {e}")
        raise HTTPException(status_code=500, detail=f"Error fixing all documents: {str(e)}")
    finally:
        driver.close()
