"""
NLP and entity API router for longevity platform.
"""
import uuid
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, UUID4
from sqlalchemy.orm import Session

from common.database import get_db
from transport.data_transport import DataTransport
from workers.celery_app import celery_app


router = APIRouter()


class EntityExtractionRequest(BaseModel):
    """Entity extraction request model."""
    document_id: UUID4


class EntityExtractionResponse(BaseModel):
    """Entity extraction response model."""
    task_id: str
    status: str
    document_id: str
    message: str


class TextEmbeddingRequest(BaseModel):
    """Text embedding request model."""
    texts: List[str]


class TextEmbeddingResponse(BaseModel):
    """Text embedding response model."""
    embeddings: List[List[float]]


class SemanticSearchRequest(BaseModel):
    """Semantic search request model."""
    query: str
    passage_embeddings: List[List[float]]
    passages: List[Dict[str, Any]]
    top_k: int = 5


class SearchResultResponse(BaseModel):
    """Semantic search result response model."""
    results: List[Dict[str, Any]]


class EntityExtractionTextRequest(BaseModel):
    """Entity extraction from text request model."""
    text: str


class EntityResponse(BaseModel):
    """Entity response model."""
    id: str
    text: str
    type: str
    score: float
    normalized_id: Optional[str] = None


class NLPModelStatusResponse(BaseModel):
    """NLP model status response model."""
    status: str
    models_initialized: bool
    message: str


@router.post("/extract-entities", response_model=EntityExtractionResponse)
async def extract_document_entities(
    request: EntityExtractionRequest,
    db: Session = Depends(get_db)
):
    """
    Extract entities from a document.

    Args:
        request: Entity extraction request
        db: Database session

    Returns:
        EntityExtractionResponse: Entity extraction task information
    """
    try:
        document_id = str(request.document_id)

        # Create processing task
        with DataTransport(db) as transport:
            task_result = transport.create_processing_task(
                document_id=document_id,
                task_type="entity_extraction"
            )

            # Queue entity extraction in Celery
            celery_app.send_task(
                "workers.entity.extract_entities",
                args=[document_id, task_result["task_id"]]
            )

            return {
                "task_id": task_result["task_id"],
                "status": "queued",
                "document_id": document_id,
                "message": "Entity extraction queued successfully"
            }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error queuing entity extraction: {str(e)}"
        )


@router.get("/entities/{document_id}", response_model=Dict[str, Any])
async def get_document_entities(
    document_id: UUID4,
    db: Session = Depends(get_db)
):
    """
    Get entities extracted from a document.

    Args:
        document_id: Document ID
        db: Database session

    Returns:
        Dict[str, Any]: Document entities
    """
    try:
        with DataTransport(db) as transport:
            # Get entities from database
            entities = transport.get_document_entities(document_id)

            return {
                "document_id": str(document_id),
                "entities": entities,
                "count": len(entities)
            }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error getting document entities: {str(e)}"
        )


@router.get("/relationships/{document_id}", response_model=Dict[str, Any])
async def get_document_relationships(
    document_id: UUID4,
    db: Session = Depends(get_db)
):
    """
    Get relationships extracted from a document.

    Args:
        document_id: Document ID
        db: Database session

    Returns:
        Dict[str, Any]: Document relationships
    """
    try:
        with DataTransport(db) as transport:
            # Get relationships from database
            relationships = transport.get_document_relationships(document_id)

            return {
                "document_id": str(document_id),
                "relationships": relationships,
                "count": len(relationships)
            }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error getting document relationships: {str(e)}"
        )


@router.post("/embeddings", response_model=TextEmbeddingResponse)
async def generate_embeddings_api(
    request: TextEmbeddingRequest
):
    """
    Generate embeddings for a list of texts.

    Args:
        request: Text embedding request

    Returns:
        TextEmbeddingResponse: Generated embeddings
    """
    try:
        # Queue task in Celery
        task = celery_app.send_task(
            "workers.nlp.generate_embeddings",
            args=[request.texts]
        )

        # Wait for result with timeout
        result = task.get(timeout=30)

        if result.get("status") == "error":
            raise Exception(result.get("error", "Unknown error"))

        return {
            "embeddings": result.get("embeddings", [])
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error generating embeddings: {str(e)}"
        )


@router.post("/biomedical-embeddings", response_model=TextEmbeddingResponse)
async def generate_biomedical_embeddings_api(
    request: TextEmbeddingRequest
):
    """
    Generate biomedical embeddings for a list of texts using BioBERT.

    Args:
        request: Text embedding request

    Returns:
        TextEmbeddingResponse: Generated biomedical embeddings
    """
    try:
        # Queue task in Celery
        task = celery_app.send_task(
            "workers.nlp.generate_biomedical_embeddings",
            args=[request.texts]
        )

        # Wait for result with timeout
        result = task.get(timeout=30)

        if result.get("status") == "error":
            raise Exception(result.get("error", "Unknown error"))

        return {
            "embeddings": result.get("embeddings", [])
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error generating biomedical embeddings: {str(e)}"
        )


@router.post("/semantic-search-embeddings", response_model=TextEmbeddingResponse)
async def generate_semantic_search_embeddings_api(
    request: TextEmbeddingRequest,
    is_query: bool = False
):
    """
    Generate embeddings for semantic search using E5-Large-V2 model.

    E5-Large-V2 is a state-of-the-art embedding model that performs
    exceptionally well for semantic search tasks.

    Args:
        request: Text embedding request
        is_query: Whether the texts are search queries (True) or passages (False)

    Returns:
        TextEmbeddingResponse: Generated semantic search embeddings
    """
    try:
        # Queue task in Celery
        task = celery_app.send_task(
            "workers.nlp.generate_semantic_search_embeddings",
            args=[request.texts, is_query]
        )

        # Wait for result with timeout
        result = task.get(timeout=30)

        if result.get("status") == "error":
            raise Exception(result.get("error", "Unknown error"))

        return {
            "embeddings": result.get("embeddings", [])
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error generating semantic search embeddings: {str(e)}"
        )


@router.post("/semantic-search", response_model=SearchResultResponse)
async def semantic_search_api(
    request: SemanticSearchRequest
):
    """
    Perform semantic search using E5-Large-V2 embeddings.

    Args:
        request: Semantic search request

    Returns:
        SearchResultResponse: Search results
    """
    try:
        # Queue task in Celery
        task = celery_app.send_task(
            "workers.nlp.semantic_search",
            args=[
                request.query,
                request.passage_embeddings,
                request.passages,
                request.top_k
            ]
        )

        # Wait for result with timeout
        result = task.get(timeout=30)

        if result.get("status") == "error":
            raise Exception(result.get("error", "Unknown error"))

        return {
            "results": result.get("results", [])
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error performing semantic search: {str(e)}"
        )


@router.post("/extract-entities-from-text", response_model=List[EntityResponse])
async def extract_entities_from_text_api(
    request: EntityExtractionTextRequest
):
    """
    Extract biomedical entities from text.

    Args:
        request: Entity extraction from text request

    Returns:
        List[EntityResponse]: Extracted entities
    """
    try:
        # Queue task in Celery
        task = celery_app.send_task(
            "workers.nlp.extract_entities_from_text",
            args=[request.text]
        )

        # Wait for result with timeout
        result = task.get(timeout=30)

        if result.get("status") == "error":
            raise Exception(result.get("error", "Unknown error"))

        return result.get("entities", [])

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error extracting entities from text: {str(e)}"
        )


@router.post("/initialize-models", response_model=NLPModelStatusResponse)
async def initialize_nlp_models_api():
    """
    Initialize NLP models through Celery task.

    Returns:
        NLPModelStatusResponse: Initialization status
    """
    try:
        # Queue task in Celery
        task = celery_app.send_task(
            "workers.nlp.initialize_models"  # This matches the task name in nlp_worker.py
        )

        # Wait for result with timeout
        result = task.get(timeout=120)  # Allow up to 2 minutes for model initialization

        if result.get("status") == "error":
            raise Exception(result.get("message", "Unknown error"))

        return result

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error initializing NLP models: {str(e)}"
        )


@router.get("/models-status", response_model=NLPModelStatusResponse)
async def check_nlp_models_status_api():
    """
    Check if NLP models are initialized through Celery task.

    Returns:
        NLPModelStatusResponse: Status of NLP models
    """
    try:
        # Queue task in Celery
        task = celery_app.send_task(
            "workers.nlp.check_models_status"  # This matches the task name in nlp_worker.py
        )

        # Wait for result with timeout
        result = task.get(timeout=10)

        return result

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error checking NLP models status: {str(e)}"
        )
