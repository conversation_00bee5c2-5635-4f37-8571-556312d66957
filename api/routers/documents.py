"""
Document API router for longevity platform.
"""
import os
import uuid
import json
from typing import List, Optional, Dict, Any
import logging
from fastapi import APIRouter, Body, Depends, HTTPException, UploadFile, File, Form, Query
from fastapi.responses import JSONResponse, StreamingResponse
from pydantic import BaseModel, HttpUrl, UUID4
from sqlalchemy.orm import Session

from common.database import get_db, Document
from api.transport.data_transport_api import DataTransportAPI
from workers.celery_app import celery_app
import celery

router = APIRouter()

# Configure logger
logger = logging.getLogger("documents_router")
logging.basicConfig(level=logging.INFO)
router = APIRouter()


# Models for request and response
class DocumentResponse(BaseModel):
    """Document response model."""
    document_id: str
    filename: str
    title: Optional[str] = None
    content_type: str
    file_size: Optional[int] = None
    status: str
    created_at: Optional[str] = None
    updated_at: Optional[str] = None
    chunk_count: Optional[int] = None


class DocumentListResponse(BaseModel):
    """List of documents response model."""
    documents: List[DocumentResponse]
    count: int


class IngestRequest(BaseModel):
    """Document ingestion request model."""
    url: Optional[HttpUrl] = None
    metadata: Optional[Dict[str, Any]] = None


class SearchRequest(BaseModel):
    """Search request model."""
    query: str
    limit: Optional[int] = 10


class SearchResponse(BaseModel):
    """Search response model."""
    query: str
    results: List[Dict[str, Any]]
    count: int


class GdriveIngestRequest(BaseModel):
    """Request model for Google Drive ingestion."""
    folder_id: Optional[str] = None  # If None, uses the default folder from settings


class GdriveMonitorRequest(BaseModel):
    """Request model for Google Drive ingestion monitoring."""
    task_id: str


@router.post("/gdrive/ingest", response_model=Dict[str, Any])
async def trigger_gdrive_ingestion(
    request: GdriveIngestRequest = Body(default=None)
):
    """
    Manually trigger Google Drive document ingestion.
    
    This endpoint will search the configured Google Drive folder (or a specific folder if provided)
    for new documents and ingest them into the system.

    Args:
        request: Optional request with folder_id parameter

    Returns:
        Dict[str, Any]: Ingestion task details
    """
    try:
        folder_id = None
        if request:
            folder_id = request.folder_id
            
        # Queue Google Drive ingestion task
        task = celery_app.send_task(
            "workers.gdrive.check_for_new_documents",
            kwargs={"folder_id": folder_id}
        )
        
        return {
            "task_id": task.id,
            "status": "queued",
            "message": "Google Drive ingestion task queued successfully",
            "folder_id": folder_id or "default"
        }
    except Exception as e:
        logger.error(f"Error triggering Google Drive ingestion: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Error triggering Google Drive ingestion: {str(e)}"
        )


@router.get("/gdrive/monitor/{task_id}", response_model=Dict[str, Any])
async def monitor_gdrive_ingestion(
    task_id: str,
    db: Session = Depends(get_db)
):
    """
    Monitor the status of a Google Drive ingestion process.
    
    This endpoint will check the status of a previously triggered Google Drive ingestion task
    and return the current state, including processed documents if available.

    Args:
        task_id: The Celery task ID returned from the trigger_gdrive_ingestion endpoint

    Returns:
        Dict[str, Any]: Current status of the ingestion task and any processed documents
    """
    try:
        # Get task status from Celery
        task_result = celery_app.AsyncResult(task_id)
        
        # Construct base response
        response = {
            "task_id": task_id,
            "status": task_result.status,
            "state": task_result.state,
        }
        
        # Add task info if task is ready
        if task_result.ready():
            if task_result.successful():
                result = task_result.result
                if isinstance(result, dict):
                    # Add task result information if available
                    response.update({
                        "processed_count": result.get("processed_count", 0),
                        "failed_count": result.get("failed_count", 0),
                        "skipped_count": result.get("skipped_count", 0),
                        "document_ids": result.get("document_ids", []),
                        "completed_at": result.get("completed_at")
                    })
                    
                    # If we have document IDs, enrich with document information
                    if "document_ids" in result and result["document_ids"]:
                        # Get documents from database
                        documents = db.query(Document).filter(
                            Document.id.in_([uuid.UUID(doc_id) for doc_id in result["document_ids"]])
                        ).all()
                        
                        # Add basic document info
                        response["documents"] = [
                            {
                                "document_id": str(doc.id),
                                "filename": doc.filename,
                                "status": doc.status,
                                "created_at": str(doc.created_at) if doc.created_at else None
                            }
                            for doc in documents
                        ]
                else:
                    # Simple result
                    response["result"] = str(result)
            else:
                # Task failed
                response["error"] = str(task_result.result)
        else:
            # Task is still running - try to get progress info if available
            if hasattr(task_result, "info") and task_result.info:
                if isinstance(task_result.info, dict):
                    response["progress"] = task_result.info
        
        return response
    except Exception as e:
        logger.error(f"Error monitoring Google Drive ingestion: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Error monitoring Google Drive ingestion: {str(e)}"
        )


# Removed redundant upload_document endpoint in favor of the unified ingest_document endpoint


@router.post("/ingest", response_model=Dict[str, Any])
async def ingest_document(
    url: Optional[str] = Form(None),
    file: Optional[UploadFile] = File(None),
    metadata_str: Optional[str] = Form(None),
    title: Optional[str] = Form(None)
):
    """
    Ingest a document from a URL or file upload.

    Args:
        url: Optional URL for document ingestion
        file: Optional uploaded file
        metadata_str: Optional JSON metadata string
        title: Optional document title

    Returns:
        Dict[str, Any]: Document information
    """
    try:
        # Check if we have either a URL or a file
        has_url = url is not None and url.strip() != ""

        if has_url and file:
            raise HTTPException(
                status_code=400,
                detail="Cannot provide both URL and file. Choose one ingestion method."
            )

        # Debug information
        print(f"URL: {url}")
        print(f"File: {file}")

        if not (has_url or file):
            raise HTTPException(
                status_code=400,
                detail="Must provide either URL or file for ingestion."
            )

        # Process metadata
        parsed_metadata = {}
        if metadata_str:
            try:
                parsed_metadata = json.loads(metadata_str)
            except json.JSONDecodeError:
                raise HTTPException(
                    status_code=400,
                    detail="Invalid metadata format. Must be valid JSON."
                )

        # Process URL ingestion
        # Determine source type and prepare task parameters
        if has_url:
            source_type = "url"
            source_path = str(url)

            # Extract filename from URL path
            from urllib.parse import urlparse
            parsed_url = urlparse(url)
            url_path = parsed_url.path
            url_filename = os.path.basename(url_path)

            # If filename is empty or doesn't have an extension, use a generic name
            if not url_filename or '.' not in url_filename:
                url_filename = f"document_{uuid.uuid4()}.txt"
        else:
            # Read file content
            file_content = await file.read()
            file_size = len(file_content)

            # Validate file size
            max_size = 1024 * 1024 * 50  # 50 MB
            if file_size > max_size:
                raise HTTPException(
                    status_code=413,
                    detail=f"File too large. Maximum size is 50 MB."
                )

            source_type = "file"
            source_path = "upload"

        # Queue ingestion task with callback to process_document
        task = celery_app.send_task(
            "workers.document.ingest_document",
            kwargs={
            "use_batch_api": True,
            "source_type": source_type,
            "source_path": source_path,
            "metadata": parsed_metadata,
            "file_content": file_content if not has_url else None,
            "filename": file.filename if not has_url else url_filename,
            "content_type": file.content_type if not has_url else None,
            "title": title,
            "callback_task": "workers.document.process_document",
            "build_knowledge_graph": True,
            }
        )

        # Get the task result to check for duplicates
        task_result = task.get(timeout=10)  # Wait for a short time to get the result

        # Check if the document is a duplicate
        if task_result and isinstance(task_result, dict) and task_result.get('is_duplicate', False):
            return {
                "document_id": task_result.get("document_id"),
                "status": "already_exists",
                "message": f"Document already exists in the system",
                "url": str(url) if has_url else None,
                "filename": file.filename if not has_url else url_filename,
                "is_duplicate": True
            }

        return {
            "task_id": task.id,
            "status": "queued",
            "message": f"Document ingestion from {'URL' if has_url else 'file upload'} queued successfully",
            "url": str(url) if has_url else None,
            "filename": file.filename if not has_url else url_filename,
            "is_duplicate": False
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error ingesting document: {str(e)}"
        )


@router.get("/list", response_model=DocumentListResponse)
async def list_documents(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    List all documents.

    Args:
        skip: Number of documents to skip
        limit: Maximum number of documents to return
        status: Filter by status

    Returns:
        DocumentListResponse: List of documents
    """
    try:
        from common.database import Document

        # Build query
        query = db.query(Document)

        if status:
            query = query.filter(Document.status == status)

        # Get count
        total_count = query.count()

        # Get documents
        documents = query.order_by(Document.created_at.desc()).offset(skip).limit(limit).all()

        # Convert to response model
        document_responses = [doc.to_dict() for doc in documents]

        return {
            "documents": document_responses,
            "count": total_count
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error listing documents: {str(e)}"
        )


@router.get("/{document_id}", response_model=DocumentResponse)
async def get_document(
    document_id: UUID4,
    db: Session = Depends(get_db)
):
    """
    Get document by ID.

    Args:
        document_id: Document ID

    Returns:
        DocumentResponse: Document information
    """
    try:
        from common.database import Document

        document = db.query(Document).filter(Document.id == document_id).first()

        if not document:
            raise HTTPException(
                status_code=404,
                detail=f"Document with ID {document_id} not found"
            )

        return document.to_dict()

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error getting document: {str(e)}"
        )


@router.get("/{document_id}/download")
async def download_document(
    document_id: UUID4,
    db: Session = Depends(get_db)
):
    """
    Download document content.

    Args:
        document_id: Document ID

    Returns:
        StreamingResponse: Document content
    """
    try:
        with DataTransportAPI(db) as transport:
            # Get document content and metadata
            content, metadata = transport.get_document_content(document_id)

            # Return streaming response
            return StreamingResponse(
                iter([content]),
                media_type=metadata["content_type"],
                headers={
                    "Content-Disposition": f"attachment; filename=\"{metadata['filename']}\""
                }
            )

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error downloading document: {str(e)}"
        )


@router.get("/{document_id}/chunks")
async def get_document_chunks(
    document_id: UUID4,
    db: Session = Depends(get_db)
):
    """
    Get document chunks.

    Args:
        document_id: Document ID

    Returns:
        Dict[str, Any]: Document chunks
    """
    try:
        with DataTransportAPI(db) as transport:
            chunks = transport.db_client.get_document_chunks(document_id)

            return {
                "document_id": str(document_id),
                "chunks": [chunk.to_dict() for chunk in chunks],
                "count": len(chunks)
            }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error getting document chunks: {str(e)}"
        )


class ProcessDocumentRequest(BaseModel):
    """Request model for document processing."""
    use_batch_api: bool = False
    build_knowledge_graph: bool = True


class BatchProcessRequest(BaseModel):
    """Request model for batch document processing."""
    document_ids: List[UUID4]


@router.post("/{document_id}/process")
async def process_document_with_options(
    document_id: UUID4,
    request: ProcessDocumentRequest,
    db: Session = Depends(get_db)
):
    """
    Process a document to extract entities and relationships with options.

    Args:
        document_id: Document ID
        request: Processing request with options
        db: Database session

    Returns:
        Dict[str, Any]: Processing task information
    """
    try:
        with DataTransportAPI(db) as transport:
            # Check if document exists
            document = db.query(Document).filter(Document.id == document_id).first()
            if not document:
                raise HTTPException(
                    status_code=404,
                    detail=f"Document with ID {document_id} not found"
                )

            # Create processing task
            task_result = transport.create_processing_task(
                document_id=document_id,
                task_type="document_processing"
            )

            # Queue document processing in Celery
            celery_task = celery_app.send_task(
                "workers.document.process_document",
                args=[str(document_id), task_result["task_id"]],
                kwargs={
                    "use_batch_api": request.use_batch_api,
                    "build_knowledge_graph": request.build_knowledge_graph
                }
            )

            return {
                "task_id": task_result["task_id"],
                "status": "queued",
                "document_id": str(document_id),
                "message": f"Document processing queued with Celery task ID: {celery_task.id}"
            }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error processing document: {str(e)}"
        )


@router.post("/batch-process")
async def batch_process_documents(
    request: BatchProcessRequest,
    db: Session = Depends(get_db)
):
    """
    Process multiple documents in batch mode.

    Args:
        request: Batch processing request with document IDs
        db: Database session

    Returns:
        Dict[str, Any]: Batch processing task information
    """
    try:
        with DataTransportAPI(db) as transport:
            # Check if documents exist
            document_ids = []
            for doc_id in request.document_ids:
                document = db.query(Document).filter(Document.id == doc_id).first()
                if not document:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Document with ID {doc_id} not found"
                    )
                document_ids.append(str(doc_id))

            # Create batch processing task
            task_result = transport.create_processing_task(
                document_id=None,  # No specific document
                task_type="batch_document_processing",
                metadata={"document_ids": document_ids}
            )

            # Queue batch processing in Celery
            celery_task = celery_app.send_task(
                "workers.document.batch_process_documents",
                args=[document_ids, task_result["task_id"]],
                kwargs={"use_batch_api": True}  # Always use batch API for batch processing
            )

            return {
                "task_id": task_result["task_id"],
                "status": "queued",
                "document_count": len(document_ids),
                "message": f"Batch processing queued with Celery task ID: {celery_task.id}"
            }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error batch processing documents: {str(e)}"
        )


class SimilaritySearchRequest(BaseModel):
    """Similarity search request model."""
    query: str
    limit: int = 10
    min_similarity: float = 0.0
    filter_document_id: Optional[str] = None


class ChunkSimilarityRequest(BaseModel):
    """Chunk similarity request model."""
    chunk_id: str
    limit: int = 10
    min_similarity: float = 0.0
    filter_document_id: Optional[str] = None


@router.post("/search", response_model=SearchResponse)
async def search(
    request: SimilaritySearchRequest
):
    """
    Search for semantically similar documents using a text query.

    Args:
        request: Search request with query text and parameters

    Returns:
        SearchResponse: Search results with similar document chunks
    """
    try:
        # Queue semantic search task
        task = celery_app.send_task(
            "workers.document.find_similar_documents",
            kwargs={
                "query_text": request.query,
                "limit": request.limit,
                "min_similarity": request.min_similarity,
                "filter_document_id": request.filter_document_id
            }
        )

        # Improve timeout handling
        try:
            result = task.get(timeout=30)
        except celery.exceptions.TimeoutError:
            raise HTTPException(
                status_code=504,
                detail="Search operation timed out. Try with a simpler query or smaller limit."
            )

        return result

    except Exception as e:
        # Log the specific error for debugging
        logger.error(f"Error in search endpoint: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Error searching for similar documents: {str(e)}"
        )


@router.post("/similar-chunks/{chunk_id}", response_model=SearchResponse)
async def find_similar_chunks(
    chunk_id: str,
    request: ChunkSimilarityRequest
):
    """
    Find chunks similar to a specific chunk using pgvector similarity search.

    This endpoint is useful for finding related content to a specific chunk
    without needing to generate a new embedding.

    Args:
        chunk_id: ID of the chunk to find similar chunks for
        request: Similarity request parameters

    Returns:
        SearchResponse: Search results with similar document chunks
    """
    try:
        # Queue similarity search task
        task = celery_app.send_task(
            "workers.document.find_similar_by_chunk_id",
            kwargs={
                "chunk_id": chunk_id,
                "limit": request.limit,
                "min_similarity": request.min_similarity,
                "filter_document_id": request.filter_document_id
            }
        )

        # Wait for result with timeout
        result = task.get(timeout=30)

        return result

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error finding similar chunks: {str(e)}"
        )