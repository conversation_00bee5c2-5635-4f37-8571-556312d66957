"""
API router for entity operations.
"""
import uuid
import logging
from typing import Optional
from fastapi import APIRouter, HTTPException, Depends, Query
from sqlalchemy.orm import Session

from common.database import get_db, Entity
from workers.celery_app import celery_app

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/api/entities",
    tags=["entities"],
    responses={404: {"description": "Not found"}},
)


@router.get("/")
async def list_entities(
    entity_type: Optional[str] = None,
    limit: int = 100,
    offset: int = 0,
    db: Session = Depends(get_db)
):
    """
    List entities with optional filtering by type.

    Args:
        entity_type: Optional entity type filter
        limit: Maximum number of entities to return
        offset: Number of entities to skip
        db: Database session

    Returns:
        dict: List of entities
    """
    try:
        # Build query
        query = db.query(Entity)

        # Apply filters
        if entity_type:
            query = query.filter(Entity.entity_type == entity_type)

        # Get total count
        total_count = query.count()

        # Get entities with pagination
        entities = query.order_by(Entity.created_at.desc()).offset(offset).limit(limit).all()

        # Format entities for response
        formatted_entities = []
        for entity in entities:
            formatted_entity = {
                "id": str(entity.id),
                "document_id": str(entity.document_id),
                "chunk_id": str(entity.chunk_id) if entity.chunk_id else None,
                "text": entity.text,
                "entity_type": entity.entity_type,
                "normalized_id": entity.normalized_id,
                "start_pos": entity.start_pos,
                "end_pos": entity.end_pos,
                "confidence": entity.confidence,
                "created_at": entity.created_at.isoformat()
            }

            # Add metadata if available
            if entity.entity_metadata:
                # Remove embedding from metadata to reduce size
                metadata = entity.entity_metadata.copy()
                if "embedding" in metadata:
                    del metadata["embedding"]
                formatted_entity["metadata"] = metadata

            formatted_entities.append(formatted_entity)

        return {
            "entities": formatted_entities,
            "count": total_count,
            "limit": limit,
            "offset": offset
        }

    except Exception as e:
        logger.error(f"Error listing entities: {e}")
        raise HTTPException(status_code=500, detail=f"Error listing entities: {str(e)}")


@router.get("/{entity_id}")
async def get_entity(
    entity_id: uuid.UUID,
    db: Session = Depends(get_db)
):
    """
    Get entity by ID.

    Args:
        entity_id: Entity ID
        db: Database session

    Returns:
        dict: Entity information
    """
    try:
        # Get entity from database
        entity = db.query(Entity).filter(Entity.id == entity_id).first()

        if not entity:
            raise HTTPException(status_code=404, detail="Entity not found")

        # Format entity for response
        formatted_entity = {
            "id": str(entity.id),
            "document_id": str(entity.document_id),
            "chunk_id": str(entity.chunk_id) if entity.chunk_id else None,
            "text": entity.text,
            "entity_type": entity.entity_type,
            "normalized_id": entity.normalized_id,
            "start_pos": entity.start_pos,
            "end_pos": entity.end_pos,
            "confidence": entity.confidence,
            "created_at": entity.created_at.isoformat()
        }

        # Add metadata if available
        if entity.entity_metadata:
            # Remove embedding from metadata to reduce size
            metadata = entity.entity_metadata.copy()
            if "embedding" in metadata:
                del metadata["embedding"]
            formatted_entity["metadata"] = metadata

        # Get chunk text using Celery task if chunk_id is available
        if entity.chunk_id:
            task = celery_app.send_task(
                "workers.entity.get_chunk_text",
                kwargs={
                    "chunk_id": str(entity.chunk_id)
                }
            )

            # Wait for result with timeout
            result = task.get(timeout=10)

            if result and "text" in result:
                formatted_entity["chunk_text"] = result["text"]

        return formatted_entity

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting entity: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting entity: {str(e)}")


@router.get("/{entity_id}/relationships")
async def get_entity_relationships(
    entity_id: uuid.UUID,
    direction: str = Query("both", enum=["incoming", "outgoing", "both"]),
    relationship_type: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Get relationships for an entity.

    Args:
        entity_id: Entity ID
        direction: Relationship direction (incoming, outgoing, or both)
        relationship_type: Optional relationship type filter
        db: Database session

    Returns:
        dict: Entity relationships
    """
    try:
        # Get entity from database
        entity = db.query(Entity).filter(Entity.id == entity_id).first()

        if not entity:
            raise HTTPException(status_code=404, detail="Entity not found")

        # Use Celery task to get entity relationships
        task = celery_app.send_task(
            "workers.entity.get_entity_relationships",
            kwargs={
                "entity_id": str(entity_id),
                "direction": direction,
                "relationship_type": relationship_type
            }
        )

        # Wait for result with timeout
        result = task.get(timeout=30)

        # Add entity information to result
        result["entity_id"] = str(entity_id)
        result["entity_text"] = entity.text
        result["entity_type"] = entity.entity_type

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting entity relationships: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting entity relationships: {str(e)}")


@router.get("/search")
async def search_entities(
    query: str,
    entity_type: Optional[str] = None,
    limit: int = 10,
    threshold: float = 0.7
):
    """
    Search for entities using vector similarity.

    Args:
        query: Search query
        entity_type: Optional entity type filter
        limit: Maximum number of results
        threshold: Similarity threshold (0-1)
        db: Database session

    Returns:
        dict: Search results
    """
    try:
        # Use Celery task to search for similar entities
        task = celery_app.send_task(
            "workers.entity.find_similar_entities",
            kwargs={
                "query_text": query,
                "entity_type": entity_type,
                "limit": limit,
                "threshold": threshold
            }
        )

        # Wait for result with timeout
        result = task.get(timeout=30)

        # Extract entities from result
        similar_entities = result.get("entities", [])

        return {
            "query": query,
            "entity_type": entity_type,
            "results": similar_entities,
            "count": len(similar_entities)
        }

    except Exception as e:
        logger.error(f"Error searching entities: {e}")
        raise HTTPException(status_code=500, detail=f"Error searching entities: {str(e)}")


@router.get("/types")
async def get_entity_types(db: Session = Depends(get_db)):
    """
    Get all entity types in the system.

    Args:
        db: Database session

    Returns:
        dict: List of entity types
    """
    try:
        # Query distinct entity types
        entity_types = db.query(Entity.entity_type).distinct().all()

        # Extract types from result
        types = [t[0] for t in entity_types if t[0]]

        return {
            "entity_types": types,
            "count": len(types)
        }

    except Exception as e:
        logger.error(f"Error getting entity types: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting entity types: {str(e)}")


@router.get("/graph")
async def get_knowledge_graph(
    entity_id: Optional[uuid.UUID] = None,
    entity_type: Optional[str] = None,
    depth: int = 2,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """
    Get knowledge graph data for visualization.

    Args:
        entity_id: Optional entity ID to center graph on
        entity_type: Optional entity type filter
        depth: Graph traversal depth
        limit: Maximum number of nodes
        db: Database session

    Returns:
        dict: Knowledge graph data
    """
    try:
        # If entity_id is provided, get neighborhood from Neo4j via Celery task
        if entity_id:
            # Get entity from database
            entity = db.query(Entity).filter(Entity.id == entity_id).first()

            if not entity:
                raise HTTPException(status_code=404, detail="Entity not found")

            # Use Celery task to get entity neighborhood
            task = celery_app.send_task(
                "workers.entity.get_entity_neighborhood",
                kwargs={
                    "entity_id": str(entity_id),
                    "depth": depth,
                    "limit": limit
                }
            )

            # Wait for result with timeout
            result = task.get(timeout=30)

            return result

        # If entity_type is provided, use Celery task to get entities of that type
        elif entity_type:
            task = celery_app.send_task(
                "workers.entity.get_entities_by_type",
                kwargs={
                    "entity_type": entity_type,
                    "limit": limit
                }
            )

            # Wait for result with timeout
            result = task.get(timeout=30)

            return result

        # If no filters, use Celery task to get a sample of entities and relationships
        else:
            task = celery_app.send_task(
                "workers.entity.get_knowledge_graph_sample",
                kwargs={
                    "limit": limit
                }
            )

            # Wait for result with timeout
            result = task.get(timeout=30)

            return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting knowledge graph: {e}")
        raise HTTPException(status_code=500, detail=f"Error getting knowledge graph: {str(e)}")
