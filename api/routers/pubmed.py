# api/routers/pubmed.py
import logging
from typing import List, Dict, Any, Optional
from pydantic import BaseModel
from fastapi import APIRouter, HTTPException, Path
from services.pubmed_service import PubMedService
from workers.celery_app import celery_app
from transport.data_transport import DataTransport
import datetime

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter(tags=["pubmed"])

# Initialize PubMedService when needed
def get_pubmed_service():
    """Get PubMed service instance."""
    return PubMedService()

class PubmedSearchRequest(BaseModel):
    query: str
    start_year: Optional[int] = None
    end_year: Optional[int] = None
    max_results: Optional[int] = 0


@router.get("/search", response_model=List[Dict[str, Any]])
async def search_pubmed(query: str, max_results: int = 20, start_year: Optional[int] = None, end_year: Optional[int] = None):
    """
    Search PubMed for articles based on a query string.
    
    Parameters:
    - query: Search query for PubMed
    - max_results: Maximum number of results to return (default: 20)
    - start_year: Filter articles published from this year onwards (optional)
    - end_year: Filter articles published until this year (optional)
    """
    try:
        # Get PubMed service
        pubmed_service = get_pubmed_service()

        # Default year range if not provided
        current_year = datetime.datetime.now().year
        start_year = start_year or current_year - 3  # Default to 3 years ago
        end_year = end_year or current_year  # Default to current year
        
        # Validate year range
        if start_year > end_year:
            logger.warning(f"Invalid year range: start_year {start_year} > end_year {end_year}. Swapping values.")
            start_year, end_year = end_year, start_year
            
        logger.info(f"Received PubMed search query: '{query}' with max_results: {max_results}, year range: {start_year}-{end_year}")

        # Directly call the service for searching
        pmids = pubmed_service.search(query=query, start_year=start_year, end_year=end_year)

        # Limit results based on max_results
        pmids = pmids[:max_results] if max_results > 0 else pmids

        # Get details for each PMID
        results = []
        for pmid in pmids:
            article_details = pubmed_service.get_article_details(pmid)
            if article_details:
                results.append(article_details)

        logger.info(f"Found {len(results)} articles for query '{query}'")
        return results
    except Exception as e:
        logger.error(f"Error during PubMed search for query '{query}': {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to search PubMed articles.")

@router.post("/ingest/{pmid}", response_model=Dict[str, str])
async def trigger_pubmed_ingestion(
    pmid: str = Path(..., title="PubMed ID", description="The unique identifier for the PubMed article")
):
    """
    Trigger the asynchronous ingestion of a specific PubMed article by its PMID.
    """
    try:
        logger.info(f"Received request to ingest PubMed article with PMID: {pmid}")

        # Check if document already exists
        with DataTransport() as transport:
            existing_doc = transport.db_client.get_document_by_source('pubmed', pmid)
            if existing_doc:
                logger.info(f"Document for PMID {pmid} already exists (ID: {existing_doc.id}).")
                return {"status": "already_exists", "document_id": str(existing_doc.id)}

        # Send task to Celery to fetch and ingest the article
        task = celery_app.send_task(
            'workers.pubmed.ingest_pubmed_article', # Task name defined in the plan
            args=[pmid],
            queue="document_processing_queue" # Or your relevant queue
        )
        logger.info(f"Sent task {task.id} to ingest PubMed article {pmid}")

        return {"status": "queued", "message": f"Ingestion task for PMID {pmid} queued.", "task_id": task.id}
    except Exception as e:
        logger.error(f"Error triggering ingestion for PMID {pmid}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to trigger ingestion for PMID {pmid}.")
    

@router.post("/insert_articles_with_query", response_model=dict)
async def insert_articles_with_query(
    query: PubmedSearchRequest
):
    try:
        logger.info(f"Received request to insert articles with query: {query.query}")
        
        # Get PubMed service to perform the search directly
        pubmed_service = get_pubmed_service()
        
        # Default year range if not provided and validate years
        current_year = datetime.datetime.now().year
        start_year = query.start_year or current_year - 3  # Default to 3 years ago
        end_year = query.end_year or current_year  # Default to current year
        
        # Validate year range
        if start_year > end_year:
            logger.warning(f"Invalid year range: start_year {start_year} > end_year {end_year}. Swapping values.")
            start_year, end_year = end_year, start_year
        
        # Search for PMIDs with validated year range
        pmids = pubmed_service.search(
            query=query.query, 
            start_year=start_year, 
            end_year=end_year
        )
        
        # Apply max_results limit after search is complete
        if query.max_results > 0:
            pmids = pmids[:query.max_results]
        
        logger.info(f"Found {len(pmids)} articles to insert")
        
        # Queue individual ingestion tasks for each PMID
        BATCH_SIZE = 10  # Process in smaller batches to avoid overwhelming the workers
        task_ids = []
        
        for i in range(0, len(pmids), BATCH_SIZE):
            batch_pmids = pmids[i:i + BATCH_SIZE]
            for pmid in batch_pmids:
                # Use our workers.pubmed.ingest_pubmed_article task for each PMID
                task = celery_app.send_task(
                    'workers.pubmed.ingest_pubmed_article',
                    args=[pmid],
                    queue="document_processing_queue"
                )
                task_ids.append(task.id)
        
        return {
            "status": "success", 
            "message": f"Queued ingestion of {len(pmids)} articles",
            "pmids_count": len(pmids),
            "task_ids": task_ids
        }
        
    except Exception as e:
        logger.error(f"Error inserting articles with query '{query.query}': {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to insert articles")

