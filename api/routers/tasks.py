"""
Tasks API router for longevity platform.
"""
import uuid
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, UUID4
from sqlalchemy.orm import Session

from common.database import get_db, ProcessingTask
from api.transport.data_transport_api import DataTransportAPI

router = APIRouter()


class TaskResponse(BaseModel):
    """Task response model."""
    id: str
    document_id: str
    task_type: str
    celery_task_id: Optional[str] = None
    status: str
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None


class TaskListResponse(BaseModel):
    """Task list response model."""
    tasks: List[TaskResponse]
    count: int


@router.get("/list", response_model=TaskListResponse)
async def list_tasks(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    task_type: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    List all tasks.

    Args:
        skip: Number of tasks to skip
        limit: Maximum number of tasks to return
        status: Filter by status
        task_type: Filter by task type
        db: Database session

    Returns:
        TaskListResponse: List of tasks
    """
    try:
        # Build query
        query = db.query(ProcessingTask)

        if status:
            query = query.filter(ProcessingTask.status == status)

        if task_type:
            query = query.filter(ProcessingTask.task_type == task_type)

        # Get count
        total_count = query.count()

        # Get tasks
        tasks = query.order_by(ProcessingTask.created_at.desc()).offset(skip).limit(limit).all()

        # Convert to response model
        task_responses = [task.to_dict() for task in tasks]

        return {
            "tasks": task_responses,
            "count": total_count
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error listing tasks: {str(e)}"
        )


@router.get("/{task_id}", response_model=TaskResponse)
async def get_task(
    task_id: UUID4,
    db: Session = Depends(get_db)
):
    """
    Get task by ID.

    Args:
        task_id: Task ID
        db: Database session

    Returns:
        TaskResponse: Task information
    """
    try:
        with DataTransportAPI(db) as transport:
            task = transport.db_client.get_processing_task(task_id)

            if not task:
                raise HTTPException(
                    status_code=404,
                    detail=f"Task with ID {task_id} not found"
                )

            return task.to_dict()

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error getting task: {str(e)}"
        )


@router.get("/by-document/{document_id}", response_model=TaskListResponse)
async def get_tasks_by_document(
    document_id: UUID4,
    db: Session = Depends(get_db)
):
    """
    Get tasks by document ID.

    Args:
        document_id: Document ID
        db: Database session

    Returns:
        TaskListResponse: List of tasks for the document
    """
    try:
        # Query tasks by document ID
        tasks = db.query(ProcessingTask).filter(
            ProcessingTask.document_id == document_id
        ).order_by(ProcessingTask.created_at.desc()).all()

        # Convert to response model
        task_responses = [task.to_dict() for task in tasks]

        return {
            "tasks": task_responses,
            "count": len(tasks)
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error getting tasks for document: {str(e)}"
        )


@router.get("/by-celery-id/{celery_task_id}", response_model=TaskListResponse)
async def get_tasks_by_celery_id(
    celery_task_id: str,
    db: Session = Depends(get_db)
):
    """
    Get tasks by Celery task ID.

    Args:
        celery_task_id: Celery task ID
        db: Database session

    Returns:
        TaskListResponse: List of tasks with the Celery task ID
    """
    try:
        # Query tasks by Celery task ID
        tasks = db.query(ProcessingTask).filter(
            ProcessingTask.celery_task_id == celery_task_id
        ).order_by(ProcessingTask.created_at.desc()).all()

        # Convert to response model
        task_responses = [task.to_dict() for task in tasks]

        return {
            "tasks": task_responses,
            "count": len(tasks)
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error getting tasks for Celery task ID: {str(e)}"
        )
