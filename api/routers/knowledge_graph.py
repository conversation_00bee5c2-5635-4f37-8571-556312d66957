"""
Knowledge Graph API router for longevity platform.
"""
import uuid
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, UUID4
from sqlalchemy.orm import Session

from common.database import get_db
from transport.data_transport import DataTransport
from workers.celery_app import celery_app


router = APIRouter()


class KnowledgeGraphBuildRequest(BaseModel):
    """Knowledge graph build request model."""
    document_id: UUID4


class KnowledgeGraphBuildResponse(BaseModel):
    """Knowledge graph build response model."""
    task_id: str
    status: str
    document_id: str
    message: str


class KnowledgeGraphQueryRequest(BaseModel):
    """Knowledge graph query request model."""
    query: str
    limit: Optional[int] = 10


class EntityNeighborhoodRequest(BaseModel):
    """Entity neighborhood request model."""
    entity_id: str
    depth: Optional[int] = 1
    limit: Optional[int] = 10


@router.post("/build", response_model=KnowledgeGraphBuildResponse)
async def build_knowledge_graph(
    request: KnowledgeGraphBuildRequest,
    db: Session = Depends(get_db)
):
    """
    Build a knowledge graph from a document.

    Args:
        request: Knowledge graph build request
        db: Database session

    Returns:
        KnowledgeGraphBuildResponse: Knowledge graph build response
    """
    try:
        with DataTransport(db) as transport:
            # Check if document exists
            document = transport.db_client.get_document(request.document_id)
            if not document:
                raise HTTPException(
                    status_code=404,
                    detail=f"Document with ID {request.document_id} not found"
                )

            # Create processing task
            task_result = transport.create_processing_task(
                document_id=request.document_id,
                task_type="knowledge_graph_building"
            )

            # Queue knowledge graph building in Celery
            celery_task = celery_app.send_task(
                "workers.knowledge_graph.build_knowledge_graph",
                args=[str(request.document_id), task_result["task_id"]]
            )

            return {
                "task_id": task_result["task_id"],
                "status": "queued",
                "document_id": str(request.document_id),
                "message": f"Knowledge graph building queued with Celery task ID: {celery_task.id}"
            }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error building knowledge graph: {str(e)}"
        )


@router.get("/status/{task_id}", response_model=Dict[str, Any])
async def get_knowledge_graph_status(
    task_id: UUID4,
    db: Session = Depends(get_db)
):
    """
    Get knowledge graph building status.

    Args:
        task_id: Task ID
        db: Database session

    Returns:
        Dict[str, Any]: Task status
    """
    try:
        with DataTransport(db) as transport:
            # Get task status
            task = transport.db_client.get_processing_task(task_id)
            if not task:
                raise HTTPException(
                    status_code=404,
                    detail=f"Task with ID {task_id} not found"
                )

            return task.to_dict()

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error getting knowledge graph status: {str(e)}"
        )


@router.post("/query", response_model=Dict[str, Any])
async def query_knowledge_graph(
    request: KnowledgeGraphQueryRequest
):
    """
    Query the knowledge graph.

    Args:
        request: Knowledge graph query request

    Returns:
        Dict[str, Any]: Query results
    """
    try:
        # Queue knowledge graph query in Celery
        task = celery_app.send_task(
            "workers.knowledge_graph.query_knowledge_graph",
            kwargs={
                "query_text": request.query,
                "limit": request.limit
            }
        )

        # Wait for result with timeout
        result = task.get(timeout=30)

        return result

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error querying knowledge graph: {str(e)}"
        )


@router.post("/entity/neighborhood", response_model=Dict[str, Any])
async def get_entity_neighborhood(
    request: EntityNeighborhoodRequest
):
    """
    Get the neighborhood of an entity in the knowledge graph.

    Args:
        request: Entity neighborhood request

    Returns:
        Dict[str, Any]: Entity neighborhood
    """
    try:
        # Queue entity neighborhood query in Celery
        task = celery_app.send_task(
            "workers.knowledge_graph.get_entity_neighborhood",
            kwargs={
                "entity_id": request.entity_id,
                "depth": request.depth,
                "limit": request.limit
            }
        )

        # Wait for result with timeout
        result = task.get(timeout=30)

        return result

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error getting entity neighborhood: {str(e)}"
        )


@router.get("/entities/{document_id}", response_model=Dict[str, Any])
async def get_document_entities(
    document_id: UUID4,
    db: Session = Depends(get_db)
):
    """
    Get entities extracted from a document.

    Args:
        document_id: Document ID
        db: Database session

    Returns:
        Dict[str, Any]: Document entities
    """
    try:
        with DataTransport(db) as transport:
            # Get entities from database
            entities = transport.get_document_entities(document_id)

            return {
                "document_id": str(document_id),
                "entities": entities,
                "count": len(entities)
            }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error getting document entities: {str(e)}"
        )


@router.get("/relationships/{document_id}", response_model=Dict[str, Any])
async def get_document_relationships(
    document_id: UUID4,
    db: Session = Depends(get_db)
):
    """
    Get relationships extracted from a document.

    Args:
        document_id: Document ID
        db: Database session

    Returns:
        Dict[str, Any]: Document relationships
    """
    try:
        with DataTransport(db) as transport:
            # Get relationships from database
            relationships = transport.get_document_relationships(document_id)

            return {
                "document_id": str(document_id),
                "relationships": relationships,
                "count": len(relationships)
            }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error getting document relationships: {str(e)}"
        )


@router.get("/entity/{entity_id}/chunks", response_model=Dict[str, Any])
async def get_chunks_mentioning_entity(
    entity_id: str,
    limit: int = 10,
    db: Session = Depends(get_db)
):
    """
    Get all chunks that mention a specific entity.

    Args:
        entity_id: Entity ID
        limit: Maximum number of results
        db: Database session

    Returns:
        Dict[str, Any]: Chunks mentioning the entity
    """
    try:
        with DataTransport(db) as transport:
            # Get chunks mentioning the entity
            chunks = transport.get_chunks_mentioning_entity(entity_id, limit)

            return {
                "entity_id": entity_id,
                "chunks": chunks,
                "count": len(chunks)
            }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error getting chunks mentioning entity: {str(e)}"
        )


class RerunKGJobRequest(BaseModel):
    """Request model for rerunning knowledge graph jobs."""
    use_batch_api: bool = True
    async_processing: bool = True


@router.get("/failed-jobs", response_model=Dict[str, Any])
async def list_failed_knowledge_graph_jobs(
    limit: int = 50,
    db: Session = Depends(get_db)
):
    """
    List failed knowledge graph building jobs.
    
    Returns a list of knowledge graph building tasks that have failed,
    including their document IDs and error messages.
    
    Args:
        limit: Maximum number of failed jobs to return
        db: Database session
        
    Returns:
        Dict[str, Any]: List of failed knowledge graph building jobs
    """
    try:
        from sqlalchemy import text
        with DataTransport(db) as transport:
            # Get all failed knowledge graph building tasks
            query = text("""
                SELECT id, document_id, created_at, updated_at, status, error
                FROM processing_tasks
                WHERE task_type = 'knowledge_graph_building'
                AND status LIKE '%failed'
                ORDER BY updated_at DESC
                LIMIT :limit
            """)
            
            failed_tasks = db.execute(query, {"limit": limit}).fetchall()
            
            # Format the results
            result = []
            for task in failed_tasks:
                document = None
                if task.document_id:
                    document = transport.db_client.get_document(task.document_id)
                
                result.append({
                    "task_id": str(task.id),
                    "document_id": str(task.document_id) if task.document_id else None,
                    "document_title": document.title if document else None,
                    "created_at": task.created_at.isoformat() if task.created_at else None,
                    "updated_at": task.updated_at.isoformat() if task.updated_at else None,
                    "error": task.error
                })
                
            return {
                "failed_jobs": result,
                "count": len(result)
            }
            
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error listing failed knowledge graph jobs: {str(e)}"
        )


@router.post("/rerun-job/{task_id}", response_model=KnowledgeGraphBuildResponse)
async def rerun_failed_knowledge_graph_job(
    task_id: UUID4,
    request: RerunKGJobRequest = None,
    db: Session = Depends(get_db)
):
    """
    Rerun a failed knowledge graph building job.
    
    Takes a task ID of a previously failed knowledge graph building job
    and resubmits it for processing.
    
    Args:
        task_id: ID of the failed task to rerun
        request: Optional parameters for rerunning the job
        db: Database session
        
    Returns:
        KnowledgeGraphBuildResponse: Knowledge graph build response
    """
    try:
        with DataTransport(db) as transport:
            # Get the original failed task
            original_task = transport.db_client.get_processing_task(task_id)
            if not original_task:
                raise HTTPException(
                    status_code=404,
                    detail=f"Task with ID {task_id} not found"
                )
                
            # Verify it's a knowledge graph building task
            if original_task.task_type != "knowledge_graph_building":
                raise HTTPException(
                    status_code=400,
                    detail=f"Task {task_id} is not a knowledge graph building task"
                )
                
            # Get document ID from original task
            document_id = original_task.document_id
            if not document_id:
                raise HTTPException(
                    status_code=400,
                    detail=f"No document ID found for task {task_id}"
                )
                
            # Verify document still exists
            document = transport.db_client.get_document(document_id)
            if not document:
                raise HTTPException(
                    status_code=404,
                    detail=f"Document with ID {document_id} not found"
                )
                
            # Create new processing task
            new_task = transport.create_processing_task(
                document_id=document_id,
                task_type="knowledge_graph_building",
                metadata={
                    "original_task_id": str(task_id),
                    "is_retry": True
                }
            )
            
            # Set parameters for rerunning
            use_batch_api = True
            async_processing = True
            
            if request:
                use_batch_api = request.use_batch_api
                async_processing = request.async_processing
                
            # Queue knowledge graph building in Celery
            celery_task = celery_app.send_task(
                "workers.knowledge_graph.build_knowledge_graph",
                args=[str(document_id)],
                kwargs={
                    "processing_task_id": new_task["task_id"],
                    "use_batch_api": use_batch_api,
                    "async_processing": async_processing
                }
            )
            
            return {
                "task_id": new_task["task_id"],
                "status": "queued",
                "document_id": str(document_id),
                "message": f"Knowledge graph building requeued with Celery task ID: {celery_task.id}"
            }
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error rerunning knowledge graph job: {str(e)}"
        )
