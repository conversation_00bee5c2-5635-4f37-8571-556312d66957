"""
Entity Orchestrator API router for longevity platform.
"""
import uuid
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, UUID4
from sqlalchemy.orm import Session

from common.database import get_db
from transport.data_transport import DataTransport
from workers.celery_app import celery_app


router = APIRouter()


class EntityProcessRequest(BaseModel):
    """Entity process request model."""
    document_id: UUID4


class EntityProcessResponse(BaseModel):
    """Entity process response model."""
    task_id: str
    status: str
    document_id: str
    message: str


class EntitySearchRequest(BaseModel):
    """Entity search request model."""
    query: str
    entity_type: Optional[str] = None
    limit: int = 10


@router.post("/process", response_model=EntityProcessResponse)
async def process_document_entities(
    request: EntityProcessRequest,
    db: Session = Depends(get_db)
):
    """
    Process a document to extract entities, normalize them, and build relationships.

    Args:
        request: Entity process request
        db: Database session

    Returns:
        EntityProcessResponse: Entity process response
    """
    try:
        with DataTransport(db) as transport:
            # Check if document exists
            document = transport.db_client.get_document(request.document_id)
            if not document:
                raise HTTPException(
                    status_code=404,
                    detail=f"Document with ID {request.document_id} not found"
                )

            # Create processing task
            task_result = transport.create_processing_task(
                document_id=request.document_id,
                task_type="entity_orchestration"
            )

            # Queue entity orchestration in Celery
            celery_task = celery_app.send_task(
                "workers.entity_orchestrator.process_document",
                args=[str(request.document_id), task_result["task_id"]]
            )

            return {
                "task_id": task_result["task_id"],
                "status": "queued",
                "document_id": str(request.document_id),
                "message": f"Entity orchestration queued with Celery task ID: {celery_task.id}"
            }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error processing document entities: {str(e)}"
        )


@router.get("/status/{task_id}", response_model=Dict[str, Any])
async def get_entity_process_status(
    task_id: UUID4,
    db: Session = Depends(get_db)
):
    """
    Get entity process status.

    Args:
        task_id: Task ID
        db: Database session

    Returns:
        Dict[str, Any]: Task status
    """
    try:
        with DataTransport(db) as transport:
            # Get task status
            task = transport.db_client.get_processing_task(task_id)
            if not task:
                raise HTTPException(
                    status_code=404,
                    detail=f"Task with ID {task_id} not found"
                )

            return task.to_dict()

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error getting entity process status: {str(e)}"
        )


@router.post("/search", response_model=Dict[str, Any])
async def search_entities(
    request: EntitySearchRequest
):
    """
    Search for entities similar to a query text.

    Args:
        request: Entity search request

    Returns:
        Dict[str, Any]: Search results
    """
    try:
        # Queue entity search in Celery
        task = celery_app.send_task(
            "workers.entity_orchestrator.find_similar_entities",
            kwargs={
                "query_text": request.query,
                "entity_type": request.entity_type,
                "limit": request.limit
            }
        )

        # Wait for result with timeout
        result = task.get(timeout=30)

        return result

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error searching entities: {str(e)}"
        )
