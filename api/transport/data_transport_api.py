"""
Data transport layer for API operations.
This is a simplified version of the DataTransport class that doesn't depend on Neo4j.
"""
import os
import uuid
import tempfile
from typing import Optional, Dict, Any, List, Union, BinaryIO, Tuple

from sqlalchemy.orm import Session

from common.database import get_db
from transport.storage_client import StorageClient
from transport.database_client import DatabaseClient


class DataTransportAPI:
    """
    Data transport layer for API operations.
    This is a simplified version that doesn't depend on Neo4j.
    """

    def __init__(self, db_session: Optional[Session] = None):
        """
        Initialize data transport with database session.

        Args:
            db_session: SQLAlchemy database session (optional)
        """
        self.db_session = db_session
        self.storage_client = StorageClient()

    def __enter__(self):
        """Context manager enter method."""
        if self.db_session is None:
            self.db_session = next(get_db())

        self.db_client = DatabaseClient(self.db_session)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit method."""
        if self.db_session:
            self.db_session.close()

    def store_document(self,
                      file_obj: BinaryIO,
                      filename: str,
                      content_type: str,
                      source_path: str = "upload",
                      metadata: Optional[Dict[str, Any]] = None,
                      title: Optional[str] = None,
                      check_duplicates: bool = True) -> Dict[str, Any]:
        """
        Store a document in the system.

        Args:
            file_obj: File object
            filename: Original filename
            content_type: MIME type of the document
            source_path: Source path or URL
            metadata: Additional metadata
            title: Optional document title (if available)
            check_duplicates: Whether to check for duplicate documents

        Returns:
            Dict[str, Any]: Document information
        """
        # Get file size
        file_obj.seek(0, os.SEEK_END)
        file_size = file_obj.tell()
        file_obj.seek(0)

        # Create initial document record
        document = self.db_client.create_document(
            filename=filename,
            source_path=source_path,
            content_type=content_type,
            file_size=file_size,
            metadata=metadata,
            title=title
        )

        # Note: We're not implementing duplicate checking in the API transport layer
        # since it's primarily used for direct API calls and not for the worker flow.
        # The worker flow uses the main DataTransport class which has duplicate checking.

        # Upload file to storage
        storage_path = self.storage_client.upload_file(
            file_obj=file_obj,
            content_type=content_type,
            filename=filename
        )

        # Update document with storage path
        document = self.db_client.update_document_status(
            document_id=document.id,
            status="uploaded",
            storage_path=storage_path
        )

        return {
            "document_id": str(document.id),
            "filename": document.filename,
            "content_type": document.content_type,
            "storage_path": document.storage_path,
            "status": document.status
        }

    def get_document_content(self, document_id: Union[str, uuid.UUID]) -> Tuple[bytes, Dict[str, Any]]:
        """
        Get document content and metadata.

        Args:
            document_id: Document ID

        Returns:
            Tuple[bytes, Dict[str, Any]]: Document content and metadata
        """
        # Get document record
        document = self.db_client.get_document(document_id)
        if not document:
            raise ValueError(f"Document with ID {document_id} not found")

        if not document.storage_path:
            raise ValueError(f"Document with ID {document_id} has no storage path")

        # Download file from storage
        content, storage_metadata = self.storage_client.download_file(document.storage_path)

        # Combine database and storage metadata
        metadata = {
            "document_id": str(document.id),
            "filename": document.filename,
            "content_type": document.content_type,
            "file_size": document.file_size,
            "status": document.status,
            "created_at": document.created_at.isoformat() if document.created_at else None,
            "updated_at": document.updated_at.isoformat() if document.updated_at else None,
            "storage_metadata": storage_metadata,
            "metadata": document.metadata or {}
        }

        return content, metadata

    def get_document_download_url(self, document_id: Union[str, uuid.UUID], expires: int = 3600) -> str:
        """
        Get presigned URL for downloading a document.

        Args:
            document_id: Document ID
            expires: Expiration time in seconds

        Returns:
            str: Presigned URL
        """
        # Get document record
        document = self.db_client.get_document(document_id)
        if not document:
            raise ValueError(f"Document with ID {document_id} not found")

        if not document.storage_path:
            raise ValueError(f"Document with ID {document_id} has no storage path")

        # Generate presigned URL
        return self.storage_client.get_presigned_url(document.storage_path, expires)

    def create_processing_task(self, document_id: Union[str, uuid.UUID], task_type: str, metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Create a processing task.

        Args:
            document_id: Document ID
            task_type: Type of processing task
            metadata: Additional metadata

        Returns:
            Dict[str, Any]: Task information
        """
        task = self.db_client.create_processing_task(
            document_id=document_id,
            task_type=task_type,
            metadata=metadata
        )

        return {
            "task_id": str(task.id),
            "document_id": str(task.document_id) if task.document_id else None,
            "task_type": task.task_type,
            "status": task.status
        }

    def update_task_status(self, task_id: Union[str, uuid.UUID], status: str, result: Optional[Dict[str, Any]] = None, error: Optional[str] = None) -> Dict[str, Any]:
        """
        Update processing task status.

        Args:
            task_id: Task ID
            status: New status
            result: Optional result data
            error: Optional error message

        Returns:
            Dict[str, Any]: Updated task information
        """
        task = self.db_client.update_task_status(
            task_id=task_id,
            status=status,
            result=result,
            error=error
        )

        return {
            "task_id": str(task.id),
            "document_id": str(task.document_id) if task.document_id else None,
            "task_type": task.task_type,
            "status": task.status
        }

    def get_document_chunks(self, document_id: Union[str, uuid.UUID]) -> List[Dict[str, Any]]:
        """
        Get all chunks for a document.

        Args:
            document_id: Document ID

        Returns:
            List[Dict[str, Any]]: List of document chunks
        """
        chunks = self.db_client.get_document_chunks(document_id)
        return [chunk.to_dict() for chunk in chunks]

    def get_chunk(self, chunk_id: Union[str, uuid.UUID]) -> Optional[Dict[str, Any]]:
        """
        Get a specific chunk by ID.

        Args:
            chunk_id: Chunk ID

        Returns:
            Optional[Dict[str, Any]]: Chunk data if found, None otherwise
        """
        chunk = self.db_client.get_chunk(chunk_id)
        return chunk.to_dict() if chunk else None

    def get_processing_task(self, task_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
        """
        Get processing task by ID.

        Args:
            task_id: Task ID

        Returns:
            Dict[str, Any]: Task information
        """
        task = self.db_client.get_processing_task(task_id)
        if not task:
            raise ValueError(f"Task with ID {task_id} not found")

        return task.to_dict()

    def get_processing_tasks_by_document(self, document_id: Union[str, uuid.UUID]) -> List[Dict[str, Any]]:
        """
        Get all processing tasks for a document.

        Args:
            document_id: Document ID

        Returns:
            List[Dict[str, Any]]: List of task information
        """
        tasks = self.db_client.get_processing_tasks_by_document(document_id)
        return [task.to_dict() for task in tasks]

    def get_processing_tasks_by_celery_id(self, celery_task_id: str) -> List[Dict[str, Any]]:
        """
        Get all processing tasks with a specific Celery task ID.

        Args:
            celery_task_id: Celery task ID

        Returns:
            List[Dict[str, Any]]: List of task information
        """
        tasks = self.db_client.get_processing_tasks_by_celery_id(celery_task_id)
        return [task.to_dict() for task in tasks]
