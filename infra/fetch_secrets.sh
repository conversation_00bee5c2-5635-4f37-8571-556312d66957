#!/bin/bash
# infra/fetch_secrets.sh
# Fetch secrets from Google Secret Manager and merge with .env.vm to create .env

set -e

ENV_VM_FILE=".env.vm"
ENV_FILE=".env"

# Check for gcloud CLI
if ! command -v gcloud &> /dev/null; then
  echo "gcloud CLI not found. Please install and authenticate gcloud."
  exit 1
fi

# Fetch secrets from Google Secret Manager
POSTGRES_PASSWORD=$(gcloud secrets versions access latest --secret="longevity-postgres-password")
NEO4J_AUTH=$(gcloud secrets versions access latest --secret="longevity-neo4j-auth")
REDIS_PASSWORD=$(gcloud secrets versions access latest --secret="longevity-redis-password")

# Copy non-secret variables
cp "$ENV_VM_FILE" "$ENV_FILE"

# APP_ENV controls the application environment configuration.
# It defaults to "docker" for containerized setups.
# For local development, set APP_ENV="local" in your shell or a local .env file
# (ensure this local .env is not committed to git if it contains sensitive info or overrides).
# The Settings class in common/config.py uses APP_ENV to determine
# connection parameters for services like Postgres, Redis, Neo4j, and Minio.
if ! grep -q "^APP_ENV=" "$ENV_FILE"; then
  echo "APP_ENV=docker" >> "$ENV_FILE"
fi

# Append secrets
echo "POSTGRES_PASSWORD=$POSTGRES_PASSWORD" >> "$ENV_FILE"
echo "NEO4J_AUTH=$NEO4J_AUTH" >> "$ENV_FILE"
echo "REDIS_PASSWORD=$REDIS_PASSWORD" >> "$ENV_FILE"

# Optionally export for current shell
export $(grep -v '^#' "$ENV_FILE" | xargs)

echo ".env file created/updated with secrets from Google Secret Manager."