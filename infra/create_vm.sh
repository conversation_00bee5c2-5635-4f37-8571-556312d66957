#!/bin/bash
# infra/create_vm.sh
# Usage: bash infra/create_vm.sh <INSTANCE_NAME> <ZONE> <MACHINE_TYPE>
# Example: bash infra/create_vm.sh longevityco-dev us-central1-a e2-standard-4

set -e

INSTANCE_NAME=${1:-longevityco-dev}
ZONE=${2:-europe-west4-a}
MACHINE_TYPE=${3:-e2-standard-8}

# Create the VM

gcloud compute instances create $INSTANCE_NAME \
  --zone=$ZONE \
  --machine-type=$MACHINE_TYPE \
  --image-family=ubuntu-2204-lts \
  --image-project=ubuntu-os-cloud \
  --boot-disk-size=100GB \
  --tags=http-server,https-server,longevityco-api \
  --metadata=startup-script='#!/bin/bash
apt-get update
apt-get install -y docker.io git
systemctl start docker
systemctl enable docker
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose
usermod -aG docker $USER
'

echo "VM $INSTANCE_NAME created. You can SSH with:"
echo "gcloud compute ssh $INSTANCE_NAME --zone $ZONE"