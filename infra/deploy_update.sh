#!/bin/bash
# infra/deploy_update.sh
# Usage:
#   bash deploy_update.sh local   # For local development
#   bash deploy_update.sh vm      # For VM/production deployment

set -e

DEPLOY_ENV=${1:-local}

# Start the ssh-agent and add your private key if needed
if [ -f "$HOME/.ssh/id_ed25519" ]; then
  eval "$(ssh-agent -s)"
  ssh-add $HOME/.ssh/id_ed25519
fi

REPO_URL="**************:aguzererler/LongevityCo.git"  # <-- UPDATE THIS
PROJECT_DIR="$HOME/LongevityCo"

# Clone repo if it doesn't exist
if [ ! -d "$PROJECT_DIR" ]; then
  git clone $REPO_URL $PROJECT_DIR
fi

cd $PROJECT_DIR

git pull

if [ "$DEPLOY_ENV" = "vm" ]; then
  # For VM/production: use docker-compose.vm.yml and .env (generated by fetch_secrets.sh)
  if [ ! -f .env ]; then
    echo "[INFO] .env not found. Run fetch_secrets.sh first."
    exit 1
  fi
  docker-compose -f docker-compose.vm.yml down || true
  docker-compose -f docker-compose.vm.yml build
  docker-compose -f docker-compose.vm.yml up -d
else
  # For local: use docker-compose.yml and .env (local dev)
  if [ ! -f .env ]; then
    echo "[INFO] .env not found. Please create a local .env file."
    exit 1
  fi
  docker-compose down || true
  docker-compose build
  docker-compose up -d
fi

echo "Deployment complete. API should be available at the VM's external IP (for vm) or localhost (for local)."
