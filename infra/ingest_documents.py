import requests

API_URL = "http://localhost:8000/ingest"  # Update if needed

def ingest_pubmed():
    payload = {
        "source": "pubmed",
        "query": "longevity diet",
        "max_results": 2
    }
    r = requests.post(API_URL, json=payload)
    print("PubMed ingest:", r.status_code, r.text)

def ingest_file(filename):
    with open(filename, 'rb') as f:
        files = {'file': f}
        r = requests.post(API_URL, files=files)
        print(f"Ingest {filename}:", r.status_code, r.text)

if __name__ == "__main__":
    ingest_pubmed()
    for fname in [
        "tests/data/sample_txt.txt",
        "tests/data/sample_epub.epub",
        "tests/data/sample_pdf.pdf",
        "tests/data/sample_url.txt"
    ]:
        ingest_file(fname)
