# LongevityCo Infrastructure Deployment & Operations

This guide describes how to set up, deploy, and operate the LongevityCo system on Google Cloud Compute Engine using Docker Compose, Google Secret Manager, and GitHub.

---

## 1. Prerequisites
- Google Cloud account with billing enabled
- gcloud CLI installed and authenticated
- Docker & Docker Compose knowledge
- GitHub repository access
- Secrets (DB passwords, etc.) stored in Google Secret Manager

---

## 2. VM Creation

Create a Compute Engine VM with Docker and Docker Compose pre-installed:

```sh
cd infra
bash create_vm.sh <INSTANCE_NAME> <ZONE> <MACHINE_TYPE>
# Example:
bash create_vm.sh longevityco-dev us-central1-a e2-standard-4
```

---

## 3. Open Firewall for API Access

Open the API port (default: 8000) to external traffic:

```sh
bash open_api_firewall.sh 8000
```

---

## 4. Prepare Environment Variables & Secrets

- Edit `.env.vm` in the project root to set non-secret environment variables (see `config.py` for reference).
- Store secrets (e.g., DB passwords, Redis password, Neo4j credentials) in Google Secret Manager:

```sh
# Create and store PostgreSQL password
gcloud secrets create longevity-postgres-password --replication-policy="automatic"
echo "your_postgres_password" | gcloud secrets versions add longevity-postgres-password --data-file=-

# Create and store Redis password
gcloud secrets create longevity-redis-password --replication-policy="automatic"
echo "your_redis_password" | gcloud secrets versions add longevity-redis-password --data-file=-

# Create and store Neo4j credentials
gcloud secrets create longevity-neo4j-auth --replication-policy="automatic"
echo "neo4j/your_neo4j_password" | gcloud secrets versions add longevity-neo4j-auth --data-file=-
```

---

## 5. Deploy & Update Code on the VM

SSH into your VM:

```sh
gcloud compute ssh <INSTANCE_NAME> --zone <ZONE>
```

On the VM, run:

```sh
cd ~/LongevityCo/infra
bash fetch_secrets.sh  # Fetch secrets and generate .env
bash deploy_update.sh  # Clone/pull repo and start services
```

- The `fetch_secrets.sh` script merges secrets from Google Secret Manager with non-secret variables from `.env.vm` to create `.env` for Docker Compose and `config.py`.
- The `deploy_update.sh` script will clone the repo (if needed), pull the latest code, and restart all Docker Compose services using `docker-compose.vmdeploy.yaml`.

---

## 6. Start the Application

From the project root (~/LongevityCo), start the stack:

```sh
docker-compose -f docker-compose.vmdeploy.yaml up -d --build
```

This will start all services (Postgres, Redis, Neo4j, API, Worker) using the environment variables from `.env`.

---

## 7. System Test Pipeline (Optional)

To run the full system test pipeline:

```sh
bash system_test.sh
```

This will:
- Start all services
- Purge databases
- Ingest test documents
- Wait for processing
- Verify ingestion and processing results

---

## 8. Maintenance
- To update code, repeat steps 5 and 6 as needed.
- To change secrets, update them in Google Secret Manager and rerun `fetch_secrets.sh`.
- To open additional ports, use `open_api_firewall.sh <PORT>`.

---

## 9. Security & Production Notes
- Do **not** commit secrets to version control.
- Restrict firewall rules to trusted IPs in production.
- Consider using HTTPS and a reverse proxy for the API.
- Regularly back up persistent volumes (Postgres, Redis, Neo4j).
- Monitor resource usage and set resource limits as needed.

---

## 10. File Overview
- `create_vm.sh` — Provision a GCP VM with Docker
- `open_api_firewall.sh` — Open API port in GCP firewall
- `fetch_secrets.sh` — Fetch secrets from Google Secret Manager and generate `.env`
- `deploy_update.sh` — Pull latest code from GitHub and restart services
- `system_test.sh` — Run end-to-end system test pipeline
- `.env.vm` — Template for non-secret environment variables
- `docker-compose.vmdeploy.yaml` — Compose file for VM deployment

---

For any issues, see the main project readme or contact the maintainers.
