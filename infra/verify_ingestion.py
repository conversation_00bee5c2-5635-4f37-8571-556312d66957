import os
import sys

# Add the project root directory to the Python path
sys.path.insert(0, os.path.abspath(os.path.dirname(os.path.dirname(__file__))))

import psycopg2
from neo4j import GraphDatabase
from common.config import settings

# Use environment variables or settings for connection details
def check_postgres():
    # Use localhost when running outside Docker
    host = "localhost"
    port = 5432  # Port from docker-compose.vm.yml
    
    print(f"Connecting to PostgreSQL at {host}:{port}")
    
    conn = psycopg2.connect(
        dbname=settings.POSTGRES_DB,
        user=settings.POSTGRES_USER,
        password=settings.POSTGRES_PASSWORD,
        host=host,
        port=port
    )
    cur = conn.cursor()
    cur.execute("SELECT COUNT(*) FROM entities;")
    print("Entities in Postgres:", cur.fetchone()[0])
    conn.close()

def check_neo4j():
    # Use localhost when running outside Docker
    host = "localhost"
    port = 7687  # Port from docker-compose.vm.yml
    
    print(f"Connecting to Neo4j at {host}:{port}")
    
    driver = GraphDatabase.driver(
        f"bolt://{host}:{port}", 
        auth=(settings.NEO4J_USER or "neo4j", settings.NEO4J_PASSWORD or "password")
    )
    with driver.session() as session:
        result = session.run("MATCH (e:Entity) RETURN count(e)")
        print("Entities in Neo4j:", result.single()[0])

if __name__ == "__main__":
    check_postgres()
    check_neo4j()
