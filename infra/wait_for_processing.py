import time
import requests

STATUS_URL = "http://localhost:8000/status"  # Update as needed

def wait_for_completion(timeout=600):
    start = time.time()
    while time.time() - start < timeout:
        r = requests.get(STATUS_URL)
        if r.json().get("all_jobs_completed"):
            print("All jobs completed.")
            return
        print("Waiting for jobs to complete...")
        time.sleep(10)
    raise TimeoutError("Processing did not complete in time.")

if __name__ == "__main__":
    wait_for_completion()
