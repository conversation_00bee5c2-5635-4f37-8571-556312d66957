"""
Test configuration for running tests on localhost (outside Docker environment).
This configuration overrides connection settings to use localhost and exposed ports.
"""
import os
from pydantic_settings import BaseSettings
from common.config import Settings


class LocalhostTestSettings(Settings):
    """Test settings optimized for localhost environment (outside Docker)."""

    # Database Settings - Use exposed port in docker-compose.yml
    POSTGRES_HOST: str = "localhost"
    POSTGRES_PORT: int = 5435  # Note: Uses exposed port 5435 from docker-compose

    # Redis Settings
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379

    # Storage (MinIO) Settings
    MINIO_HOST: str = "localhost"
    MINIO_PORT: int = 9090  # Note: Uses exposed port 9090 from docker-compose
    STORAGE_BUCKET_NAME: str = "test-longevity-documents"

    # Neo4j Settings
    NEO4J_HOST: str = "localhost"
    NEO4J_PORT: int = 7687

    # Test-specific settings
    TESTING: bool = True
    TEST_DATA_DIR: str = os.path.join(os.path.dirname(os.path.dirname(__file__)), "data")
    
    # For testing Vertex AI batch processing, use sample response file
    VERTEX_AI_BATCH_SAMPLE_RESPONSE: str = os.path.join(TEST_DATA_DIR, "sample_vertex_ai_batch_response.jsonl")


# Create a global test settings object for localhost
localhost_test_settings = LocalhostTestSettings()


def get_localhost_test_settings():
    """Return the localhost test settings object."""
    return localhost_test_settings