"""
Test configuration for running tests in Docker or cloud environments.
This configuration uses internal Docker network service names and default ports.
"""
import os
from pydantic_settings import BaseSettings
from common.config import Settings


class DockerTestSettings(Settings):
    """Test settings optimized for Docker/cloud environments."""

    # Database Settings - Use Docker service names
    POSTGRES_HOST: str = "postgres"
    POSTGRES_PORT: int = 5432  # Docker internal port

    # Redis Settings
    REDIS_HOST: str = "redis"
    REDIS_PORT: int = 6379

    # Storage (MinIO) Settings
    MINIO_HOST: str = "minio"
    MINIO_PORT: int = 9000  # Docker internal port
    STORAGE_BUCKET_NAME: str = "test-longevity-documents"

    # Neo4j Settings
    NEO4J_HOST: str = "neo4j"
    NEO4J_PORT: int = 7687

    # Test-specific settings
    TESTING: bool = True
    TEST_DATA_DIR: str = "/app/tests/data"  # Path inside Docker container
    
    # For testing Vertex AI batch processing, use sample response file
    VERTEX_AI_BATCH_SAMPLE_RESPONSE: str = os.path.join(TEST_DATA_DIR, "sample_vertex_ai_batch_response.jsonl")


# Create a global test settings object for Docker environment
docker_test_settings = DockerTestSettings()


def get_docker_test_settings():
    """Return the Docker/cloud test settings object."""
    return docker_test_settings