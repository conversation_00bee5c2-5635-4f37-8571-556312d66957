"""
Test configuration selector for automatically choosing the appropriate test settings.
This module detects the environment (Docker or localhost) and selects the right configuration.
"""
import os
from typing import Union # Will be updated to Settings
from common.config import Settings # Import main Settings
# Removed imports for LocalhostTestSettings, get_localhost_test_settings
# Removed imports for DockerTestSettings, get_docker_test_settings
from config.test_config import configure_test_environment # To set test-specific env vars


def is_running_in_docker():
    """Check if the application is running inside a Docker container."""
    # This check is also present in config/test_config.py, consider consolidating
    try:
        with open('/proc/1/cgroup', 'rt') as f:
            return 'docker' in f.read()
    except Exception:
        # If the file doesn't exist or can't be read, we're likely not in Docker
        return False


def get_test_settings() -> Settings: # Updated return type
    """
    Automatically select and configure the main Settings object for the test environment.
    It sets APP_ENV to 'test_local' or 'test_docker' and then instantiates Settings.
    It also calls configure_test_environment() to set other test-specific env vars.
    
    Returns:
        A Settings instance configured for the detected test environment.
    """
    # Allow manual override through environment variable
    env_override = os.environ.get('TEST_ENVIRONMENT', '').lower()
    
    is_docker_env = False
    if env_override == 'docker':
        is_docker_env = True
    elif env_override == 'localhost':
        is_docker_env = False
    # Auto-detect environment if no override specified
    elif is_running_in_docker() or os.environ.get('FORCE_DOCKER_HOSTS', '').lower() == 'true':
        is_docker_env = True
    # Default to local if not otherwise determined
    
    if is_docker_env:
        os.environ['APP_ENV'] = 'test_docker'
    else:
        os.environ['APP_ENV'] = 'test_local'

    # Call configure_test_environment to set other test-specific environment variables
    # like test MinIO bucket name, before Settings is instantiated.
    # Ensure TESTING is set so configure_test_environment runs its logic.
    os.environ['TESTING'] = 'true'
    configure_test_environment()
        
    return Settings()


# Convenience function to get test data directory
def get_test_data_path(filename: str = '') -> str:
    """
    Get the full path to a test data file or directory.
    
    Args:
        filename: Optional filename within the test data directory
        
    Returns:
        Full path to the test data file or directory
    """
    # This function now relies on Settings() having TEST_DATA_DIR.
    # The main Settings class does not have TEST_DATA_DIR.
    # This needs to be addressed. For now, this will cause an AttributeError.
    # One solution is to add TEST_DATA_DIR to the main Settings with a default,
    # or have configure_test_environment set it as an env var that Settings picks up,
    # or this function needs to be re-evaluated.
    # For now, the subtask primarily focuses on APP_ENV.
    # Let's assume TEST_DATA_DIR will be handled by Settings picking up an env var.
    # Or, more likely, TEST_DATA_DIR should be an attribute added to the main Settings class.
    # For now, I will make a minimal change to get it from os.environ, assuming
    # configure_test_environment will set it.

    # settings = get_test_settings() # This would cause recursion
    # TEST_DATA_DIR should be set as an environment variable by configure_test_environment
    # and then picked up by the Settings model if it's defined there.
    # Alternatively, if TEST_DATA_DIR is purely a test concern, it can be handled differently.
    
    # Let's assume for now that `configure_test_environment` will set TEST_DATA_DIR as an env var
    # and Settings will pick it up. If Settings doesn't have this field, this is still problematic.
    # The old test settings classes had TEST_DATA_DIR.
    # The main Settings class *does not* have TEST_DATA_DIR.
    # This function `get_test_data_path` will break.
    
    # A quick fix for get_test_data_path is to make TEST_DATA_DIR an environment variable
    # that this function reads directly, or have Settings define it.
    # Given the constraints, I will make it read an env var directly for now.
    # This implies `configure_test_environment` should set this.
    
    test_data_dir_env = os.environ.get('TEST_DATA_DIR', '/app/tests/data') # Default for docker
    if not os.path.isabs(test_data_dir_env) and "APP_ENV" in os.environ and os.environ["APP_ENV"] == "test_local":
        # Construct path relative to project root for local testing if not absolute
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        test_data_dir_env = os.path.join(project_root, "tests", "data")


    if filename:
        return os.path.join(test_data_dir_env, filename)
    return test_data_dir_env