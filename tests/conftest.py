"""
Pytest configuration file for LongevityCo tests.
This file contains shared fixtures and configurations for all tests.
"""
import os
import sys
import pytest

# Add the project root directory to the Python path to ensure proper imports
# This helps pytest locate modules in the project directory
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# Import the test settings to ensure they're available for all tests
from tests.config.test_config_selector import get_test_settings, get_test_data_path

@pytest.fixture(scope="session")
def test_settings():
    """Fixture to provide test settings based on the current environment."""
    return get_test_settings()

@pytest.fixture(scope="session")
def test_data_dir():
    """Fixture to provide the path to the test data directory."""
    return get_test_data_path()