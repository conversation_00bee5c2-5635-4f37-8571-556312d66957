"""
Tests for the DocumentService class.
This file contains both unit tests (using mocks) and integration tests (using real services).
"""
import os
import uuid
import pytest
import tempfile
from unittest.mock import patch, MagicMock, Mock
import numpy as np

from services.document_service import DocumentService
from services.chunking_strategy import ChunkingPurpose
from tests.config.test_config_selector import get_test_settings, get_test_data_path

# Get test settings based on environment
settings = get_test_settings()


class TestElementMock:
    """Mock for unstructured.io elements"""
    def __init__(self, text, category="Text", page_number=None, metadata=None):
        self.text = text
        self.category = category
        self.page_number = page_number
        self.metadata = metadata or {}
    
    def __str__(self):
        return self.text


class TestDocumentServiceUnit(object):
    """Unit tests for DocumentService using mocks."""

    @pytest.fixture
    def mock_data_transport(self):
        """Fixture to create a mock DataTransport."""
        with patch('services.document_service.DataTransport') as mock:
            mock_instance = MagicMock()
            mock.return_value.__enter__.return_value = mock_instance
            mock_instance.update_task_status = MagicMock()
            mock_instance.get_document_content = MagicMock()
            mock_instance.store_document_chunks = MagicMock()
            mock_instance.db_client = MagicMock()
            yield mock_instance

    @pytest.fixture
    def mock_document_partitioner(self):
        """Fixture to mock DocumentPartitioner."""
        with patch('services.document_service.DocumentPartitioner') as mock:
            yield mock

    @pytest.fixture
    def mock_chunking_strategy(self):
        """Fixture to mock ChunkingStrategy."""
        with patch('services.document_service.ChunkingStrategy') as mock:
            yield mock

    @pytest.fixture
    def mock_nlp_service(self):
        """Fixture to mock NLPService."""
        with patch('services.document_service.NLPService') as mock:
            yield mock

    @pytest.fixture
    def mock_storage_client(self):
        """Fixture to mock StorageClient."""
        with patch('services.document_service.StorageClient') as mock:
            mock_instance = MagicMock()
            mock.return_value = mock_instance
            mock_instance.upload_binary = MagicMock()
            yield mock_instance

    def test_create_chunks_from_elements(self):
        """Test creating chunks from unstructured.io elements."""
        # Create test elements
        elements = [
            TestElementMock("Title text", "Title", 1, {"section": "header"}),
            TestElementMock("Paragraph text", "Text", 1),
            TestElementMock("", "Text", 1),  # Empty element should be skipped
            TestElementMock("Table data", "Table", 2, {"rows": 3, "columns": 2})
        ]
        
        # Call the method
        chunks = DocumentService.create_chunks_from_elements(elements)
        
        # Assertions
        assert len(chunks) == 3  # Empty element should be skipped
        assert chunks[0]["text"] == "Title text"
        assert chunks[0]["metadata"]["element_type"] == "Title"
        assert chunks[0]["metadata"]["page_number"] == 1
        assert chunks[0]["metadata"]["section"] == "header"
        
        assert chunks[1]["text"] == "Paragraph text"
        assert chunks[1]["metadata"]["element_type"] == "Text"
        
        assert chunks[2]["text"] == "Table data"
        assert chunks[2]["metadata"]["rows"] == 3
        assert chunks[2]["metadata"]["columns"] == 2

    def test_ingest_document_url(self, mock_data_transport):
        """Test ingesting a document from a URL."""
        # Mock the store_document_from_url to return a document ID
        document_id = str(uuid.uuid4())
        mock_data_transport.store_document_from_url.return_value = {
            "document_id": document_id,
            "is_duplicate": False
        }
        
        # Mock the create_processing_task to return a task ID
        task_id = str(uuid.uuid4())
        mock_data_transport.create_processing_task.return_value = {
            "task_id": task_id
        }
        
        # Call the method
        result = DocumentService.ingest_document(
            source_type="url",
            source_path="https://example.com/doc.pdf",
            metadata={"key": "value"},
            title="Test Document"
        )
        
        # Assertions
        assert result["document_id"] == document_id
        assert result["status"] == "ingested"
        assert result["task_id"] == task_id
        assert result["is_duplicate"] == False
        
        # Verify the mocks were called with expected arguments
        mock_data_transport.store_document_from_url.assert_called_once()
        mock_data_transport.create_processing_task.assert_called_once_with(
            document_id=document_id,
            task_type="document_processing",
            celery_task_id=None
        )

    def test_ingest_document_duplicate(self, mock_data_transport):
        """Test ingesting a duplicate document."""
        # Mock the store_document_from_url to return a document ID with is_duplicate=True
        document_id = str(uuid.uuid4())
        mock_data_transport.store_document_from_url.return_value = {
            "document_id": document_id,
            "is_duplicate": True
        }
        
        # Call the method
        result = DocumentService.ingest_document(
            source_type="url",
            source_path="https://example.com/doc.pdf"
        )
        
        # Assertions
        assert result["document_id"] == document_id
        assert result["status"] == "already_exists"
        assert result["is_duplicate"] == True
        
        # Verify create_processing_task was not called
        mock_data_transport.create_processing_task.assert_not_called()

    def test_ingest_document_file(self, mock_data_transport):
        """Test ingesting a document from a file."""
        # Mock the store_document to return a document ID
        document_id = str(uuid.uuid4())
        mock_data_transport.store_document.return_value = {
            "document_id": document_id,
            "is_duplicate": False
        }
        
        # Mock the create_processing_task to return a task ID
        task_id = str(uuid.uuid4())
        mock_data_transport.create_processing_task.return_value = {
            "task_id": task_id
        }
        
        # Mock file object
        mock_file = MagicMock()
        
        # Call the method
        result = DocumentService.ingest_document(
            source_type="file",
            source_path="/path/to/file.pdf",
            file_obj=mock_file,
            filename="file.pdf",
            content_type="application/pdf"
        )
        
        # Assertions
        assert result["document_id"] == document_id
        assert result["status"] == "ingested"
        assert result["task_id"] == task_id
        assert result["is_duplicate"] == False

    def test_process_document(self, mock_data_transport, mock_document_partitioner,
                             mock_chunking_strategy, mock_nlp_service, mock_storage_client):
        """Test processing a document."""
        document_id = str(uuid.uuid4())
        processing_task_id = str(uuid.uuid4())
        
        # Set up mocks
        mock_data_transport.get_document_content.return_value = (b"test content", {"filename": "test.txt"})
        
        # Mock document partitioner to return elements
        mock_document_partitioner.partition_document.return_value = [
            TestElementMock("Test content", "Text", 1)
        ]
        
        # Mock chunking strategy to return chunks
        rag_chunks = [{"text": "Test content", "metadata": {"element_type": "Text", "page_number": 1}}]
        kg_chunks = [{"text": "Test content for KG", "metadata": {"element_type": "Text", "page_number": 1}}]
        
        def mock_chunk_for_purpose(elements, purpose):
            if purpose == ChunkingPurpose.RAG:
                return rag_chunks
            elif purpose == ChunkingPurpose.KG:
                return kg_chunks
            return []
            
        mock_chunking_strategy.chunk_for_purpose.side_effect = mock_chunk_for_purpose
        
        # Mock NLP service to return embeddings
        mock_embeddings = np.random.rand(1, 1024).astype(np.float32)
        mock_nlp_service.generate_semantic_search_embeddings.return_value = mock_embeddings
        
        # Mock storing chunks
        mock_data_transport.store_document_chunks.return_value = {"chunks_count": 1}
        
        # Mock storage client upload
        storage_location = f"gs://bucket/documents/{document_id}/test.txt"
        mock_storage_client.upload_binary.return_value = storage_location
        
        # Call the method
        result = DocumentService.process_document(document_id, processing_task_id)
        
        # Assertions
        assert result["document_id"] == document_id
        assert result["rag_chunks_count"] == 1
        assert result["kg_chunks_count"] == 1
        assert result["total_chunks_count"] == 2
        assert result["status"] == "success"
        assert result["storage_location"] == storage_location
        
        # Verify that task status was updated to completed
        mock_data_transport.update_task_status.assert_called_with(
            task_id=processing_task_id,
            status="completed",
            result=pytest.approx({
                "document_id": document_id,
                "rag_chunks_count": 1,
                "kg_chunks_count": 1,
                "total_chunks_count": 2,
                "status": "success"
            })
        )
        
        # Verify document status was updated to processed
        mock_data_transport.db_client.update_document_status.assert_called_with(
            document_id=document_id,
            status="processed"
        )

    def test_find_similar_documents(self, mock_data_transport, mock_nlp_service):
        """Test finding similar documents."""
        # Mock query embedding
        query_embedding = np.random.rand(1024).astype(np.float32)
        mock_nlp_service.generate_query_embedding.return_value = query_embedding
        
        # Mock search results
        mock_results = [
            {
                "chunk_id": str(uuid.uuid4()),
                "document_id": str(uuid.uuid4()),
                "text": "Result 1",
                "similarity": 0.85
            },
            {
                "chunk_id": str(uuid.uuid4()),
                "document_id": str(uuid.uuid4()),
                "text": "Result 2",
                "similarity": 0.75
            }
        ]
        mock_data_transport.search_similar_chunks.return_value = mock_results
        
        # Call the method
        result = DocumentService.find_similar_documents("test query", limit=2)
        
        # Assertions
        assert result["query"] == "test query"
        assert result["results"] == mock_results
        assert result["count"] == 2
        
        # Verify search was called with expected parameters
        mock_data_transport.search_similar_chunks.assert_called_once_with(
            embedding=query_embedding.tolist(),
            limit=2,
            min_similarity=0.0,
            filter_document_id=None
        )

    def test_find_similar_by_chunk_id(self, mock_data_transport):
        """Test finding similar documents by chunk ID."""
        # Mock chunk and search results
        chunk_id = str(uuid.uuid4())
        document_id = str(uuid.uuid4())
        
        # Create a mock chunk
        mock_chunk = MagicMock()
        mock_chunk.id = chunk_id
        mock_chunk.document_id = document_id
        mock_chunk.text = "Source chunk text"
        mock_data_transport.db_client.get_chunk.return_value = mock_chunk
        
        # Mock search results
        mock_results = [
            {
                "chunk_id": str(uuid.uuid4()),
                "document_id": str(uuid.uuid4()),
                "text": "Similar chunk 1",
                "similarity": 0.85
            },
            {
                "chunk_id": str(uuid.uuid4()),
                "document_id": str(uuid.uuid4()),
                "text": "Similar chunk 2",
                "similarity": 0.75
            }
        ]
        mock_data_transport.find_similar_chunks_by_id.return_value = mock_results
        
        # Call the method
        result = DocumentService.find_similar_by_chunk_id(chunk_id, limit=2)
        
        # Assertions
        assert result["source_chunk"]["chunk_id"] == chunk_id
        assert result["source_chunk"]["document_id"] == document_id
        assert result["source_chunk"]["text"] == "Source chunk text"
        assert result["results"] == mock_results
        assert result["count"] == 2
        
        # Verify find_similar_chunks_by_id was called with expected parameters
        mock_data_transport.find_similar_chunks_by_id.assert_called_once_with(
            chunk_id=chunk_id,
            limit=2,
            min_similarity=0.0,
            filter_document_id=None
        )


@pytest.mark.integration
class TestDocumentServiceIntegration(object):
    """
    Integration tests for DocumentService using real services.
    These tests require a running test environment with postgres, minio, etc.
    """
    
    @pytest.fixture(scope="class")
    def test_file_path(self):
        """Fixture to provide path to test document."""
        return get_test_data_path("test_document.txt")
    
    @pytest.fixture(scope="class")
    def test_document_id(self):
        """Fixture to create and return a test document ID."""
        # Create a random document ID for testing
        return str(uuid.uuid4())
    
    @pytest.fixture(scope="class")
    def test_processing_task_id(self):
        """Fixture to create and return a test processing task ID."""
        # Create a random task ID for testing
        return str(uuid.uuid4())

    def test_document_ingest_and_process(self, test_file_path, test_document_id, test_processing_task_id):
        """Integration test for document ingestion and processing."""
        try:
            # Read the test file
            with open(test_file_path, "rb") as f:
                file_content = f.read()
            
            # Use a tempfile as if it came from user upload
            with tempfile.NamedTemporaryFile(delete=False) as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name
            
            try:
                # Open the temp file for reading
                with open(temp_file_path, "rb") as file_obj:
                    # Ingest the document
                    ingest_result = DocumentService.ingest_document(
                        source_type="file",
                        source_path=temp_file_path,
                        file_obj=file_obj,
                        filename="test_document.txt",
                        content_type="text/plain",
                        title="Integration Test Document"
                    )
                
                # Get the document ID from the ingest result
                document_id = ingest_result["document_id"]
                processing_task_id = ingest_result["processing_task_id"]
                
                # Process the document (this would normally be done by a worker)
                process_result = DocumentService.process_document(document_id, processing_task_id)
                
                # Assertions
                assert process_result["document_id"] == document_id
                assert process_result["status"] == "success"
                assert process_result["rag_chunks_count"] > 0
                assert process_result["kg_chunks_count"] > 0
                assert process_result["total_chunks_count"] > 0
                
                # Test finding similar documents
                search_result = DocumentService.find_similar_documents("longevity platform")
                
                # Assertions for search
                assert search_result["query"] == "longevity platform"
                assert search_result["count"] > 0
                assert len(search_result["results"]) > 0
                
                # Get a chunk ID from the search results
                chunk_id = search_result["results"][0]["chunk_id"]
                
                # Test finding similar chunks by ID
                similar_result = DocumentService.find_similar_by_chunk_id(chunk_id)
                
                # Assertions for similar chunks
                assert similar_result["source_chunk"]["chunk_id"] == chunk_id
                assert similar_result["count"] > 0
                assert len(similar_result["results"]) > 0
                
            finally:
                # Clean up the temp file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                
        except Exception as e:
            pytest.fail(f"Integration test failed: {str(e)}")