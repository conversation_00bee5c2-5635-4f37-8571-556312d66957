import os
import time
import requests
from common.config import settings

def purge_databases():
    """Run the purge_databases.py script to clear Neo4j and PostgreSQL."""
    os.system("python3 purge_databases.py")

def insert_document(file_path):
    """Insert a document into the system using the API and return the ingestion task ID."""
    url = f"http://{settings.API_HOST}:{settings.API_PORT}/api/documents/ingest"
    with open(file_path, 'rb') as f:
        response = requests.post(url, files={'file': f})
    response.raise_for_status()
    return response.json().get('task_id') # Return task_id

def get_document_id_from_ingestion_task(ingestion_task_id):
    """Poll the ingestion task status until it's complete and return the document_id."""
    # Use the endpoint to get task by Celery ID. This might return multiple tasks if retried,
    # but the ingestion task should create a document and an initial processing task.
    url = f"http://{settings.API_HOST}:{settings.API_PORT}/api/tasks/by-celery-id/{ingestion_task_id}"
    document_id = None
    attempts = 0
    max_attempts = 2 # Limit to 2 attempts

    while attempts < max_attempts:
        attempts += 1
        print(f"Attempt {attempts}/{max_attempts} to get document_id for Celery Task ID {ingestion_task_id}")
        response = requests.get(url)
        response.raise_for_status()
        response_data = response.json()
        tasks = response_data.get('tasks', [])
        print(f"Polling Celery Task ID {ingestion_task_id} - Found {len(tasks)} tasks.")

        for task_data in tasks:
            status = task_data.get('status')
            retrieved_doc_id = task_data.get('document_id')
            task_type = task_data.get('task_type')
            print(f"  - Task ID {task_data.get('id')}, Type: {task_type}, Status: {status}, Document ID: {retrieved_doc_id}")

            if retrieved_doc_id:
                document_id = retrieved_doc_id
                if status == 'COMPLETED' and task_type == 'document_ingestion':
                     return document_id
                elif status == 'PROCESSING' and task_type == 'document_ingestion' and document_id:
                    return document_id
                elif document_id: # Fallback if we get a document_id from any related task
                    return document_id

        if document_id:
            return document_id

        all_failed = all(t.get('status') == 'FAILED' for t in tasks)
        if tasks and all_failed:
            print(f"All tasks for Celery ID {ingestion_task_id} failed before a document_id could be retrieved.")
            return None # Return None if all tasks failed

        if attempts < max_attempts:
            print(f"Waiting for document_id for Celery Task ID {ingestion_task_id}...")
            time.sleep(10) # Increased sleep time
        else:
            print(f"Max attempts reached for Celery Task ID {ingestion_task_id}. Could not retrieve document_id.")
            return None # Return None if max attempts reached

def monitor_task_status(document_id):
    """Monitor the task statuses for a document."""
    # Get all tasks for the given document_id
    url = f"http://{settings.API_HOST}:{settings.API_PORT}/api/tasks/by-document/{document_id}"
    print(f"Monitoring tasks for Document ID {document_id} using URL: {url}")
    
    # Define the expected sequence of task types and their terminal statuses
    # This is a simplified example. You'll need to adjust based on your actual task workflow.
    expected_task_flow = {
        "document_ingestion": "COMPLETED",
        "chunking": "COMPLETED",
        "embedding": "COMPLETED",
        "vertex_ai_batch_job": "COMPLETED", # Assuming this is one of the tasks
        "knowledge_graph_building": "COMPLETED" # Assuming this is the final task
    }
    # Track the status of main processing stages
    # This needs to align with your actual task types and how they are named.
    # Using a dictionary to store the latest status of each expected task type.
    task_statuses = {}
    
    while True:
        response = requests.get(url)
        response.raise_for_status()
        data = response.json()
        tasks = data.get('tasks', [])

        if not tasks:
            print(f"No tasks found yet for document ID {document_id}. Waiting...")
            time.sleep(10)
            continue

        all_processing_completed = True
        found_critical_failure = False

        print(f"--- Status for Document ID {document_id} ---")
        for task in tasks:
            task_type = task.get('task_type')
            status = task.get('status')
            task_id = task.get('id')
            print(f"  Task ID: {task_id}, Type: {task_type}, Status: {status}")
            
            if task_type in expected_task_flow:
                task_statuses[task_type] = status
                if status == 'FAILED':
                    print(f"CRITICAL FAILURE: Task {task_type} (ID: {task_id}) for document {document_id} FAILED.")
                    found_critical_failure = True
                    break 
        
        if found_critical_failure:
            return "FAILED" # Overall failure

        # Check if all expected tasks have completed
        all_processing_completed = True
        for task_type, expected_status in expected_task_flow.items():
            current_status = task_statuses.get(task_type)
            if current_status != expected_status:
                all_processing_completed = False
                break
        
        if all_processing_completed:
            print(f"All expected tasks for document {document_id} have COMPLETED.")
            return "COMPLETED"

        print(f"Document {document_id} processing not yet complete. Current task statuses: {task_statuses}")
        print("-----------------------------------------")
        time.sleep(10) # Check every 10 seconds

def test_task_status_monitoring():
    """Test the task status monitoring workflow."""
    # Step 1: Purge databases
    print("Purging databases...")
    purge_databases()

    # Step 2: Insert sample documents
    test_data_dir = "tests/data"
    for file_name in os.listdir(test_data_dir):
        file_path = os.path.join(test_data_dir, file_name)
        if os.path.isfile(file_path):
            print(f"Inserting document: {file_name}")
            ingestion_task_id = insert_document(file_path)
            print(f"Document {file_name} ingestion task ID: {ingestion_task_id}")

            document_id = get_document_id_from_ingestion_task(ingestion_task_id)
            if not document_id:
                print(f"Could not retrieve document_id for ingestion task {ingestion_task_id}. Skipping monitoring for this document.")
                continue

            # Step 3: Monitor task statuses
            print(f"Monitoring task statuses for document ID: {document_id}")
            final_status = monitor_task_status(document_id)
            assert final_status == 'COMPLETED', f"Document {document_id} did not complete successfully. Final status: {final_status}"

if __name__ == "__main__":
    test_task_status_monitoring()
