"""
Vertex AI client for interacting with Google Cloud Vertex AI API.
"""
import logging
import json
import uuid
from typing import List, Dict, Any, Optional, Union
from concurrent.futures import ThreadPoolExecutor

import google.auth
from google.cloud import aiplatform
from google.cloud import storage
from google.oauth2 import service_account

# Import GenerativeModel if available, otherwise use a mock
try:
    from vertexai.generative_models import GenerativeModel
except ImportError:
    # Create a mock GenerativeModel for environments without vertexai
    class GenerativeModel:
        def __init__(self, model_name):
            self.model_name = model_name

        def generate_content(self, prompt, **kwargs):
            from collections import namedtuple
            Response = namedtuple('Response', ['text'])
            return Response(text="[]")

from common.config import settings
from services.prompt_templates import PromptTemplates

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class VertexAIClient:
    """Client for interacting with Google Cloud Vertex AI API."""

    def __init__(self):
        """Initialize Vertex AI client with settings."""
        self._initialize_client()
        self._model = None
        self._initialized = True

    def _initialize_client(self):
        """Initialize Vertex AI client."""
        try:
            # Check if credentials file path is provided
            if hasattr(settings, 'GOOGLE_APPLICATION_CREDENTIALS') and settings.GOOGLE_APPLICATION_CREDENTIALS:
                # Use service account credentials with appropriate scopes
                credentials = service_account.Credentials.from_service_account_file(
                    settings.GOOGLE_APPLICATION_CREDENTIALS,
                    scopes=['https://www.googleapis.com/auth/cloud-platform']
                )
                self.project_id = credentials.project_id
                aiplatform.init(
                    project=self.project_id,
                    location=settings.VERTEX_AI_LOCATION,
                    credentials=credentials
                )
            else:
                # Use default credentials
                credentials, self.project_id = google.auth.default()
                aiplatform.init(
                    project=self.project_id,
                    location=settings.VERTEX_AI_LOCATION
                )

            logger.info(f"Initialized Vertex AI client for project: {self.project_id}")
        except Exception as e:
            logger.error(f"Failed to initialize Vertex AI client: {e}")
            raise

    def _get_model(self):
        """Get or initialize the LLM model."""
        if self._model is None:
            try:
                self._model = GenerativeModel(settings.VERTEX_AI_MODEL_ID)
                logger.info(f"Initialized Vertex AI model: {settings.VERTEX_AI_MODEL_ID}")
            except Exception as e:
                logger.error(f"Failed to initialize Vertex AI model: {e}")
                raise
        return self._model

    def extract_entities_and_relationships(self, chunk_text: str) -> Dict[str, Any]:
        """
        Extract entities and relationships from text using Vertex AI LLM.

        Args:
            chunk_text: Text chunk to analyze

        Returns:
            Dict[str, Any]: Extracted entities and relationships
        """
        model = self._get_model()

        # Use prompt template for entity extraction
        entity_prompt = PromptTemplates.entity_extraction_prompt(chunk_text, chunk_id=str(uuid.uuid4()))

        try:
            # Get entity extraction response
            entity_response = model.generate_content(
                entity_prompt,
                generation_config={
                    "temperature": 0.1,
                    "max_output_tokens": 1024,
                }
            )

            # Parse entity response
            entities = self._parse_json_response(entity_response.text)

            # If we have multiple entities, extract relationships
            if len(entities) > 1:
                # Use prompt template for relationship extraction
                relationship_prompt = PromptTemplates.relationship_extraction_prompt(chunk_text, entities)

                # Get relationship extraction response
                relationship_response = model.generate_content(
                    relationship_prompt,
                    generation_config={
                        "temperature": 0.1,
                        "max_output_tokens": 1024,
                    }
                )

                # Parse relationship response
                relationships = self._parse_json_response(relationship_response.text)
            else:
                relationships = []

            return {
                "entities": entities,
                "relationships": relationships
            }

        except Exception as e:
            logger.error(f"Error extracting entities and relationships: {e}")
            return {"entities": [], "relationships": []}

    def process_chunks_in_parallel(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process multiple chunks in parallel using Vertex AI online API.

        Args:
            chunks: List of chunk objects with text

        Returns:
            List[Dict[str, Any]]: Processed chunks with entities and relationships
        """
        # Check if we have chunks to process
        if not chunks:
            return []

        # Filter out empty chunks
        valid_chunks = []
        for chunk in chunks:
            chunk_id = chunk.get("id")
            chunk_text = chunk.get("text", "")
            
            if not chunk_text or len(chunk_text.strip()) == 0:
                logger.warning(f"Skipping empty chunk: {chunk_id}")
                continue
                
            valid_chunks.append({
                "id": chunk_id,
                "text": chunk_text
            })
        
        if not valid_chunks:
            return []

        # Use parallel processing with online API
        logger.info(f"Using parallel processing with online API for {len(valid_chunks)} chunks")
        return self._process_chunks_in_parallel(valid_chunks)

    def _process_chunks_in_parallel(self, chunks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process chunks in parallel using online prediction API.

        Args:
            chunks: List of chunk objects with text

        Returns:
            List[Dict[str, Any]]: Processed chunks with entities and relationships
        """
        results = []

        # Process chunks in parallel using ThreadPoolExecutor
        with ThreadPoolExecutor(max_workers=min(10, len(chunks))) as executor:
            # Submit all tasks
            future_to_chunk = {executor.submit(self._process_single_chunk, chunk): chunk for chunk in chunks}

            # Collect results as they complete
            for future in future_to_chunk:
                chunk = future_to_chunk[future]
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    logger.error(f"Error processing chunk {chunk.get('id')}: {e}")
                    # Add empty result for failed chunk
                    results.append({
                        "chunk_id": chunk.get("id"),
                        "entities": [],
                        "relationships": [],
                        "error": str(e)
                    })

        return results

    def _process_single_chunk(self, chunk: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a single chunk using online prediction API.

        Args:
            chunk: Chunk object with text

        Returns:
            Dict[str, Any]: Processed chunk with entities and relationships
        """
        chunk_id = chunk.get("id")
        chunk_text = chunk.get("text", "")

        logger.info(f"Processing chunk {chunk_id} with Vertex AI online API")

        try:
            # Extract entities and relationships
            extraction_result = self.extract_entities_and_relationships(chunk_text)

            # Add chunk ID to result
            extraction_result["chunk_id"] = chunk_id

            return extraction_result

        except Exception as e:
            logger.error(f"Error processing chunk {chunk_id}: {e}")
            return {
                "chunk_id": chunk_id,
                "entities": [],
                "relationships": [],
                "error": str(e)
            }

    def _ensure_initialized(self):
        """Ensure the client is initialized."""
        if not hasattr(self, '_initialized') or not self._initialized:
            self._initialize_client()
            self._initialized = True

    def _extract_relationships(self, chunk_text: str, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Extract relationships between entities using online API.

        Args:
            chunk_text: Text chunk
            entities: List of entities

        Returns:
            List[Dict[str, Any]]: Extracted relationships
        """
        if not entities or len(entities) < 2:
            return []

        model = self._get_model()

        # Use prompt template for relationship extraction
        relationship_prompt = PromptTemplates.relationship_extraction_prompt(chunk_text, entities)

        try:
            # Get relationship extraction response
            relationship_response = model.generate_content(
                relationship_prompt,
                generation_config={
                    "temperature": 0.1,
                    "max_output_tokens": 1024,
                }
            )

            # Parse relationship response
            return self._parse_json_response(relationship_response.text)

        except Exception as e:
            logger.error(f"Error extracting relationships: {e}")
            return []

    def normalize_entities(self, entities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Normalize entities and eliminate duplicates using Vertex AI LLM.

        Args:
            entities: List of entities to normalize

        Returns:
            List[Dict[str, Any]]: Normalized entities with duplicates marked
        """
        if not entities:
            return []

        model = self._get_model()

        # Use prompt template for entity normalization
        normalization_prompt = PromptTemplates.entity_normalization_prompt(entities)

        try:
            # Get normalization response
            normalization_response = model.generate_content(
                normalization_prompt,
                generation_config={
                    "temperature": 0.1,
                    "max_output_tokens": 1024,
                }
            )

            # Parse normalization response
            normalized_entities = self._parse_json_response(normalization_response.text)

            # Process normalized entities to mark duplicates
            result = []
            duplicate_indices = set()

            for entity in normalized_entities:
                original_idx = entity.get("original_index")
                similar_to = entity.get("similar_to", [])

                # Skip if this is a duplicate of an entity we've already processed
                if original_idx in duplicate_indices:
                    continue

                # Mark similar entities as duplicates
                if similar_to:
                    duplicate_indices.update(similar_to)

                # Get the original entity and update it
                if 0 <= original_idx < len(entities):
                    original_entity = entities[original_idx].copy()
                    original_entity["normalized_text"] = entity.get("normalized_text")
                    original_entity["normalized_id"] = str(uuid.uuid4())  # Generate a normalized ID
                    original_entity["is_duplicate"] = False
                    original_entity["duplicate_of"] = None
                    result.append(original_entity)

            # Add duplicate information to the result
            for idx, entity in enumerate(entities):
                if idx in duplicate_indices:
                    # Find which entity this is a duplicate of
                    for norm_entity in normalized_entities:
                        if idx in norm_entity.get("similar_to", []):
                            original_idx = norm_entity.get("original_index")
                            if 0 <= original_idx < len(result):
                                duplicate = entity.copy()
                                duplicate["normalized_text"] = norm_entity.get("normalized_text")
                                duplicate["normalized_id"] = result[original_idx]["normalized_id"]  # Use the same normalized ID
                                duplicate["is_duplicate"] = True
                                duplicate["duplicate_of"] = original_idx
                                result.append(duplicate)
                            break

            return result

        except Exception as e:
            logger.error(f"Error normalizing entities: {e}")
            return entities  # Return original entities if normalization fails

    def _parse_json_response(self, response_text: str) -> List[Dict[str, Any]]:
        """
        Parse JSON response from LLM.

        Args:
            response_text: Response text from LLM

        Returns:
            List[Dict[str, Any]]: Parsed JSON data
        """
        try:
            # Clean up response text to extract JSON
            cleaned_text = response_text.strip()

            # Find JSON array in response
            start_idx = cleaned_text.find('[')
            end_idx = cleaned_text.rfind(']') + 1

            if start_idx >= 0 and end_idx > start_idx:
                json_text = cleaned_text[start_idx:end_idx]
                return json.loads(json_text)
            else:
                logger.warning(f"Could not find JSON array in response: {cleaned_text}")
                return []

        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON response: {e}")
            logger.error(f"Response text: {response_text}")
            return []
