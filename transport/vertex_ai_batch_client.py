"""
Vertex AI Batch Client for batch processing with Google Cloud Vertex AI API.
"""
import logging
import json
import re
import uuid
from typing import List, Dict, Any, Optional, Union, Tuple
import os
import time

import google.auth

from common.id_validator import validate_id_with_context

def normalize_entity_type(entity_type: str) -> str:
    """
    Normalize entity type to ensure consistency.

    Args:
        entity_type: Entity type to normalize

    Returns:
        str: Normalized entity type
    """
    if not entity_type:
        return "unknown"

    # Lowercase and trim whitespace
    normalized = entity_type.lower().strip()

    # Define standard entity types (singular form)
    standard_types = {
        # Singular to plural mapping
        "biomarker": "biomarkers",
        "disease": "diseases",
        "intervention": "interventions",
        "supplement": "supplements",
        "tracker": "trackers",
        "lifestyle factor": "lifestyle factors",
        "genetic factor": "genetic factors",
        "organization": "organizations/people",
        "person": "organizations/people",
        "organization/person": "organizations/people",

        # Ensure plural forms map to themselves
        "biomarkers": "biomarkers",
        "diseases": "diseases",
        "interventions": "interventions",
        "supplements": "supplements",
        "trackers": "trackers",
        "lifestyle factors": "lifestyle factors",
        "genetic factors": "genetic factors",
        "organizations/people": "organizations/people"
    }

    # Check for exact match in standard types
    if normalized in standard_types:
        return standard_types[normalized]

    # Check for singular/plural forms
    if normalized.endswith('s') and normalized[:-1] in standard_types:
        # Handle plural form (e.g., "biomarkers" -> "biomarkers")
        singular = normalized[:-1]
        return standard_types[singular]
    elif normalized + 's' in standard_types:
        # Handle singular form (e.g., "biomarker" -> "biomarkers")
        plural = normalized + 's'
        return standard_types[plural]

    # If no match found, return the original type
    return entity_type
from google.cloud import aiplatform
from google.cloud import storage
from google.oauth2 import service_account
from google.cloud.aiplatform.compat.types import job_state as gca_job_state

from common.config import settings
from services.prompt_templates import PromptTemplates

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class VertexAIBatchClient:
    """Client for batch processing with Google Cloud Vertex AI API."""

    def __init__(self):
        """Initialize Vertex AI Batch client with settings."""
        self._initialize_client()

    def _initialize_client(self):
        """Initialize Vertex AI client."""
        try:
            # Use the credentials file from settings
            credentials_file = settings.GCS_CREDENTIALS_FILE

            # Use service account credentials with appropriate scopes
            self.credentials = service_account.Credentials.from_service_account_file(
                credentials_file,
                scopes=['https://www.googleapis.com/auth/cloud-platform']
            )
            self.project_id = self.credentials.project_id
            self.location = settings.VERTEX_AI_LOCATION
            aiplatform.init(
                project=self.project_id,
                location=settings.VERTEX_AI_LOCATION,
                credentials=self.credentials
            )

            logger.info(f"Initialized Vertex AI Batch client for project: {self.project_id} using credentials file: {credentials_file}")
        except Exception as e:
            logger.error(f"Failed to initialize Vertex AI Batch client: {e}")
            raise

    def get_batch_job_name(self, batch_job_id: str) -> str:
        """Formats the full batch prediction job name for the Vertex AI API."""
        if not batch_job_id.startswith('projects/'):
            project_id = self.project_id
            name = f"projects/{project_id}/locations/{self.location}/batchPredictionJobs/{batch_job_id.split('/')[-1]}"
            logger.info(f"Created job name from non-project ID: {name}")
        else:
            parts = batch_job_id.split('/')
            logger.info(f"Batch job ID parts: {parts}, length: {len(parts)}")

            if len(parts) >= 6 and parts[2] == 'locations':
                project_id = parts[1]
                original_location = parts[3]
                job_id = parts[5]
                logger.info(f"Extracted project_id: {project_id}, original_location: {original_location}, job_id: {job_id}")

                name = f"projects/{project_id}/locations/{original_location}/batchPredictionJobs/{job_id}"
                logger.info(f"Using original location in job name: {name}")
            else:
                name = batch_job_id
                logger.info(f"Using original batch job ID as name: {name}")
        return name

    def batch_process_chunks(self, chunks: List[Dict[str, Any]], wait_for_results: bool = True) -> Union[List[Dict[str, Any]], Dict[str, Any]]:
        """
        Process multiple chunks using Vertex AI Batch API.

        Args:
            chunks: List of chunk objects with text
            wait_for_results: Whether to wait for results (True) or return immediately (False)

        Returns:
            If wait_for_results=True: List[Dict[str, Any]]: Processed chunks with entities and relationships
            If wait_for_results=False: Dict[str, Any]: Batch job information
        """
        if not chunks:
            return [] if wait_for_results else {"status": "error", "message": "No chunks to process"}

        valid_chunks = []
        for i, chunk in enumerate(chunks):
            chunk_id = chunk.get("id")
            normalized_id = validate_id_with_context(chunk_id, f"Chunk {i}", logger)
            if not normalized_id:
                logger.error(f"Chunk at index {i} has an invalid ID, skipping")
                continue

            if normalized_id != chunk_id:
                chunk["id"] = normalized_id
                chunk_id = normalized_id

            chunk_text = chunk.get("text", "")
            if not chunk_text or len(chunk_text.strip()) == 0:
                logger.warning(f"Skipping empty chunk: {chunk_id}")
                continue

            logger.info(f"Validated chunk {i}: ID={chunk_id}, Text length={len(chunk_text)}")
            valid_chunks.append(chunk)

        if not valid_chunks:
            return [] if wait_for_results else {"status": "error", "message": "No valid chunks to process"}

        logger.info(f"Using batch API for {len(valid_chunks)} chunks, wait_for_results={wait_for_results}")
        return self._batch_process_with_batch_api(valid_chunks, wait_for_results)

    def _create_entity_extraction_prompt(self, chunk_text: str, chunk_id: str) -> Dict[str, Any]:
        """Creates the standard entity extraction prompt structure."""
        return {
            "request": {
                "contents": [
                    {
                        "role": "user",
                        "parts": [
                            {
                                "text": PromptTemplates.entity_extraction_prompt(chunk_text, chunk_id)
                            }
                        ]
                    }
                ]
            }
        }

    def _batch_process_with_batch_api(self, chunks: List[Dict[str, Any]], wait_for_results: bool = True) -> Union[List[Dict[str, Any]], Dict[str, Any]]:
        logger.info(f"Processing {len(chunks)} chunks with Vertex AI Batch API, wait_for_results={wait_for_results}")

        batch_job_id = f"kg-batch-{uuid.uuid4().hex[:8]}"

        try:
            input_uri = self._create_batch_input_file(chunks, batch_job_id)
            output_uri_prefix = f"gs://{settings.GCS_BUCKET_NAME}/batch-predictions/{batch_job_id}/output"
            model_name = settings.VERTEX_AI_BATCH_MODEL_ID

            logger.info(f"Submitting Batch Prediction Job for model: {model_name}")
            batch_prediction_job = aiplatform.BatchPredictionJob.create(
                job_display_name=f"kg-batch-job-{batch_job_id}",
                model_name=model_name,
                instances_format="jsonl",
                predictions_format="jsonl",
                gcs_source=[input_uri],
                gcs_destination_prefix=output_uri_prefix,
                model_parameters={
                    "temperature": 0.1,
                    "maxOutputTokens": 1024,
                    "topP": 0.8,
                    "topK": 40
                }
            )

            if not batch_prediction_job.resource_name:
                raise Exception("Failed to create batch prediction job")

            logger.info(f"Created batch prediction job: {batch_prediction_job.resource_name}")

            try:
                from transport.data_transport import DataTransport
                with DataTransport() as transport:
                    batch_task = transport.create_processing_task(
                        document_id=chunks[0].get("document_id") if chunks and "document_id" in chunks[0] else None,
                        task_type="vertex_ai_batch_job",
                        metadata={
                            "batch_job_id": batch_prediction_job.resource_name,
                            "input_uri": input_uri,
                            "output_uri": output_uri_prefix,
                            "chunk_count": len(chunks),
                            "model_name": model_name,
                            "batch_job_id_str": batch_job_id,
                            "status": "created"
                        }
                    )
                    logger.info(f"Created processing task for batch job: {batch_task['task_id']}")
            except Exception as e:
                raise Exception(f"Failed to create processing task: {e}")

            batch_prediction_job._temp_input_gcs_uri = input_uri

            if not wait_for_results:
                return {
                    "status": "submitted",
                    "batch_job_id": batch_prediction_job.resource_name,
                    "input_uri": input_uri,
                    "output_uri": output_uri_prefix,
                    "chunk_count": len(chunks)
                }

            self.wait_for_batch_job(batch_prediction_job)

            success, error_message = self._check_job_state(batch_prediction_job)
            if not success:
                raise Exception(f"Batch prediction job failed: {error_message}")

            results = self.get_batch_results(batch_prediction_job)
            self.cleanup_batch_input(batch_prediction_job)

            return results

        except Exception as e:
            logger.error(f"Error in batch processing: {e}")
            return {
                "status": "error",
                "message": str(e)
            }

    def _create_batch_input_file(self, chunks: List[Dict[str, Any]], batch_job_id: str) -> str:
        storage_client = storage.Client(project=self.project_id, credentials=self.credentials)
        bucket = storage_client.bucket(settings.GCS_BUCKET_NAME)
        input_blob_name = f"batch-inputs/{batch_job_id}/input.jsonl"
        blob = bucket.blob(input_blob_name)

        entity_prompts = []
        for chunk in chunks:
            chunk_id = chunk.get("id")
            chunk_text = chunk.get("text", "")
            prompt = self._create_entity_extraction_prompt(chunk_text, chunk_id)
            entity_prompts.append(json.dumps(prompt))

        input_data = '\n'.join(entity_prompts)

        try:
            logger.info(f"Uploading batch input data to: {input_blob_name}")
            blob.upload_from_string(input_data, content_type="application/jsonl")
            logger.info(f"Successfully uploaded input file.")
        except Exception as e:
            logger.error(f"Failed to upload batch input file to GCS: {e}")
            raise

        return f"gs://{settings.GCS_BUCKET_NAME}/{input_blob_name}"

    def _get_batch_job(self, job_id_or_job: Union[str, aiplatform.BatchPredictionJob]) -> aiplatform.BatchPredictionJob:
        """Retrieves the BatchPredictionJob object from an ID or returns the object if provided."""
        if isinstance(job_id_or_job, str):
            try:
                logger.info(f"Getting batch prediction job with ID: {job_id_or_job}")
                job_name = self.get_batch_job_name(job_id_or_job)
                logger.info(f"Formatted job name: {job_name}")
                job = aiplatform.BatchPredictionJob(batch_prediction_job_name=job_name)
                logger.info(f"Successfully found batch job: {job.resource_name}")
                return job
            except Exception as e:
                logger.error(f"Failed to get batch prediction job with ID {job_id_or_job}: {e}")
                raise ValueError(f"Could not find batch job with ID {job_id_or_job}") from e
        elif isinstance(job_id_or_job, aiplatform.BatchPredictionJob):
            return job_id_or_job
        else:
            raise TypeError("job_id_or_job must be a string or BatchPredictionJob object")

    def get_batch_results(self, job_id_or_job, chunks=None) -> List[Dict[str, Any]]:
        """
        Get the results of a batch prediction job.

        Args:
            job_id_or_job: Either a job ID string or a BatchPredictionJob object
            chunks: Optional list of chunks (not used in this implementation, but kept for compatibility)

        Returns:
            List[Dict[str, Any]]: List of processed results with entities and relationships
        """
        try:
            job = self._get_batch_job(job_id_or_job)
        except (ValueError, TypeError) as e:
            logger.error(f"Error getting batch job: {e}")
            return []

        output_uri = self._get_job_output_uri(job)
        if not output_uri:
            logger.error("Could not determine output URI from job")
            return []

        all_predictions = self._download_prediction_files(output_uri)
        if not all_predictions:
            logger.error(f"No valid predictions found in {output_uri}")
            return []

        # Process the predictions using the existing helper method
        processed_results = self._process_raw_predictions(all_predictions)
        return processed_results

    def _download_prediction_files(self, output_uri: str) -> List[Dict[str, Any]]:
        """Download and parse prediction files from GCS."""
        all_predictions = []
        try:
            storage_client = storage.Client(project=self.project_id, credentials=self.credentials)

            if not output_uri.startswith("gs://"):
                logger.error(f"Invalid GCS URI format: {output_uri}")
                return []
            parts = output_uri[5:].split('/', 1)
            if len(parts) < 2:
                logger.error(f"Could not parse bucket and prefix from GCS URI: {output_uri}")
                return []
            bucket_name = parts[0]
            prefix = parts[1] if len(parts) > 1 else ""

            bucket = storage_client.bucket(bucket_name)
            blobs = list(bucket.list_blobs(prefix=prefix))

            prediction_files = [blob for blob in blobs if blob.name.endswith('.jsonl') and not blob.name.endswith('/')]

            if not prediction_files:
                logger.warning(f"No prediction files found in {output_uri}")
                return []

            logger.info(f"Found {len(prediction_files)} prediction files in {output_uri}")

            for blob in prediction_files:
                try:
                    content = blob.download_as_text()
                    logger.info(f"Downloaded prediction file: {blob.name}, size: {len(content)} bytes")

                    for line_num, line in enumerate(content.strip().split('\n')):
                        if not line:
                            continue

                        try:
                            prediction = json.loads(line)
                            all_predictions.append(prediction)
                        except json.JSONDecodeError as e:
                            logger.warning(f"Error parsing prediction line {line_num+1} in {blob.name} as JSON: {e}. Line content: '{line[:100]}...'")
                            continue
                except Exception as e:
                    logger.error(f"Error processing prediction file {blob.name}: {e}")
                    continue

            logger.info(f"Successfully downloaded and parsed {len(all_predictions)} predictions from {len(prediction_files)} files.")
            return all_predictions
        except Exception as e:
            logger.error(f"Error downloading or parsing prediction files from {output_uri}: {e}")
            return []

    def _process_parsed_prediction(self, response_data: Dict[str, Any], chunk_id: str) -> Dict[str, List]:
        """Processes parsed prediction data into standardized entities and relationships."""
        entities = []
        relationships = []

        for entity in response_data.get('entities', []):
            entity_text = entity.get('text', '').strip()
            if entity_text:
                # Get entity type and normalize it
                entity_type = entity.get('type', 'unknown').strip()
                normalized_type = normalize_entity_type(entity_type)

                entities.append({
                    "text": entity_text,
                    "type": normalized_type,
                    "significance": entity.get('significance', '').strip(),
                    "id": str(uuid.uuid4())
                })

        for rel in response_data.get('relationships', []):
            source_text = rel.get('source_text', '').strip()
            target_text = rel.get('target_text', '').strip()
            rel_type = rel.get('type', '').strip()
            evidence_text = rel.get('evidence_text', '').strip()

            if source_text and target_text and rel_type:
                relationships.append({
                    "source_text": source_text,
                    "target_text": target_text,
                    "type": rel_type,
                    "evidence_text": evidence_text,
                    "id": str(uuid.uuid4())
                })

        return {'entities': entities, 'relationships': relationships}

    def download_batch_prediction_results(self, batch_job_id: str) -> Dict[str, Any]:
        """
        Download and parse the results of a batch prediction job directly from GCS.
        This is useful for testing and debugging.

        Args:
            batch_job_id: The ID or full resource name of the batch prediction job

        Returns:
            Dict[str, Any]: The parsed prediction results
        """
        try:
            job = self._get_batch_job(batch_job_id)

            success, error_message = self._check_job_state(job)
            if not success:
                logger.warning(f"Batch prediction job {job.resource_name} did not succeed: {error_message}")
                return {"status": "error", "message": f"Job state: {error_message}", "job_id": job.resource_name}

            output_uri = self._get_job_output_uri(job)
            if not output_uri:
                logger.error(f"Could not determine output URI from job {job.resource_name}")
                return {"status": "error", "message": "Could not determine output URI", "job_id": job.resource_name}

            logger.info(f"Output URI for job {job.resource_name}: {output_uri}")

            all_predictions = self._download_prediction_files(output_uri)
            if not all_predictions:
                return {"status": "warning", "message": f"No valid predictions found or downloaded from {output_uri}", "job_id": job.resource_name, "output_uri": output_uri}

            processed_results = self._process_raw_predictions(all_predictions)

            return {
                "status": "success",
                "job_id": job.resource_name,
                "output_uri": output_uri,
                "raw_predictions": all_predictions,
                "processed_results": processed_results,
                "prediction_count": len(all_predictions)
            }
        except (ValueError, TypeError) as e:
            logger.error(f"Error getting batch job {batch_job_id}: {e}")
            return {"status": "error", "message": str(e), "job_id": batch_job_id}
        except Exception as e:
            logger.error(f"Error downloading batch prediction results for job {batch_job_id}: {e}", exc_info=True)
            return {"status": "error", "message": str(e), "job_id": batch_job_id}

    def _check_job_state(self, job_id_or_job: Union[str, aiplatform.BatchPredictionJob]) -> Tuple[bool, str]:
        """
        Check the state of a batch prediction job.

        Args:
            job_id_or_job: Either a job ID string or a BatchPredictionJob object

        Returns:
            Tuple[bool, str]: (success, error_message)
                - success: True if the job completed successfully, False otherwise
                - error_message: Error message if the job failed, empty string if successful
        """
        try:
            job = self._get_batch_job(job_id_or_job)

            # Check job state
            if job.state == gca_job_state.JobState.JOB_STATE_SUCCEEDED:
                logger.info(f"Batch job {job.resource_name} completed successfully")
                return True, ""
            elif job.state in [
                gca_job_state.JobState.JOB_STATE_FAILED,
                gca_job_state.JobState.JOB_STATE_CANCELLED
            ]:
                error_msg = f"Batch job failed with state: {job.state}"
                logger.error(error_msg)
                return False, error_msg
            else:
                error_msg = f"Batch job is not complete. Current state: {job.state}"
                logger.info(error_msg)
                return False, error_msg
        except Exception as e:
            error_msg = f"Error checking job state: {e}"
            logger.error(error_msg)
            return False, error_msg

    def get_batch_job_state(self, job_id: str) -> gca_job_state.JobState:
        """
        Get the state of a batch prediction job.

        Args:
            job_id: The ID of the batch prediction job

        Returns:
            JobState: The state of the batch prediction job
        """
        try:
            job = self._get_batch_job(job_id)
            logger.info(f"Batch job {job.resource_name} state: {job.state}")
            return job.state
        except Exception as e:
            logger.error(f"Error getting batch job state: {e}")
            raise

    def _get_job_output_uri(self, job: aiplatform.BatchPredictionJob) -> Optional[str]:
        """
        Get the output URI for a batch prediction job.

        Args:
            job: The BatchPredictionJob object

        Returns:
            Optional[str]: The output URI or None if not available
        """
        try:
            # Get the output URI from the job
            output_uri = job.output_info.gcs_output_directory
            if output_uri:
                logger.info(f"Found output URI in job: {output_uri}")
                return output_uri
            else:
                logger.warning(f"No output URI found in job {job.resource_name}")
                return None
        except Exception as e:
            logger.error(f"Error getting output URI from job {job.resource_name}: {e}")
            return None

    def _extract_prediction_text(self, prediction: Dict[str, Any]) -> str:
        """
        Extract prediction text from a prediction object.

        Args:
            prediction: The prediction object from Vertex AI

        Returns:
            str: The extracted prediction text
        """
        try:
            # Extract response text from the API response format
            response = prediction.get('response', {})
            candidates = response.get('candidates', [])

            if not candidates:
                logger.warning("No candidates found in prediction response")
                return ""

            content = candidates[0].get('content', {})
            parts = content.get('parts', [])

            if not parts:
                logger.warning("No parts found in prediction response content")
                return ""

            text = parts[0].get('text', '')

            # Process the text through _parse_json_response to handle markdown code blocks
            # but we only want the text, not the parsed JSON
            if text.startswith('```json') and text.endswith('```'):
                return text[7:-3].strip()

            return text
        except Exception as e:
            logger.error(f"Error extracting prediction text: {e}")
            return ""

    def _parse_json_response(self, text: str) -> Dict[str, Any]:
        """
        Parse JSON response from prediction text.

        Args:
            text: The prediction text

        Returns:
            Dict[str, Any]: The parsed JSON data
        """
        try:
            # Parse the JSON (markdown code blocks are already handled in _extract_prediction_text)
            return json.loads(text)
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing JSON: {e}")
            return {}
        except Exception as e:
            logger.error(f"Unexpected error parsing JSON: {e}")
            return {}

    def _extract_chunk_id_from_prediction(self, prediction: Dict[str, Any], response_data: Dict[str, Any]) -> Optional[str]:
        """
        Extract chunk ID from prediction data.

        Args:
            prediction: The prediction object
            response_data: The parsed response data

        Returns:
            Optional[str]: The extracted chunk ID or None if not found
        """
        # First try to get chunk_id from the response data
        chunk_id = response_data.get('chunk_id', '')
        if chunk_id:
            return chunk_id

        # If not found, try to extract from the request
        try:
            request = prediction.get('request', {})
            contents = request.get('contents', [])
            if contents and 'parts' in contents[0]:
                request_parts = contents[0]['parts']
                if request_parts:
                    request_text = request_parts[0].get('text', '')
                    # Extract chunk_id from the request text using multiple patterns
                    chunk_id_patterns = [
                        r'"chunk_id":\s*"([^"]+)"',  # JSON format
                        r'chunk_id:\s*"([^"]+)"',    # YAML-like format
                        r'Chunk ID:\s*([\w-]+)',     # Plain text format
                        r'chunk ID:\s*([\w-]+)'      # Case insensitive
                    ]

                    for pattern in chunk_id_patterns:
                        chunk_id_match = re.search(pattern, request_text, re.IGNORECASE)
                        if chunk_id_match:
                            return chunk_id_match.group(1)
        except Exception as e:
            logger.error(f"Error extracting chunk ID from prediction: {e}")

        return None

    def wait_for_batch_job(self, job_id_or_job: Union[str, aiplatform.BatchPredictionJob], timeout_seconds: int = 3600) -> bool:
        """
        Wait for a batch prediction job to complete.

        Args:
            job_id_or_job: Either a job ID string or a BatchPredictionJob object
            timeout_seconds: Maximum time to wait in seconds

        Returns:
            bool: True if the job completed successfully, False otherwise
        """
        try:
            job = self._get_batch_job(job_id_or_job)
            logger.info(f"Waiting for batch job {job.resource_name} to complete (timeout: {timeout_seconds}s)")

            # Wait for the job to complete
            job.wait(timeout=timeout_seconds)

            # Use the existing _check_job_state method to check the job state
            success, _ = self._check_job_state(job)
            return success
        except Exception as e:
            logger.error(f"Error waiting for batch job: {e}")
            return False

    def cleanup_batch_input(self, job_id_or_job: Union[str, aiplatform.BatchPredictionJob]) -> bool:
        """
        Clean up temporary input files for a batch job.

        Args:
            job_id_or_job: Either a job ID string or a BatchPredictionJob object

        Returns:
            bool: True if cleanup was successful, False otherwise
        """
        try:
            job = self._get_batch_job(job_id_or_job)

            # Check if the job has a temporary input URI
            if hasattr(job, '_temp_input_gcs_uri') and job._temp_input_gcs_uri:
                logger.info(f"Cleaning up temporary input file: {job._temp_input_gcs_uri}")

                # Parse the GCS URI
                if not job._temp_input_gcs_uri.startswith("gs://"):
                    logger.error(f"Invalid GCS URI format: {job._temp_input_gcs_uri}")
                    return False

                parts = job._temp_input_gcs_uri[5:].split('/', 1)
                if len(parts) < 2:
                    logger.error(f"Could not parse bucket and blob from GCS URI: {job._temp_input_gcs_uri}")
                    return False

                bucket_name = parts[0]
                blob_name = parts[1]

                # Delete the blob
                storage_client = storage.Client(project=self.project_id, credentials=self.credentials)
                bucket = storage_client.bucket(bucket_name)
                blob = bucket.blob(blob_name)

                if blob.exists():
                    blob.delete()
                    logger.info(f"Successfully deleted temporary input file: {job._temp_input_gcs_uri}")
                    return True
                else:
                    logger.warning(f"Temporary input file does not exist: {job._temp_input_gcs_uri}")
                    return True  # Consider it a success if the file doesn't exist
            else:
                logger.info("No temporary input file to clean up")
                return True
        except Exception as e:
            logger.error(f"Error cleaning up temporary input file: {e}")
            return False

    def _process_raw_predictions(self, predictions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Process raw predictions from Vertex AI to extract entities and relationships.
        Uses helper methods for parsing and processing.

        Args:
            predictions: List of raw prediction objects from Vertex AI

        Returns:
            List[Dict[str, Any]]: Processed results with entities and relationships
        """
        processed_results = []
        processed_chunk_ids = set()

        for i, prediction in enumerate(predictions):
            if 'request' in prediction and 'response' in prediction:
                # Extract and process the prediction text
                prediction_text = self._extract_prediction_text(prediction)
                if not prediction_text:
                    logger.warning(f"Prediction {i+1}: No text found in response.")
                    continue

                # Parse the JSON response
                response_data = self._parse_json_response(prediction_text)

                # Extract and validate the chunk ID
                chunk_id = self._extract_chunk_id_from_prediction(prediction, response_data)
                if not chunk_id:
                    logger.warning(f"Prediction {i+1}: Could not extract chunk ID, skipping.")
                    continue

                try:
                    chunk_id = validate_id_with_context(chunk_id, f"Prediction {i+1}", logger, error_on_invalid=True)
                except ValueError as e:
                    logger.warning(f"Prediction {i+1}: Invalid chunk ID '{chunk_id}': {e}. Skipping.")
                    continue

                # Skip duplicate chunk IDs
                if chunk_id in processed_chunk_ids:
                    logger.warning(f"Prediction {i+1}: Duplicate chunk ID '{chunk_id}' found. Skipping.")
                    continue
                processed_chunk_ids.add(chunk_id)

                # Process the prediction data
                processed_data = self._process_parsed_prediction(response_data, chunk_id)

                # Create the result entry
                result_entry = {
                    "chunk_id": chunk_id,
                    "entities": processed_data['entities'],
                    "relationships": processed_data['relationships'],
                    "entity_count": len(processed_data['entities']),
                    "relationship_count": len(processed_data['relationships'])
                }

                # Add error and message if present
                if 'error' in response_data and response_data['error']:
                    result_entry['error'] = response_data['error']
                if 'message' in response_data and response_data['message']:
                    result_entry['message'] = response_data['message']

                processed_results.append(result_entry)
            else:
                logger.warning(f"Prediction {i+1}: Does not match expected request/response format. Skipping.")

        logger.info(f"Processed {len(processed_results)} predictions out of {len(predictions)} raw predictions.")
        return processed_results

    def process_pending_batch_jobs(self, max_tasks: int = 10) -> Dict[str, Any]:
        """
        Process pending vertex_ai_batch_job tasks by checking their status and updating them.
        
        This function:
        1. Queries for vertex_ai_batch_job tasks in 'created' status
        2. Checks if the batch job has completed
        3. Updates the task with results or error information
        
        Args:
            max_tasks: Maximum number of tasks to process at once
            
        Returns:
            Dict[str, Any]: Summary of processed tasks
        """
        from transport.data_transport import DataTransport
        from common.database import ProcessingTask

        logger.info(f"Processing pending vertex_ai_batch_jobs (max: {max_tasks})")
        
        # Track statistics
        processed_count = 0
        completed_count = 0
        failed_count = 0
        
        try:
            with DataTransport() as transport:
                # Query for tasks with type "vertex_ai_batch_job" in "created" status
                batch_tasks = transport.db_client.db.query(ProcessingTask).filter(
                    ProcessingTask.task_type == "vertex_ai_batch_job",
                    ProcessingTask.status == "created",
                    ProcessingTask.task_metadata.contains({"batch_job_id": None}).is_(False)
                ).limit(max_tasks).all()
                
                logger.info(f"Found {len(batch_tasks)} pending vertex_ai_batch_job tasks")
                
                # Process each task
                for task in batch_tasks:
                    task_id = task.id
                    batch_job_id = task.task_metadata.get("batch_job_id")
                    
                    if not batch_job_id:
                        logger.warning(f"No batch job ID found in task {task_id} metadata")
                        continue
                    
                    logger.info(f"Processing vertex_ai_batch_job task {task_id} with batch job {batch_job_id}")
                    processed_count += 1
                    
                    try:
                        # Get the batch job state
                        job_state = self.get_batch_job_state(batch_job_id)
                        logger.info(f"Batch job {batch_job_id} state: {job_state}")
                        
                        # Import the JobState enum for comparison
                        from google.cloud.aiplatform.compat.types import job_state as gca_job_state
                        
                        if job_state == gca_job_state.JobState.JOB_STATE_SUCCEEDED:
                            logger.info(f"Batch job {batch_job_id} completed successfully")
                            
                            # Download the results
                            results = self.download_batch_prediction_results(batch_job_id)
                            
                            if results["status"] == "success":
                                logger.info(f"Successfully downloaded results for batch job {batch_job_id}")
                                
                                # Update task status with results information
                                transport.update_task_status(
                                    task_id=task_id,
                                    status="completed",
                                    result={
                                        "status": "completed",
                                        "batch_job_id": batch_job_id,
                                        "completed_at": time.time(),
                                        "output_uri": results.get("output_uri"),
                                        "prediction_count": results.get("prediction_count", 0),
                                        "processed_results": results.get("processed_results", [])
                                    }
                                )
                                completed_count += 1
                            else:
                                logger.error(f"Error downloading results for batch job {batch_job_id}: {results.get('message')}")
                                transport.update_task_status(
                                    task_id=task_id,
                                    status="failed",
                                    result={
                                        "status": "failed",
                                        "batch_job_id": batch_job_id,
                                        "completed_at": time.time(),
                                        "error": results.get("message", "Unknown error downloading batch job results")
                                    }
                                )
                                failed_count += 1
                        
                        elif job_state in [gca_job_state.JobState.JOB_STATE_FAILED, gca_job_state.JobState.JOB_STATE_CANCELLED]:
                            logger.error(f"Batch job {batch_job_id} failed with state: {job_state}")
                            
                            # Update task status to reflect failure
                            transport.update_task_status(
                                task_id=task_id,
                                status="failed",
                                result={
                                    "status": "failed",
                                    "error": f"Batch job failed with state: {job_state}",
                                    "failed_at": time.time()
                                }
                            )
                            failed_count += 1
                        
                        # For jobs still in progress, we don't update the status
                    
                    except Exception as e:
                        logger.error(f"Error processing batch job {batch_job_id} for task {task_id}: {e}", exc_info=True)
                        
                        # Update task to reflect error
                        try:
                            transport.update_task_status(
                                task_id=task_id,
                                status="failed",
                                result={
                                    "status": "failed",
                                    "error": f"Error processing batch job: {str(e)}",
                                    "failed_at": time.time()
                                }
                            )
                            failed_count += 1
                        except Exception as update_error:
                            logger.error(f"Error updating task status: {update_error}")
            
            return {
                "status": "success", 
                "processed": processed_count,
                "completed": completed_count,
                "failed": failed_count
            }
        
        except Exception as e:
            logger.error(f"Error in process_pending_batch_jobs: {e}", exc_info=True)
            return {
                "status": "error",
                "message": str(e),
                "processed": processed_count,
                "completed": completed_count,
                "failed": failed_count
            }
