"""
Neo4j client for interacting with Neo4j graph database.
"""
import logging
import uuid
from typing import List, Dict, Any, Optional, Union, Tuple

from neo4j import GraphDatabase
from neo4j.exceptions import Neo4jError

from common.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class Neo4jClient:
    """Client for interacting with Neo4j graph database."""

    def __init__(self):
        """Initialize Neo4j client with settings."""
        self._driver = None
        self._initialize_driver()

    def _initialize_driver(self):
        """Initialize Neo4j driver."""
        try:
            uri = f"neo4j://{settings.NEO4J_HOST}:{settings.NEO4J_PORT}"
            self._driver = GraphDatabase.driver(
                uri,
                auth=(settings.NEO4J_USER, settings.NEO4J_PASSWORD)
            )
            # Test connection
            with self._driver.session() as session:
                result = session.run("RETURN 1 AS test")
                test_value = result.single()["test"]
                if test_value != 1:
                    raise Exception("Neo4j connection test failed")
            logger.info(f"Connected to Neo4j at {uri}")
        except Exception as e:
            logger.error(f"Failed to connect to Neo4j: {e}")
            raise

    def close(self):
        """Close Neo4j driver."""
        if self._driver:
            self._driver.close()
            logger.info("Neo4j connection closed")

    def create_constraints(self):
        """Create necessary constraints in Neo4j."""
        constraints = [
            "CREATE CONSTRAINT entity_id IF NOT EXISTS FOR (e:Entity) REQUIRE e.id IS UNIQUE",
            "CREATE CONSTRAINT document_id IF NOT EXISTS FOR (d:Document) REQUIRE d.id IS UNIQUE",
            "CREATE CONSTRAINT chunk_id IF NOT EXISTS FOR (c:Chunk) REQUIRE c.id IS UNIQUE"
        ]

        with self._driver.session() as session:
            for constraint in constraints:
                try:
                    session.run(constraint)
                    logger.info(f"Created constraint: {constraint}")
                except Neo4jError as e:
                    logger.error(f"Error creating constraint: {e}")

    def store_document(self, document_id: str, metadata: Dict[str, Any]) -> Dict[str, Any]:
        """
        Store document node in Neo4j.

        Args:
            document_id: Document ID
            metadata: Document metadata

        Returns:
            Dict[str, Any]: Operation result
        """
        query = """
        MERGE (d:Document {id: $document_id})
        SET d += $properties
        RETURN d.id as id
        """

        properties = {
            "filename": metadata.get("filename", ""),
            "content_type": metadata.get("content_type", ""),
            "created_at": metadata.get("created_at", ""),
            "updated_at": metadata.get("updated_at", "")
        }

        with self._driver.session() as session:
            try:
                result = session.run(query, document_id=document_id, properties=properties)
                record = result.single()
                return {"id": record["id"], "status": "success"}
            except Neo4jError as e:
                logger.error(f"Error storing document in Neo4j: {e}")
                return {"id": document_id, "status": "error", "error": str(e)}

    def store_chunk(self, chunk_id: str, document_id: str, chunk_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Store chunk node in Neo4j.

        Args:
            chunk_id: Chunk ID
            document_id: Document ID
            chunk_data: Chunk data

        Returns:
            Dict[str, Any]: Operation result
        """
        # Create chunk and link to document (assumes document exists)
        query = """
        MATCH (d:Document {id: $document_id})
        MERGE (c:Chunk {id: $chunk_id})
        SET c += $properties
        MERGE (c)-[:PART_OF]->(d)
        RETURN c.id as id
        """

        # Get embedding but don't include it in logs
        embedding = chunk_data.get("embedding", None)
        
        # For logging purposes, create a copy without the actual embedding vector
        log_properties = {
            "text": chunk_data.get("text", ""),
            "chunk_index": chunk_data.get("chunk_index", 0),
            "created_at": chunk_data.get("created_at", ""),
            "embedding_present": embedding is not None,
            "document_id": document_id  # Store document_id as property for easier fixing if relationship breaks
        }
        
        # Actual properties to store in Neo4j
        properties = {
            "text": chunk_data.get("text", ""),
            "chunk_index": chunk_data.get("chunk_index", 0),
            "created_at": chunk_data.get("created_at", ""),
            "embedding": embedding,
            "document_id": document_id  # Store document_id as property for easier fixing if relationship breaks
        }

        with self._driver.session() as session:
            try:
                # Log the properties without the embedding vector
                logger.info(f"Creating chunk {chunk_id} with properties: {log_properties}")
                
                result = session.run(
                    query,
                    chunk_id=chunk_id,
                    document_id=document_id,
                    properties=properties
                )
                record = result.single()

                if record:
                    return {"id": record["id"], "status": "success"}
                else:
                    logger.error(f"Failed to create chunk {chunk_id} or link to document {document_id}")
                    return {"id": chunk_id, "status": "error", "error": "Failed to create chunk or link to document"}
            except Neo4jError as e:
                logger.error(f"Error storing chunk in Neo4j: {e}")
                return {"id": chunk_id, "status": "error", "error": str(e)}

    def create_chunk_relationships(self, document_id: str, chunks: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Create relationships between chunks in the same document.

        Args:
            document_id: Document ID
            chunks: List of chunks with IDs and indices

        Returns:
            Dict[str, Any]: Operation result
        """
        # First create NEXT relationships between sequential chunks
        sequential_query = """
        MATCH (d:Document {id: $document_id})
        MATCH (c1:Chunk {id: $chunk1_id})-[:PART_OF]->(d)
        MATCH (c2:Chunk {id: $chunk2_id})-[:PART_OF]->(d)
        MERGE (c1)-[r:NEXT]->(c2)
        RETURN c1.id as source_id, c2.id as target_id
        """

        # Sort chunks by index
        sorted_chunks = sorted(chunks, key=lambda x: x.get("chunk_index", 0))

        sequential_relationships = 0

        with self._driver.session() as session:
            try:
                # Create NEXT relationships between sequential chunks
                for i in range(len(sorted_chunks) - 1):
                    chunk1 = sorted_chunks[i]
                    chunk2 = sorted_chunks[i + 1]

                    # Log the chunks being connected
                    logger.info(f"Creating NEXT relationship: Chunk {chunk1.get('chunk_index')} -> Chunk {chunk2.get('chunk_index')}")

                    result = session.run(
                        sequential_query,
                        document_id=document_id,
                        chunk1_id=chunk1.get("id"),
                        chunk2_id=chunk2.get("id")
                    )

                    record = result.single()
                    if record:
                        sequential_relationships += 1
                        logger.info(f"Created relationship: {record['source_id']} -> {record['target_id']}")

                logger.info(f"Created {sequential_relationships} sequential relationships between chunks for document {document_id}")

                # Also create FOLLOWED_BY relationship type which is more descriptive
                followed_by_query = """
                MATCH (d:Document {id: $document_id})
                MATCH (c1:Chunk {id: $chunk1_id})-[:PART_OF]->(d)
                MATCH (c2:Chunk {id: $chunk2_id})-[:PART_OF]->(d)
                MERGE (c1)-[r:FOLLOWED_BY]->(c2)
                SET r.document_id = $document_id
                RETURN c1.id as source_id, c2.id as target_id
                """

                followed_by_relationships = 0

                # Create FOLLOWED_BY relationships between sequential chunks
                for i in range(len(sorted_chunks) - 1):
                    chunk1 = sorted_chunks[i]
                    chunk2 = sorted_chunks[i + 1]

                    result = session.run(
                        followed_by_query,
                        document_id=document_id,
                        chunk1_id=chunk1.get("id"),
                        chunk2_id=chunk2.get("id")
                    )

                    if result.single():
                        followed_by_relationships += 1

                logger.info(f"Created {followed_by_relationships} FOLLOWED_BY relationships between chunks")

                return {
                    "document_id": document_id,
                    "sequential_relationships": sequential_relationships,
                    "followed_by_relationships": followed_by_relationships,
                    "status": "success"
                }

            except Neo4jError as e:
                logger.error(f"Error creating chunk relationships in Neo4j: {e}")
                return {
                    "document_id": document_id,
                    "status": "error",
                    "error": str(e)
                }

    def store_entities(self, chunk_id: str, entities: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Store entities in Neo4j.

        Args:
            chunk_id: Chunk ID
            entities: List of entity objects

        Returns:
            Dict[str, Any]: Operation result
        """
        query = """
        MATCH (c:Chunk {id: $chunk_id})
        UNWIND $entities AS entity
        MERGE (e:Entity {id: entity.id})
        SET e += entity.properties
        MERGE (e)-[r:MENTIONED_IN]->(c)
        SET r.start = entity.start, r.end = entity.end
        RETURN count(e) AS entity_count
        """

        entity_data = []
        for entity in entities:
            entity_id = entity.get("id", str(uuid.uuid4()))

            # Use standardized field names
            entity_type = entity.get("type", "")
            entity_text = entity.get("text", "")
            start_pos = entity.get("start", 0)
            end_pos = entity.get("end", 0)
            normalized_id = entity.get("normalized_id", None)
            normalized_text = entity.get("normalized_text", entity_text)
            is_duplicate = entity.get("is_duplicate", False)

            # Get global ID if available, otherwise use entity ID
            global_id = entity.get("global_id", entity_id)

            # Get mapped_from if available (for tracking original entity ID)
            mapped_from = entity.get("mapped_from", None)

            # Build properties dictionary
            properties = {
                "text": entity_text,
                "type": entity_type,
                "normalized_id": normalized_id,
                "normalized_text": normalized_text,
                "is_duplicate": is_duplicate,
                "global_id": global_id
            }

            # Add mapped_from if available
            if mapped_from:
                properties["mapped_from"] = mapped_from

            entity_data.append({
                "id": entity_id,
                "properties": properties,
                "start": start_pos,
                "end": end_pos
            })

        with self._driver.session() as session:
            try:
                result = session.run(query, chunk_id=chunk_id, entities=entity_data)
                record = result.single()
                return {
                    "chunk_id": chunk_id,
                    "entity_count": record["entity_count"],
                    "status": "success"
                }
            except Neo4jError as e:
                logger.error(f"Error storing entities in Neo4j: {e}")
                return {
                    "chunk_id": chunk_id,
                    "status": "error",
                    "error": str(e)
                }

    def store_relationships(self, chunk_id: str, relationships: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Store relationships in Neo4j. If entities don't exist, create them.

        Args:
            chunk_id: Chunk ID
            relationships: List of relationship objects

        Returns:
            Dict[str, Any]: Operation result
        """
        logger.info(f"Storing {len(relationships)} relationships for chunk {chunk_id}")
        if relationships:
            sample_rel = relationships[0]
            logger.info(f"Sample relationship: {sample_rel.get('source_text', '')} ({sample_rel.get('source_id', '')}) --[{sample_rel.get('type', '')}]--> {sample_rel.get('target_text', '')} ({sample_rel.get('target_id', '')})")
        
        # Create a query that creates entities if missing and then creates the relationship
        create_query = """
        UNWIND $relationships AS rel
        // Create or find source entity
        MERGE (source:Entity {id: rel.source_id})
        ON CREATE SET 
            source.text = rel.source_text,
            source.type = COALESCE(rel.source_type, 'unknown'),
            source.created_at = timestamp(),
            source.auto_created = true
        
        // Create or find target entity
        MERGE (target:Entity {id: rel.target_id})
        ON CREATE SET 
            target.text = rel.target_text,
            target.type = COALESCE(rel.target_type, 'unknown'),
            target.created_at = timestamp(),
            target.auto_created = true
        
        // Create dynamic relationship based on type
        CALL apoc.create.relationship(source, rel.type, {
            id: rel.id,
            confidence: rel.confidence,
            evidence_text: rel.evidence_text,
            chunk_id: $chunk_id,
            created_at: timestamp()
        }, target) YIELD rel AS created_rel
        
        RETURN count(created_rel) AS relationship_count
        """

        # Fallback query if APOC is not available
        fallback_query = """
        UNWIND $relationships AS rel
        // Create or find source entity
        MERGE (source:Entity {id: rel.source_id})
        ON CREATE SET 
            source.text = rel.source_text,
            source.type = COALESCE(rel.source_type, 'unknown'),
            source.created_at = timestamp(),
            source.auto_created = true
        
        // Create or find target entity
        MERGE (target:Entity {id: rel.target_id})
        ON CREATE SET 
            target.text = rel.target_text,
            target.type = COALESCE(rel.target_type, 'unknown'),
            target.created_at = timestamp(),
            target.auto_created = true
        
        // Create relationship (static type for fallback)
        MERGE (source)-[r:RELATED_TO]->(target)
        SET r.id = rel.id,
            r.type = rel.type,
            r.confidence = rel.confidence,
            r.evidence_text = rel.evidence_text,
            r.chunk_id = $chunk_id,
            r.created_at = timestamp()
        
        RETURN count(r) AS relationship_count
        """
        
        # Filter and prepare relationship data
        skipped_relationship_count = 0
        relationship_data = []
        
        for rel in relationships:
            rel_id = rel.get("id", str(uuid.uuid4()))
            
            # Use standardized field names
            rel_type = rel.get("relationship_type", rel.get("type", "RELATED_TO")).upper().replace(" ", "_")
            confidence = rel.get("confidence", 0.0)
            evidence_text = rel.get("evidence_text", "")

            # Make sure we have source and target IDs
            source_id = rel.get("source_id")
            target_id = rel.get("target_id")

            if not source_id or not target_id:
                skipped_relationship_count += 1
                logger.warning(f"Skipping relationship without source_id or target_id: {rel}")
                continue

            # Extract source and target text for entity creation if needed
            source_text = rel.get("source_text", "")
            target_text = rel.get("target_text", "")
            source_type = rel.get("source_type", "unknown")
            target_type = rel.get("target_type", "unknown")
            
            if not source_text or not target_text:
                # If text is missing but we have IDs, we'll create placeholder entities
                logger.warning(f"Relationship missing text for source or target, using placeholder values: {rel}")
                if not source_text:
                    source_text = f"Entity_{source_id}"
                if not target_text:
                    target_text = f"Entity_{target_id}"

            relationship_data.append({
                "id": rel_id,
                "source_id": source_id,
                "target_id": target_id,
                "source_text": source_text,
                "target_text": target_text,
                "source_type": source_type,
                "target_type": target_type,
                "type": rel_type,
                "confidence": confidence,
                "evidence_text": evidence_text
            })
        
        # If no valid relationships to create, return early
        if not relationship_data:
            logger.warning(f"No valid relationships to create for chunk {chunk_id}")
            return {
                "chunk_id": chunk_id,
                "relationship_count": 0,
                "skipped_count": skipped_relationship_count,
                "status": "warning",
                "message": "No valid relationships to create"
            }
        
        with self._driver.session() as session:
            try:
                logger.info(f"Creating {len(relationship_data)} relationships with auto-entity creation for chunk {chunk_id}")
                
                # Try using the APOC relationship creation first
                try:
                    result = session.run(
                        create_query,
                        chunk_id=chunk_id,
                        relationships=relationship_data
                    )
                    record = result.single()
                    created_count = record["relationship_count"] if record else 0
                    
                    if created_count == 0:
                        # If APOC fails, try the fallback approach
                        logger.warning("APOC relationship creation returned zero results, trying fallback method")
                        result = session.run(
                            fallback_query,
                            chunk_id=chunk_id,
                            relationships=relationship_data
                        )
                        record = result.single()
                        created_count = record["relationship_count"] if record else 0
                        
                except Exception as e:
                    # APOC might not be available, use fallback
                    logger.warning(f"Error using APOC: {e}. Falling back to standard Cypher.")
                    result = session.run(
                        fallback_query, 
                        chunk_id=chunk_id,
                        relationships=relationship_data
                    )
                    record = result.single()
                    created_count = record["relationship_count"] if record else 0
                
                logger.info(f"Successfully created {created_count} relationships for chunk {chunk_id}")
                
                # Now verify that the relationships were actually created
                verification_query = """
                MATCH (source:Entity)-[r]->(target:Entity)
                WHERE r.chunk_id = $chunk_id
                RETURN count(r) AS actual_count
                """
                
                verify_result = session.run(verification_query, chunk_id=chunk_id)
                verify_record = verify_result.single()
                actual_count = verify_record["actual_count"] if verify_record else 0
                
                logger.info(f"Verification: Found {actual_count} relationships with chunk_id={chunk_id} in Neo4j")
                
                return {
                    "chunk_id": chunk_id,
                    "relationship_count": created_count,
                    "verified_count": actual_count,
                    "skipped_count": skipped_relationship_count,
                    "status": "success" if created_count > 0 else "warning"
                }
            except Exception as e:
                logger.error(f"Error storing relationships in Neo4j: {e}")
                logger.error(f"First erroring relationship: {relationship_data[0] if relationship_data else None}")
                return {
                    "chunk_id": chunk_id,
                    "status": "error",
                    "error": str(e),
                    "skipped_count": skipped_relationship_count
                }