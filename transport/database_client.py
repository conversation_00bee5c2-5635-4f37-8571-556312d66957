"""
Database client for interacting with PostgreSQL.
"""
import uuid
import json
from typing import Any, Dict, List, Optional, Union, Tuple
from datetime import datetime, timezone

from sqlalchemy.orm import Session
from sqlalchemy import func

from common.database import Document, Chunk, KGChunk, ProcessingTask, Entity, Relationship
from pgvector.sqlalchemy import Vector


class DatabaseClient:
    """Client for interacting with PostgreSQL database."""

    def __init__(self, db_session: Session):
        """
        Initialize database client with session.

        Args:
            db_session: SQLAlchemy database session
        """
        self.db = db_session

    def create_document(self,
                       filename: str,
                       source_path: str,
                       content_type: str,
                       storage_path: Optional[str] = None,
                       file_size: Optional[int] = None,
                       metadata: Optional[Dict[str, Any]] = None,
                       title: Optional[str] = None,
                       content_hash: Optional[str] = None) -> Document:
        """
        Create a new document record.

        Args:
            filename: Original filename
            source_path: Original path/URL of the document
            content_type: MIME type of the document
            storage_path: Path in storage where the document is stored
            file_size: Size of the file in bytes
            metadata: Additional metadata for the document
            title: Optional document title (if available)
            content_hash: Optional SHA-256 hash of document content

        Returns:
            Document: Created document record
        """
        # Extract title from metadata if not provided directly
        if title is None and metadata and 'title' in metadata:
            title = metadata.get('title')

        document = Document(
            filename=filename,
            title=title,  # Can be None
            source_url=source_path,
            document_type=content_type,
            doc_metadata=metadata,
            content_hash=content_hash,
            status="pending"
        )

        self.db.add(document)
        self.db.commit()
        self.db.refresh(document)

        return document

    def get_document(self, document_id: Union[str, uuid.UUID]) -> Optional[Document]:
        """
        Get document by ID.

        Args:
            document_id: Document ID

        Returns:
            Optional[Document]: Document record if found
        """
        if isinstance(document_id, str):
            document_id = uuid.UUID(document_id)

        return self.db.query(Document).filter(Document.id == document_id).first()

    def get_document_by_hash(self, content_hash: str) -> Optional[Document]:
        """
        Get document by content hash.

        Args:
            content_hash: SHA-256 hash of document content

        Returns:
            Optional[Document]: Document record if found
        """
        return self.db.query(Document).filter(Document.content_hash == content_hash).first()

    def get_document_by_source(self, source_type: str, source_path: str) -> Optional[Document]:
        """
        Get document by source type and path.

        Args:
            source_type: Source type (e.g., 'pubmed', 'url')
            source_path: Source path or identifier

        Returns:
            Optional[Document]: Document record if found
        """
        # Check if the document exists with the given source type and path
        # We use source_url field to store the source path
        return self.db.query(Document).filter(
            Document.doc_metadata.has_key('source'),
            Document.doc_metadata['source'].astext == source_type,
            Document.source_url == source_path
        ).first()

    def get_all_documents(self) -> List[Document]:
        """
        Get all documents in the database.

        Returns:
            List[Document]: List of all document records
        """
        return self.db.query(Document).all()

    def delete_document(self, document_id: Union[str, uuid.UUID]) -> bool:
        """
        Delete a document and all its associated chunks.

        Args:
            document_id: Document ID

        Returns:
            bool: True if successful
        """
        if isinstance(document_id, str):
            document_id = uuid.UUID(document_id)

        # Get the document
        document = self.get_document(document_id)
        if not document:
            return False

        # Delete all chunks associated with the document
        self.db.query(Chunk).filter(Chunk.document_id == document_id).delete()
        self.db.query(KGChunk).filter(KGChunk.document_id == document_id).delete()

        # Delete the document
        self.db.delete(document)
        self.db.commit()

        # Try to delete from Neo4j as well
        try:
            from transport.neo4j_client import Neo4jClient
            neo4j_client = Neo4jClient()
            neo4j_client.delete_document(str(document_id))
            neo4j_client.close()
        except Exception as e:
            # Log the error but continue
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error deleting document from Neo4j: {e}")

        return True

    def get_chunk(self, chunk_id: Union[str, uuid.UUID], chunk_type: str = "rag") -> Optional[Union[Chunk, KGChunk]]:
        """
        Get chunk by ID.

        Args:
            chunk_id: Chunk ID
            chunk_type: Type of chunk to retrieve ("rag" or "kg")
                       Default is "rag" for retrieval-optimized chunks

        Returns:
            Optional[Union[Chunk, KGChunk]]: Chunk record if found
        """
        if isinstance(chunk_id, str):
            chunk_id = uuid.UUID(chunk_id)

        if chunk_type == "kg":
            # Get knowledge graph optimized chunk
            return self.db.query(KGChunk).filter(KGChunk.id == chunk_id).first()
        else:  # Default to RAG chunks for retrieval
            # Get retrieval-optimized chunk
            return self.db.query(Chunk).filter(Chunk.id == chunk_id).first()

    def update_document_status(self,
                              document_id: Union[str, uuid.UUID],
                              status: str,
                              storage_path: Optional[str] = None) -> Document:
        """
        Update document status.

        Args:
            document_id: Document ID
            status: New status
            storage_path: Path in storage (if updated)

        Returns:
            Document: Updated document record
        """
        if isinstance(document_id, str):
            document_id = uuid.UUID(document_id)

        document = self.db.query(Document).filter(Document.id == document_id).first()
        if not document:
            raise ValueError(f"Document with ID {document_id} not found")

        document.status = status
        document.updated_at = datetime.now(timezone.utc)

        if storage_path:
            document.storage_path = storage_path

        self.db.commit()
        self.db.refresh(document)

        return document

    def store_chunks(self, document_id: Union[str, uuid.UUID], chunks: List[Dict[str, Any]], chunk_type: str = "rag") -> List[Union[Chunk, KGChunk]]:
        """
        Store document chunks.

        Args:
            document_id: Document ID
            chunks: List of chunk objects with text, metadata, and optional embedding
            chunk_type: Type of chunks to store ("rag" or "kg")

        Returns:
            List[Union[Chunk, KGChunk]]: Created chunk records
        """
        import logging
        logger = logging.getLogger(__name__)

        if isinstance(document_id, str):
            document_id = uuid.UUID(document_id)

        # Check if document exists
        document = self.get_document(document_id)
        if not document:
            raise ValueError(f"Document with ID {document_id} not found")

        # Create chunk records
        chunk_records = []
        for i, chunk_data in enumerate(chunks):
            # Convert embedding to vector format if present
            embedding = chunk_data.get('embedding')
            embedding_vector = None

            if embedding is not None:
                embedding_vector = embedding

            # Generate custom chunk ID using document_id and chunk_index
            custom_id = f"{document_id}_{chunk_type}_{i}"
            chunk_uuid = uuid.uuid5(uuid.NAMESPACE_URL, custom_id)

            # Create chunk record based on chunk_type
            if chunk_type == "kg":
                # Create KG chunk record
                chunk = KGChunk(
                    id=chunk_uuid,  # Use the generated UUID based on document_id and index
                    document_id=document_id,
                    chunk_index=i,
                    text=chunk_data['text'],
                    chunk_metadata=chunk_data.get('metadata', {}),
                    embedding=embedding_vector
                )
            else:  # Default to RAG chunks
                # Create RAG chunk record
                chunk = Chunk(
                    id=chunk_uuid,  # Use the generated UUID based on document_id and index
                    document_id=document_id,
                    chunk_index=i,
                    text=chunk_data['text'],
                    chunk_metadata=chunk_data.get('metadata', {}),
                    embedding=embedding_vector
                )

            self.db.add(chunk)
            chunk_records.append(chunk)

        # Update document status to processed
        document.status = "processed"
        document.updated_at = datetime.now(timezone.utc)

        # Commit the transaction to PostgreSQL
        try:
            logger.info(f"Committing {len(chunk_records)} chunks to PostgreSQL for document {document_id}")
            self.db.commit()
            logger.info(f"Successfully committed chunks to PostgreSQL")

            # Refresh all records to get generated IDs
            for chunk in chunk_records:
                self.db.refresh(chunk)
        except Exception as e:
            logger.error(f"Error committing chunks to PostgreSQL: {e}")
            self.db.rollback()
            raise

        # Only store KG chunks in Neo4j
        if chunk_type == "kg":
            try:
                from transport.neo4j_client import Neo4jClient
                neo4j_client = Neo4jClient()
                logger.info(f"Storing document and KG chunks in Neo4j for document {document_id}")

                # Store document in Neo4j
                neo4j_client.store_document(str(document_id), {
                    "filename": document.filename,
                    "content_type": document.content_type,
                    "created_at": document.created_at.isoformat() if document.created_at else None,
                    "updated_at": document.updated_at.isoformat() if document.updated_at else None
                })

                # Prepare KG chunks for Neo4j
                neo4j_chunks = []
                for chunk in chunk_records:
                    # Prepare chunk data
                    chunk_data = {
                        "text": chunk.text,
                        "chunk_index": chunk.chunk_index,
                        "created_at": chunk.created_at.isoformat() if chunk.created_at else None,
                        "document_id": str(document_id)
                    }

                    # Store chunk in Neo4j (document is already stored)
                    neo4j_client.store_chunk(str(chunk.id), str(document_id), chunk_data)

                    # Add to list for creating relationships
                    neo4j_chunks.append({
                        "id": str(chunk.id),
                        "chunk_index": chunk.chunk_index
                    })

                # Create relationships between chunks
                if neo4j_chunks:
                    logger.info(f"Creating relationships between {len(neo4j_chunks)} KG chunks for document {document_id}")
                    neo4j_client.create_chunk_relationships(str(document_id), neo4j_chunks)

                # Close Neo4j client
                neo4j_client.close()
                logger.info(f"Successfully stored document and KG chunks in Neo4j")

            except Exception as e:
                logger.error(f"Error storing KG chunks in Neo4j: {e}")
                # Continue with PostgreSQL storage even if Neo4j fails
                # The chunks are already committed to PostgreSQL, so we can return them
        else:
            logger.info(f"Skipping Neo4j storage for non-KG chunks (type: {chunk_type})")

        return chunk_records

    def get_document_chunks(self, document_id: Union[str, uuid.UUID], chunk_type: str = "rag") -> List[Union[Chunk, KGChunk]]:
        """
        Get all chunks for a document.

        Args:
            document_id: Document ID
            chunk_type: Type of chunks to retrieve ("rag" or "kg")

        Returns:
            List[Union[Chunk, KGChunk]]: List of document chunks
        """
        if isinstance(document_id, str):
            document_id = uuid.UUID(document_id)

        if chunk_type == "kg":
            return self.db.query(KGChunk).filter(KGChunk.document_id == document_id).order_by(KGChunk.chunk_index).all()
        else:  # Default to RAG chunks
            return self.db.query(Chunk).filter(Chunk.document_id == document_id).order_by(Chunk.chunk_index).all()

    def create_processing_task(self,
                              document_id: Union[str, uuid.UUID],
                              task_type: str,
                              celery_task_id: Optional[str] = None,
                              metadata: Optional[Dict[str, Any]] = None,
                              id: Optional[Union[str, uuid.UUID]] = None,
                              status: Optional[str] = "pending") -> ProcessingTask:
        """
        Create a new processing task record.

        Args:
            document_id: Document ID
            task_type: Type of processing task
            celery_task_id: Celery task ID if available
            metadata: Additional metadata for the task
            id: Optional task ID (if not provided, a new UUID will be generated)
            status: Optional initial status (defaults to 'pending')

        Returns:
            ProcessingTask: Created task record
        """
        if isinstance(document_id, str):
            document_id = uuid.UUID(document_id)

        if isinstance(id, str) and id:
            id = uuid.UUID(id)

        task = ProcessingTask(
            id=id if id else uuid.uuid4(),
            document_id=document_id,
            task_type=task_type,
            celery_task_id=celery_task_id,
            status=status,
            task_metadata=metadata
        )

        self.db.add(task)
        self.db.commit()
        self.db.refresh(task)

        return task

    def update_task_status(self,
                          task_id: Union[str, uuid.UUID],
                          status: str,
                          result: Optional[Dict[str, Any]] = None,
                          error: Optional[str] = None) -> ProcessingTask:
        """
        Update task status.

        Args:
            task_id: Task ID
            status: New status
            result: Task result (for completed tasks)
            error: Error message (for failed tasks)

        Returns:
            ProcessingTask: Updated task record
        """
        if isinstance(task_id, str):
            task_id = uuid.UUID(task_id)

        task = self.db.query(ProcessingTask).filter(ProcessingTask.id == task_id).first()
        if not task:
            raise ValueError(f"Task with ID {task_id} not found")

        task.status = status
        task.updated_at = datetime.now(timezone.utc)

        if result is not None:
            task.result = result

        if error is not None:
            task.error = error

        self.db.commit()
        self.db.refresh(task)

        return task

    def search_similar(self, embedding: List[float], limit: int = 10, exclude_chunk_id: Optional[Union[str, uuid.UUID]] = None,
                      min_similarity: float = 0.0, filter_document_id: Optional[Union[str, uuid.UUID]] = None) -> List[Dict[str, Any]]:
        """
        Search for similar chunks using vector similarity with pgvector.

        Args:
            embedding: Query embedding vector
            limit: Maximum number of results
            exclude_chunk_id: Optional chunk ID to exclude from results (e.g., the query chunk itself)
            min_similarity: Minimum similarity threshold (0.0 to 1.0)
            filter_document_id: Optional document ID to filter results by

        Returns:
            List[Dict[str, Any]]: List of similar chunks with similarity scores
        """
        results = []

        # Convert embedding to vector format
        query_vector = embedding

        # Build query - explicitly use RAG chunks (Chunk table) for retrieval
        query = (
            self.db.query(
                Chunk.id,
                Chunk.document_id,
                Chunk.chunk_index,
                Chunk.text,
                Chunk.chunk_metadata,
                Document.filename,
                func.cosine_similarity(Chunk.embedding, query_vector).label("similarity")
            )
            .join(Document, Chunk.document_id == Document.id)
            .filter(Chunk.embedding.is_not(None))
        )
        # Note: We're using the Chunk table which contains RAG chunks optimized for retrieval

        # Exclude specific chunk if provided
        if exclude_chunk_id is not None:
            if isinstance(exclude_chunk_id, str):
                exclude_chunk_id = uuid.UUID(exclude_chunk_id)
            query = query.filter(Chunk.id != exclude_chunk_id)

        # Filter by document ID if provided
        if filter_document_id is not None:
            if isinstance(filter_document_id, str):
                filter_document_id = uuid.UUID(filter_document_id)
            query = query.filter(Chunk.document_id == filter_document_id)

        # Apply similarity threshold if provided
        if min_similarity > 0.0:
            query = query.filter(func.cosine_similarity(Chunk.embedding, query_vector) >= min_similarity)

        # Complete the query with ordering and limit
        chunk_results = (
            query.order_by(func.cosine_similarity(Chunk.embedding, query_vector).desc())
            .limit(limit)
            .all()
        )

        # Format results
        for result in chunk_results:
            results.append({
                "chunk_id": str(result.id),
                "document_id": str(result.document_id),
                "filename": result.filename,
                "chunk_index": result.chunk_index,
                "text": result.text,
                "metadata": result.chunk_metadata,
                "similarity": float(result.similarity)
            })

        return results

    def create_entity(self,
                     id: Union[str, uuid.UUID],
                     document_id: Union[str, uuid.UUID],
                     text: str,
                     entity_type: str,
                     chunk_id: Optional[Union[str, uuid.UUID]] = None,
                     kg_chunk_id: Optional[Union[str, uuid.UUID]] = None,
                     normalized_id: Optional[str] = None,
                     start_pos: Optional[int] = None,
                     end_pos: Optional[int] = None,
                     confidence: Optional[float] = None,
                     entity_metadata: Optional[Dict[str, Any]] = None,
                     embedding: Optional[List[float]] = None) -> Entity:
        """
        Create a new entity record.

        Args:
            id: Entity ID
            document_id: Document ID
            text: Entity text
            entity_type: Entity type
            chunk_id: Optional chunk ID
            kg_chunk_id: Optional KG chunk ID
            normalized_id: Optional normalized entity ID
            start_pos: Optional start position in text
            end_pos: Optional end position in text
            confidence: Optional confidence score
            entity_metadata: Optional entity metadata
            embedding: Optional entity embedding

        Returns:
            Entity: Created entity record
        """
        # Convert IDs to UUID if they're strings
        if isinstance(id, str):
            id = uuid.UUID(id)
        if isinstance(document_id, str):
            document_id = uuid.UUID(document_id)
        if isinstance(chunk_id, str) and chunk_id:
            chunk_id = uuid.UUID(chunk_id)
        if isinstance(kg_chunk_id, str) and kg_chunk_id:
            kg_chunk_id = uuid.UUID(kg_chunk_id)

        # Create entity record
        entity = Entity(
            id=id,
            document_id=document_id,
            chunk_id=chunk_id,
            kg_chunk_id=kg_chunk_id,
            text=text,
            entity_type=entity_type,
            normalized_id=normalized_id,
            start_pos=start_pos,
            end_pos=end_pos,
            confidence=confidence,
            entity_metadata=entity_metadata,
            embedding=embedding
        )

        self.db.add(entity)
        self.db.commit()
        self.db.refresh(entity)

        return entity

    def create_relationship(self,
                          id: Union[str, uuid.UUID],
                          document_id: Union[str, uuid.UUID],
                          source_id: Union[str, uuid.UUID],
                          target_id: Union[str, uuid.UUID],
                          relationship_type: str,
                          chunk_id: Optional[Union[str, uuid.UUID]] = None,
                          kg_chunk_id: Optional[Union[str, uuid.UUID]] = None,
                          confidence: Optional[float] = None,
                          evidence_text: Optional[str] = None,
                          relationship_metadata: Optional[Dict[str, Any]] = None) -> Relationship:
        """
        Create a new relationship record.

        Args:
            id: Relationship ID
            document_id: Document ID
            source_id: Source entity ID
            target_id: Target entity ID
            relationship_type: Relationship type
            chunk_id: Optional chunk ID
            kg_chunk_id: Optional KG chunk ID
            confidence: Optional confidence score
            evidence_text: Optional evidence text
            relationship_metadata: Optional relationship metadata

        Returns:
            Relationship: Created relationship record
        """
        # Convert IDs to UUID if they're strings
        if isinstance(id, str):
            id = uuid.UUID(id)
        if isinstance(document_id, str):
            document_id = uuid.UUID(document_id)
        if isinstance(source_id, str):
            source_id = uuid.UUID(source_id)
        if isinstance(target_id, str):
            target_id = uuid.UUID(target_id)
        if isinstance(chunk_id, str) and chunk_id:
            chunk_id = uuid.UUID(chunk_id)
        if isinstance(kg_chunk_id, str) and kg_chunk_id:
            kg_chunk_id = uuid.UUID(kg_chunk_id)

        # Create relationship record
        relationship = Relationship(
            id=id,
            document_id=document_id,
            source_id=source_id,
            target_id=target_id,
            relationship_type=relationship_type,
            chunk_id=chunk_id,
            kg_chunk_id=kg_chunk_id,
            confidence=confidence,
            evidence_text=evidence_text,
            relationship_metadata=relationship_metadata
        )

        self.db.add(relationship)
        self.db.commit()
        self.db.refresh(relationship)

        return relationship

    def get_entity(self, entity_id: Union[str, uuid.UUID]) -> Optional[Entity]:
        """
        Get entity by ID.

        Args:
            entity_id: Entity ID

        Returns:
            Optional[Entity]: Entity record if found
        """
        if isinstance(entity_id, str):
            entity_id = uuid.UUID(entity_id)

        return self.db.query(Entity).filter(Entity.id == entity_id).first()

    def get_entities_by_document(self, document_id: Union[str, uuid.UUID]) -> List[Entity]:
        """
        Get all entities for a document.

        Args:
            document_id: Document ID

        Returns:
            List[Entity]: List of entities
        """
        if isinstance(document_id, str):
            document_id = uuid.UUID(document_id)

        return self.db.query(Entity).filter(Entity.document_id == document_id).all()

    def get_relationships_by_document(self, document_id: Union[str, uuid.UUID]) -> List[Relationship]:
        """
        Get all relationships for a document.

        Args:
            document_id: Document ID

        Returns:
            List[Relationship]: List of relationships
        """
        if isinstance(document_id, str):
            document_id = uuid.UUID(document_id)

        return self.db.query(Relationship).filter(Relationship.document_id == document_id).all()

    def find_similar_by_chunk_id(self, chunk_id: Union[str, uuid.UUID], limit: int = 10,
                                min_similarity: float = 0.0, filter_document_id: Optional[Union[str, uuid.UUID]] = None) -> List[Dict[str, Any]]:
        """
        Find chunks similar to a specific chunk using pgvector similarity search.

        This method retrieves the embedding for the specified chunk and then finds
        other chunks with similar embeddings.

        Args:
            chunk_id: ID of the chunk to find similar chunks for
            limit: Maximum number of results
            min_similarity: Minimum similarity threshold (0.0 to 1.0)
            filter_document_id: Optional document ID to filter results by

        Returns:
            List[Dict[str, Any]]: List of similar chunks with similarity scores
        """
        # Convert chunk_id to UUID if it's a string
        if isinstance(chunk_id, str):
            chunk_id = uuid.UUID(chunk_id)

        # Get the RAG chunk and its embedding
        # Note: We're explicitly using the Chunk table which contains RAG chunks optimized for retrieval
        chunk = self.db.query(Chunk).filter(Chunk.id == chunk_id).first()
        if not chunk or chunk.embedding is None:
            raise ValueError(f"RAG chunk with ID {chunk_id} not found or has no embedding")

        # Use the embedding to find similar chunks
        return self.search_similar(
            embedding=chunk.embedding,
            limit=limit,
            exclude_chunk_id=chunk_id,  # Exclude the query chunk itself
            min_similarity=min_similarity,
            filter_document_id=filter_document_id
        )

    def get_entities_by_document(self, document_id: Union[str, uuid.UUID]) -> List[Entity]:
        """
        Get all entities for a document.

        Args:
            document_id: Document ID

        Returns:
            List[Entity]: List of entities
        """
        if isinstance(document_id, str):
            document_id = uuid.UUID(document_id)

        return self.db.query(Entity).filter(Entity.document_id == document_id).all()

    def get_processing_task(self, task_id: Union[str, uuid.UUID]) -> Optional[ProcessingTask]:
        """
        Get processing task by ID.

        Args:
            task_id: Task ID

        Returns:
            Optional[ProcessingTask]: Processing task record if found
        """
        if isinstance(task_id, str):
            task_id = uuid.UUID(task_id)

        return self.db.query(ProcessingTask).filter(ProcessingTask.id == task_id).first()

    def get_processing_tasks_by_document(self, document_id: Union[str, uuid.UUID]) -> List[ProcessingTask]:
        """
        Get all processing tasks for a document.

        Args:
            document_id: Document ID

        Returns:
            List[ProcessingTask]: List of processing tasks
        """
        if isinstance(document_id, str):
            document_id = uuid.UUID(document_id)

        return self.db.query(ProcessingTask).filter(ProcessingTask.document_id == document_id).all()

    def get_processing_tasks_by_celery_id(self, celery_task_id: str) -> List[ProcessingTask]:
        """
        Get all processing tasks with a specific Celery task ID.

        Args:
            celery_task_id: Celery task ID

        Returns:
            List[ProcessingTask]: List of processing tasks
        """
        return self.db.query(ProcessingTask).filter(ProcessingTask.celery_task_id == celery_task_id).all()

    def find_similar_entities(self, embedding: List[float], entity_type: Optional[str] = None,
                             threshold: float = 0.9, limit: int = 5) -> List[Tuple[Entity, float]]:
        """
        Find entities similar to the given embedding using vector similarity.

        Args:
            embedding: Entity embedding to search for
            entity_type: Optional entity type to filter by
            threshold: Similarity threshold (0-1)
            limit: Maximum number of results to return

        Returns:
            List[Tuple[Entity, float]]: List of (entity, similarity) pairs
        """
        # Convert embedding to vector format
        query_vector = embedding

        # Build query
        query = (
            self.db.query(
                Entity,
                func.cosine_similarity(func.cast(Entity.entity_metadata["embedding"].astext, Vector), query_vector).label("similarity")
            )
            .filter(Entity.entity_metadata.has_key("embedding"))
        )

        # Filter by entity type if provided
        if entity_type is not None:
            query = query.filter(Entity.entity_type == entity_type)

        # Apply similarity threshold
        query = query.filter(
            func.cosine_similarity(func.cast(Entity.entity_metadata["embedding"].astext, Vector), query_vector) >= threshold
        )

        # Complete the query with ordering and limit
        results = (
            query.order_by(func.cosine_similarity(func.cast(Entity.entity_metadata["embedding"].astext, Vector), query_vector).desc())
            .limit(limit)
            .all()
        )

        return results