"""
Storage client for interacting with MinIO/S3-compatible storage or Google Cloud Storage.
"""
import os
import uuid
import tempfile
from abc import ABC, abstractmethod
from datetime import datetime, timedelta, timezone
from urllib.parse import urlparse
from typing import BinaryIO, Dict, Optional, Union, Tuple

import requests
from minio import Minio
from minio.error import S3Error

from common.config import settings

try:
    from google.cloud import storage
    from google.cloud.exceptions import GoogleCloudError
    from google.auth.exceptions import DefaultCredentialsError
    GCS_AVAILABLE = True
except ImportError:
    GCS_AVAILABLE = False


class BaseStorageClient(ABC):
    """Abstract base class for storage clients."""

    @abstractmethod
    def upload_file(self,
                   file_obj: BinaryIO,
                   content_type: str,
                   filename: Optional[str] = None) -> str:
        """Upload a file to storage."""
        pass

    @abstractmethod
    def download_file(self, storage_path: str) -> Tuple[bytes, Dict]:
        """Download a file from storage."""
        pass

    @abstractmethod
    def get_presigned_url(self, storage_path: str, expires: int = 3600) -> str:
        """Generate presigned URL for accessing a file."""
        pass

    @abstractmethod
    def delete_file(self, storage_path: str) -> bool:
        """Delete a file from storage."""
        pass

    def download_from_url(self, url: str) -> Tuple[bytes, str, str]:
        """
        Download a file from a URL.

        Args:
            url: URL to download from

        Returns:
            Tuple[bytes, str, str]: File content, content type, and filename
        """
        try:
            response = requests.get(url, stream=True, timeout=30)
            response.raise_for_status()

            # Get content type
            content_type = response.headers.get("Content-Type", "application/octet-stream")

            # Try to get filename from URL or Content-Disposition header
            filename = None
            content_disposition = response.headers.get("Content-Disposition")

            if content_disposition and "filename=" in content_disposition:
                # Extract filename from Content-Disposition
                filename = content_disposition.split("filename=")[1].strip('"\'')
            else:
                # Extract filename from URL
                parsed_url = urlparse(url)
                url_path = parsed_url.path
                filename = os.path.basename(url_path)

            # If filename is still empty, use a generic name based on content type
            if not filename:
                filename = f"document_{uuid.uuid4()}"
                if "pdf" in content_type:
                    filename += ".pdf"
                elif "epub" in content_type:
                    filename += ".epub"
                elif "text/plain" in content_type:
                    filename += ".txt"
                elif "text/html" in content_type:
                    filename += ".html"

            # Download content
            content = response.content

            return content, content_type, filename

        except requests.RequestException as e:
            raise Exception(f"Failed to download from URL: {str(e)}")


class MinioStorageClient(BaseStorageClient):
    """Client for interacting with MinIO/S3-compatible object storage."""

    def __init__(self):
        """Initialize storage client with settings."""
        # Get MinIO endpoint from environment or settings
        minio_endpoint = os.environ.get('MINIO_ENDPOINT', f"{settings.MINIO_HOST}:{settings.MINIO_PORT}")
        # Get access key and secret key from environment or settings
        access_key = os.environ.get('MINIO_ACCESS_KEY', settings.MINIO_ROOT_USER)
        secret_key = os.environ.get('MINIO_SECRET_KEY', settings.MINIO_ROOT_PASSWORD)
        # Get secure flag from environment or settings
        secure = os.environ.get('MINIO_SECURE', str(settings.MINIO_SECURE)).lower() == 'true'
        # Get bucket name from environment or settings
        self.bucket_name = os.environ.get('MINIO_BUCKET_NAME', settings.STORAGE_BUCKET_NAME)

        self.client = Minio(
            minio_endpoint,
            access_key=access_key,
            secret_key=secret_key,
            secure=secure,
        )

        # Ensure bucket exists
        self._ensure_bucket_exists()

    def _ensure_bucket_exists(self):
        """Ensure the specified bucket exists."""
        try:
            if not self.client.bucket_exists(self.bucket_name):
                self.client.make_bucket(self.bucket_name)
                # Set public read policy if needed
                # self.client.set_bucket_policy(self.bucket_name, policy)
        except S3Error as e:
            raise Exception(f"Failed to check/create bucket: {str(e)}")

    def upload_file(self,
                   file_obj: BinaryIO,
                   content_type: str,
                   filename: Optional[str] = None) -> str:
        """
        Upload a file to storage.

        Args:
            file_obj: File object or bytes-like object
            content_type: Content type of the file
            filename: Original filename (used for generating path)

        Returns:
            str: Storage path where the file was uploaded
        """
        # Generate storage path using timestamp and filename
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")
        file_uuid = str(uuid.uuid4())

        # Use original filename if provided, otherwise use UUID
        if filename:
            # Extract extension from filename
            _, ext = os.path.splitext(filename)
            storage_filename = f"{file_uuid}{ext}"
        else:
            storage_filename = file_uuid

        # Create path: yyyy/mm/dd/timestamp_uuid_filename
        storage_path = f"{datetime.now(timezone.utc).strftime('%Y/%m/%d')}/{timestamp}_{storage_filename}"

        try:
            # Get file size
            file_obj.seek(0, os.SEEK_END)
            file_size = file_obj.tell()
            file_obj.seek(0)

            # Upload file
            self.client.put_object(
                bucket_name=self.bucket_name,
                object_name=storage_path,
                data=file_obj,
                length=file_size,
                content_type=content_type
            )

            return storage_path

        except S3Error as e:
            raise Exception(f"Failed to upload file: {str(e)}")

    def download_file(self, storage_path: str) -> Tuple[bytes, Dict]:
        """
        Download a file from storage.

        Args:
            storage_path: Path to the file in storage

        Returns:
            Tuple[bytes, dict]: File content and metadata
        """
        try:
            # Get object
            response = self.client.get_object(
                bucket_name=self.bucket_name,
                object_name=storage_path
            )

            # Read data and metadata
            data = response.read()
            metadata = {
                "content_type": response.headers.get("Content-Type", "application/octet-stream"),
                "last_modified": response.headers.get("Last-Modified"),
                "size": len(data),
            }

            return data, metadata

        except S3Error as e:
            raise Exception(f"Failed to download file: {str(e)}")
        finally:
            if 'response' in locals():
                response.close()
                response.release_conn()

    def get_presigned_url(self, storage_path: str, expires: int = 3600) -> str:
        """
        Generate presigned URL for accessing a file.

        Args:
            storage_path: Path to the file in storage
            expires: Expiration time in seconds

        Returns:
            str: Presigned URL
        """
        try:
            return self.client.presigned_get_object(
                bucket_name=self.bucket_name,
                object_name=storage_path,
                expires=timedelta(seconds=expires)
            )
        except S3Error as e:
            raise Exception(f"Failed to generate presigned URL: {str(e)}")

    def delete_file(self, storage_path: str) -> bool:
        """
        Delete a file from storage.

        Args:
            storage_path: Path to the file in storage

        Returns:
            bool: True if successful
        """
        try:
            self.client.remove_object(
                bucket_name=self.bucket_name,
                object_name=storage_path
            )
            return True
        except S3Error as e:
            raise Exception(f"Failed to delete file: {str(e)}")


class GCSStorageClient(BaseStorageClient):
    """Client for interacting with Google Cloud Storage."""

    def __init__(self):
        """Initialize Google Cloud Storage client with settings."""
        if not GCS_AVAILABLE:
            raise ImportError(
                "Google Cloud Storage library is not installed. "
                "Install it with 'pip install google-cloud-storage'"
            )
        
        try:
            # Get bucket name from environment or settings
            self.bucket_name = os.environ.get('GCS_BUCKET_NAME', settings.STORAGE_BUCKET_NAME)
            
            # Initialize client
            self.client = storage.Client()
            
            # Get bucket reference
            self.bucket = self.client.bucket(self.bucket_name)
            
            # Ensure bucket exists
            self._ensure_bucket_exists()
            
        except (DefaultCredentialsError, GoogleCloudError) as e:
            raise Exception(f"Failed to initialize GCS client: {str(e)}")

    def _ensure_bucket_exists(self):
        """Ensure the specified bucket exists."""
        try:
            if not self.bucket.exists():
                self.bucket = self.client.create_bucket(self.bucket_name)
        except GoogleCloudError as e:
            raise Exception(f"Failed to check/create bucket: {str(e)}")

    def upload_file(self,
                   file_obj: BinaryIO,
                   content_type: str,
                   filename: Optional[str] = None) -> str:
        """
        Upload a file to storage.

        Args:
            file_obj: File object or bytes-like object
            content_type: Content type of the file
            filename: Original filename (used for generating path)

        Returns:
            str: Storage path where the file was uploaded
        """
        # Generate storage path using timestamp and filename
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S")
        file_uuid = str(uuid.uuid4())

        # Use original filename if provided, otherwise use UUID
        if filename:
            # Extract extension from filename
            _, ext = os.path.splitext(filename)
            storage_filename = f"{file_uuid}{ext}"
        else:
            storage_filename = file_uuid

        # Create path: yyyy/mm/dd/timestamp_uuid_filename
        storage_path = f"{datetime.now(timezone.utc).strftime('%Y/%m/%d')}/{timestamp}_{storage_filename}"

        try:
            # Create blob
            blob = self.bucket.blob(storage_path)
            
            # Upload file
            blob.upload_from_file(
                file_obj,
                content_type=content_type,
                rewind=True
            )
            
            return storage_path

        except GoogleCloudError as e:
            raise Exception(f"Failed to upload file: {str(e)}")

    def download_file(self, storage_path: str) -> Tuple[bytes, Dict]:
        """
        Download a file from storage.

        Args:
            storage_path: Path to the file in storage

        Returns:
            Tuple[bytes, dict]: File content and metadata
        """
        try:
            # Get blob
            blob = self.bucket.blob(storage_path)
            
            # Download content
            content = blob.download_as_bytes()
            
            # Get metadata
            blob.reload()  # Ensure we have the latest metadata
            metadata = {
                "content_type": blob.content_type or "application/octet-stream",
                "last_modified": blob.updated,
                "size": blob.size,
            }
            
            return content, metadata

        except GoogleCloudError as e:
            raise Exception(f"Failed to download file: {str(e)}")

    def get_presigned_url(self, storage_path: str, expires: int = 3600) -> str:
        """
        Generate presigned URL for accessing a file.

        Args:
            storage_path: Path to the file in storage
            expires: Expiration time in seconds

        Returns:
            str: Presigned URL
        """
        try:
            blob = self.bucket.blob(storage_path)
            url = blob.generate_signed_url(
                expiration=timedelta(seconds=expires),
                method="GET"
            )
            return url
        except GoogleCloudError as e:
            raise Exception(f"Failed to generate presigned URL: {str(e)}")

    def delete_file(self, storage_path: str) -> bool:
        """
        Delete a file from storage.

        Args:
            storage_path: Path to the file in storage

        Returns:
            bool: True if successful
        """
        try:
            blob = self.bucket.blob(storage_path)
            blob.delete()
            return True
        except GoogleCloudError as e:
            raise Exception(f"Failed to delete file: {str(e)}")


def create_storage_client() -> BaseStorageClient:
    """
    Factory function to create the appropriate storage client based on configuration.
    
    Returns:
        BaseStorageClient: An instance of the appropriate storage client
    """
    storage_provider = os.environ.get('STORAGE_PROVIDER', 'minio').lower()
    
    if storage_provider == 'gcs':
        return GCSStorageClient()
    else:
        # Default to MinIO/S3
        return MinioStorageClient()


# For backward compatibility
StorageClient = create_storage_client
