longevity-platform/
├── api/                  # FastAPI app
│   ├── main.py           # Main app
│   └── routers/          # API endpoints
├── services/             # Business logic
│   ├── document_service.py # Document processing
│   └── entity_service.py # Entity extraction
├── workers/              # Celery workers
│   ├── celery_app.py     # Celery config
│   ├── document_worker.py # Document processing tasks
│   └── entity_worker.py  # Entity extraction tasks
├── common/               # Shared modules
│   ├── config.py         # Settings
│   └── database.py       # DB models
├── transport/            # Data transport layer
│   ├── data_transport.py # Coordination
│   ├── storage_client.py # MinIO client
│   └── database_client.py # Database client
└── docker/               # Docker configs