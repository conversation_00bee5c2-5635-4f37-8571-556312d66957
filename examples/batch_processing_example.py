"""
Example script demonstrating how to use the Vertex AI client for batch processing.
"""
import logging
import sys
import time
import os
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from common.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def check_dependencies():
    """Check if required dependencies are installed."""
    missing_deps = []
    try:
        import google.cloud.storage
    except ImportError:
        missing_deps.append("google-cloud-storage")
    
    try:
        import google.cloud.aiplatform
    except ImportError:
        missing_deps.append("google-cloud-aiplatform")
    
    if missing_deps:
        logger.error(f"Missing required dependencies: {', '.join(missing_deps)}")
        logger.error("\nThis script needs to run in the Docker environment with all dependencies.")
        logger.error("Please run it with docker-compose exec:\n")
        logger.error("docker-compose exec api python3 /app/examples/batch_processing_example.py --check-neo4j")
        logger.error("OR")
        logger.error("docker-compose exec worker python3 /app/examples/batch_processing_example.py --check-neo4j")
        return False
    return True


def check_recent_batch_jobs():
    """Check the status of recent batch prediction jobs."""
    try:
        from transport.vertex_ai_batch_client import VertexAIBatchClient
        import psycopg2
        from psycopg2.extras import RealDictCursor
    except ImportError as e:
        logger.error(f"Import error: {e}")
        logger.error("Please run this script inside the Docker container with all dependencies.")
        return
    
    # Initialize Vertex AI Batch client
    client = VertexAIBatchClient()
    
    # Connect to the database to get batch jobs
    try:
        conn = psycopg2.connect(
            host=settings.POSTGRES_HOST,
            port=settings.POSTGRES_PORT,
            dbname=settings.POSTGRES_DB,
            user=settings.POSTGRES_USER,
            password=settings.POSTGRES_PASSWORD
        )
        
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        # Get recent batch jobs
        cursor.execute("""
            SELECT id, task_type, status, created_at, updated_at, document_id,
                   result->>'batch_job_id' as batch_job_id
            FROM processing_tasks
            WHERE (task_type = 'knowledge_graph_building' AND
                   result->>'batch_job_id' IS NOT NULL)
               OR task_type = 'vertex_ai_batch_job'
            ORDER BY created_at DESC
            LIMIT 10
        """)
        
        batch_jobs = cursor.fetchall()
        logger.info(f"Found {len(batch_jobs)} recent batch jobs:")
        
        for job in batch_jobs:
            logger.info(f"Job ID: {job['id']}")
            logger.info(f"Status: {job['status']}")
            logger.info(f"Document ID: {job['document_id']}")
            logger.info(f"Created: {job['created_at']}")
            
            if job['batch_job_id']:
                try:
                    # Check the batch job status
                    state = client.get_batch_job_state(job['batch_job_id'])
                    logger.info(f"Batch Job ID: {job['batch_job_id']}")
                    logger.info(f"Batch Job State: {state}")
                    logger.info("---")
                except Exception as e:
                    logger.error(f"Error checking batch job {job['batch_job_id']}: {e}")
            else:
                logger.info("No batch job ID found")
                logger.info("---")
                
        cursor.close()
        conn.close()
    
    except Exception as e:
        logger.error(f"Error checking recent batch jobs: {e}")


def check_neo4j_entities():
    """Check if entities have been uploaded to Neo4j."""
    try:
        from transport.neo4j_client import Neo4jClient
    except ImportError as e:
        logger.error(f"Import error: {e}")
        logger.error("Please run this script inside the Docker container with all dependencies.")
        return
    
    try:
        # Initialize Neo4j client
        client = Neo4jClient()
        
        # Count total number of entities
        result = client.run_query(
            "MATCH (e:Entity) RETURN count(e) as entity_count"
        )
        entity_count = result[0]["entity_count"] if result else 0
        
        # Count total number of relationships
        result = client.run_query(
            "MATCH ()-[r]->() RETURN count(r) as relationship_count"
        )
        rel_count = result[0]["relationship_count"] if result else 0
        
        # Get sample of recent entities
        result = client.run_query(
            "MATCH (e:Entity) RETURN e.id as id, e.text as text, e.type as type, e.created_at as created_at ORDER BY e.created_at DESC LIMIT 5"
        )
        
        logger.info(f"Neo4j DB contains {entity_count} entities and {rel_count} relationships")
        
        if result:
            logger.info("Recent entities:")
            for entity in result:
                logger.info(f"ID: {entity['id']}")
                logger.info(f"Text: {entity['text']}")
                logger.info(f"Type: {entity['type']}")
                logger.info(f"Created: {entity['created_at']}")
                logger.info("---")
        else:
            logger.info("No entities found in Neo4j")
            
    except Exception as e:
        logger.error(f"Error checking Neo4j entities: {e}")


def print_docker_exec_command():
    """Print commands to run this script in docker."""
    script_path = os.path.abspath(__file__)
    relative_path = os.path.relpath(script_path, os.path.dirname(os.path.dirname(script_path)))
    docker_path = f"/app/{relative_path}"
    
    logger.info("\nTo run this script with Docker, use one of the following commands:")
    logger.info(f"docker-compose exec api python3 {docker_path} --check-neo4j")
    logger.info("OR")
    logger.info(f"docker-compose exec worker python3 {docker_path} --check-neo4j")
    logger.info(f"docker-compose exec worker python3 {docker_path} --check-jobs")


def main():
    """Main function to demonstrate batch processing."""
    # Parse command line arguments
    import argparse
    parser = argparse.ArgumentParser(description="Batch processing example")
    parser.add_argument("--check-jobs", action="store_true", help="Check recent batch jobs")
    parser.add_argument("--check-neo4j", action="store_true", help="Check Neo4j for entities")
    parser.add_argument("--docker-help", action="store_true", help="Print Docker exec commands")
    args = parser.parse_args()
    
    if args.docker_help:
        print_docker_exec_command()
        return
    
    if not check_dependencies():
        print_docker_exec_command()
        return
    
    if args.check_jobs:
        logger.info("Checking recent batch jobs...")
        check_recent_batch_jobs()
        return
        
    if args.check_neo4j:
        logger.info("Checking Neo4j entities...")
        check_neo4j_entities()
        return
    
    # If no arguments provided, print help
    print_docker_exec_command()


if __name__ == "__main__":
    main()
