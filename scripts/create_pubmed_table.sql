-- Create pubmed_articles table to store detailed information about PubMed articles
CREATE TABLE IF NOT EXISTS pubmed_articles (
    id SERIAL PRIMARY KEY,
    pmid VARCHAR(20) UNIQUE NOT NULL,
    title TEXT NOT NULL,
    abstract TEXT,
    authors JSON<PERSON>,
    journal VARCHAR(255),
    publication_date DATE,
    publication_year INT,
    mesh_terms JSONB,
    keywords JSONB,
    document_id UUID,  -- References the document in the documents table
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create index on PMID for faster lookups
CREATE INDEX idx_pubmed_articles_pmid ON pubmed_articles(pmid);

-- Create index on document_id for joins with documents table
CREATE INDEX idx_pubmed_articles_document_id ON pubmed_articles(document_id);

-- Create index on publication_year for filtering by year
CREATE INDEX idx_pubmed_articles_pub_year ON pubmed_articles(publication_year);

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_pubmed_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update updated_at on row update
CREATE TRIGGER pubmed_articles_updated_at
BEFORE UPDATE ON pubmed_articles
FOR EACH ROW
EXECUTE FUNCTION update_pubmed_updated_at();

-- Add a comment to the table
COMMENT ON TABLE pubmed_articles IS 'Stores detailed information about PubMed articles that have been ingested';