#!/bin/bash

# Script to set up environment variables for testing storage clients
# Usage: 
#   source scripts/setup_storage_env.sh minio                           # For MinIO testing
#   source scripts/setup_storage_env.sh gcs                             # For GCS testing
#   source scripts/setup_storage_env.sh gcs /path/to/gcs_login.json     # For GCS with explicit credentials path

# Determine which storage provider to use
PROVIDER=${1:-minio}
PROVIDER=$(echo $PROVIDER | tr '[:upper:]' '[:lower:]')  # Convert to lowercase

# Check if custom credentials path is provided
CUSTOM_CREDS_PATH=${2:-""}

# Base directory (project root)
BASE_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )/.." && pwd )"
echo "🔎 Project root directory: $BASE_DIR"

# Common variables
export STORAGE_BUCKET_NAME="longevity-documents-test"

if [[ "$PROVIDER" == "gcs" ]]; then
    # GCS Settings
    export STORAGE_PROVIDER="gcs"
    
    # Look for credentials file in multiple locations
    FOUND_CREDS=false
    
    # Check locations in order:
    # 1. Custom path if provided
    # 2. Project root
    # 3. Current directory
    # 4. User's home directory
    
    POSSIBLE_PATHS=(
        "$CUSTOM_CREDS_PATH"
        "$BASE_DIR/gcs_login.json"
        "./gcs_login.json"
        "$HOME/gcs_login.json"
        "/Users/<USER>/Repos/LongevityCo/gcs_login.json"
    )
    
    for path in "${POSSIBLE_PATHS[@]}"; do
        if [[ -n "$path" && -f "$path" ]]; then
            export GOOGLE_APPLICATION_CREDENTIALS="$path"
            echo "✅ Found GCS credentials at: $path"
            FOUND_CREDS=true
            break
        elif [[ -n "$path" ]]; then
            echo "🔍 Checked but not found: $path"
        fi
    done
    
    if [[ "$FOUND_CREDS" == "false" ]]; then
        echo "❌ ERROR: GCS credentials file not found"
        echo "Please specify the path to your credentials file:"
        echo "source scripts/setup_storage_env.sh gcs /path/to/your/gcs_login.json"
        return 1
    fi
    
    # Set project ID if specified in config
    if [[ -n "$GCS_PROJECT_ID" ]]; then
        export GCS_PROJECT_ID="$GCS_PROJECT_ID"
        echo "✅ Using GCS project ID: $GCS_PROJECT_ID"
    else
        export GCS_PROJECT_ID="rosy-rider-453708-c3"
        echo "✅ Using default GCS project ID: $GCS_PROJECT_ID"
    fi
    
    echo "✅ GCS environment variables set successfully!"

else
    # MinIO Settings
    export STORAGE_PROVIDER="minio"
    export MINIO_HOST="localhost"
    export MINIO_PORT="9090"
    export MINIO_ENDPOINT="localhost:9090"
    export MINIO_ACCESS_KEY="minio"
    export MINIO_SECRET_KEY="minio123"
    export MINIO_SECURE="false"
    
    echo "✅ MinIO environment variables set (port 9090)"
    echo "ℹ️  Note: Make sure MinIO is running with:"
    echo "docker run -p 9090:9090 -p 9091:9091 --name minio \\
  -e \"MINIO_ROOT_USER=minio\" \\
  -e \"MINIO_ROOT_PASSWORD=minio123\" \\
  minio/minio server /data --console-address \":9091\" --address \":9090\""
fi

# Print summary
echo ""
echo "📋 Storage Environment Summary:"
echo "------------------------------"
echo "Provider:    $STORAGE_PROVIDER"
echo "Bucket:      $STORAGE_BUCKET_NAME"

if [[ "$PROVIDER" == "minio" ]]; then
    echo "Endpoint:    $MINIO_ENDPOINT"
    echo "Access Key:  $MINIO_ACCESS_KEY"
    echo "Secure:      $MINIO_SECURE"
else
    echo "Project ID:  $GCS_PROJECT_ID"
    echo "Credentials: $GOOGLE_APPLICATION_CREDENTIALS"
fi

echo ""
# Use the correct path to test script and check for python3 if python isn't available
if command -v python &>/dev/null; then
    echo "🧪 Run tests with: python $BASE_DIR/tests/test_storage_client.py"
elif command -v python3 &>/dev/null; then
    echo "🧪 Run tests with: python3 $BASE_DIR/tests/test_storage_client.py"
else
    echo "⚠️  Python command not found. Please use the appropriate Python command for your system."
    echo "🧪 Run tests with: [python-command] $BASE_DIR/tests/test_storage_client.py"
fi
echo ""
