#!/usr/bin/env python
"""
Script to rerun all failed knowledge graph building jobs.

This script will:
1. Query the API for all failed knowledge graph jobs
2. Rerun each failed job one by one
3. Report on the results

Usage:
    python rerun_failed_kg_jobs.py [--limit <num>] [--batch <bool>] [--async-mode <bool>]

Options:
    --limit <num>       Limit the number of failed jobs to rerun (default: all)
    --batch <bool>      Whether to use batch API (true/false, default: true)
    --async-mode <bool> Whether to use async processing (true/false, default: true)
    --status <str>      Filter by specific status ('failed', 'batch_job_failed', etc., default: 'failed')
    --dry-run           Show what would be rerun without actually rerunning jobs
    --api-url <url>     Base URL of the API service (default: http://localhost:8000)
"""

import argparse
import json
import time
import sys
import os
import requests
from typing import Dict, Any, List, Optional
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("kg-rerunner")

# API settings - adjust these based on your environment
DEFAULT_API_URL = "http://localhost:8000"  # Change if your API runs elsewhere


def get_api_url(user_url=None):
    """Get the API base URL from parameters, environment or use default."""
    if user_url:
        return user_url
    return os.environ.get("API_BASE_URL", DEFAULT_API_URL)


def get_failed_jobs(api_url: str, limit: Optional[int] = None, status: str = "failed") -> List[Dict[str, Any]]:
    """
    Get list of failed knowledge graph jobs from the API.
    
    Args:
        api_url: Base URL for the API
        limit: Maximum number of jobs to retrieve
        status: Status filter ('failed', 'batch_job_failed', etc.)
    
    Returns:
        List of failed job information dicts
    """
    url = f"{api_url}/api/knowledge-graph/failed-jobs"
    params = {}
    
    if limit:
        params["limit"] = limit
    
    try:
        logger.info(f"Sending request to {url}")
        response = requests.get(url, params=params)
        response.raise_for_status()
        result = response.json()
        
        # Filter by status if needed
        jobs = result.get("failed_jobs", [])
        if status != "failed":
            # More flexible matching - either exact match or contains the status
            jobs = [job for job in jobs if 
                   job.get("status") == status or 
                   (isinstance(job.get("status"), str) and status in job.get("status"))]
            
        return jobs
    
    except Exception as e:
        logger.error(f"Error getting failed jobs: {e}")
        return []


def rerun_job(api_url: str, task_id: str, use_batch_api: bool = True, async_processing: bool = True) -> Dict[str, Any]:
    """
    Rerun a specific failed knowledge graph job.
    
    Args:
        api_url: Base URL for the API
        task_id: ID of the failed task to rerun
        use_batch_api: Whether to use batch API
        async_processing: Whether to use async processing
    
    Returns:
        Response from the API with new task information
    """
    url = f"{api_url}/api/knowledge-graph/rerun-job/{task_id}"
    payload = {
        "use_batch_api": use_batch_api,
        "async_processing": async_processing
    }
    
    try:
        response = requests.post(url, json=payload)
        response.raise_for_status()
        return response.json()
    
    except Exception as e:
        logger.error(f"Error rerunning job {task_id}: {e}")
        return {"status": "error", "message": str(e)}


def main():
    """Main function to parse arguments and rerun failed jobs."""
    parser = argparse.ArgumentParser(description="Rerun failed knowledge graph jobs")
    parser.add_argument("--limit", type=int, help="Limit the number of failed jobs to rerun")
    parser.add_argument("--batch", type=str, choices=["true", "false"], 
                        default="true", help="Whether to use batch API (true/false)")
    parser.add_argument("--async-mode", type=str, choices=["true", "false"], 
                        default="true", help="Whether to use async processing (true/false)")
    parser.add_argument("--status", type=str, default="failed", 
                        help="Filter by specific failure status (failed, batch_job_failed, etc.)")
    parser.add_argument("--dry-run", action="store_true", 
                        help="Show what would be rerun without actually rerunning")
    parser.add_argument("--api-url", type=str,
                        help="Base URL of the API service (default: http://localhost:8000)")
    
    args = parser.parse_args()
    
    # Convert string args to boolean
    use_batch_api = args.batch.lower() == "true"
    async_processing = args.async_mode.lower() == "true"
    
    # Get API URL
    api_url = get_api_url(args.api_url)
    
    # Get failed jobs
    logger.info(f"Fetching failed knowledge graph jobs (status: {args.status})")
    failed_jobs = get_failed_jobs(api_url, args.limit, args.status)
    logger.info(f"Found {len(failed_jobs)} failed jobs")
    
    if not failed_jobs:
        logger.info("No failed jobs to rerun.")
        return
    
    # Ask for confirmation unless it's a dry run
    if not args.dry_run:
        print(f"\nAbout to rerun {len(failed_jobs)} failed knowledge graph jobs.")
        print(f"API URL: {api_url}")
        print(f"Using batch API: {use_batch_api}")
        print(f"Using async processing: {async_processing}")
        confirmation = input("\nContinue? [y/N] ")
        
        if confirmation.lower() != "y":
            logger.info("Operation cancelled by user.")
            return
    
    # Process each job
    results = {
        "successful": [],
        "failed": []
    }
    
    for i, job in enumerate(failed_jobs):
        task_id = job.get("task_id")
        document_id = job.get("document_id")
        document_title = job.get("document_title", "Unknown")
        
        logger.info(f"[{i+1}/{len(failed_jobs)}] Processing job {task_id} for document: {document_title}")
        
        if args.dry_run:
            logger.info(f"DRY RUN: Would rerun job {task_id} for document {document_id} ({document_title})")
            results["successful"].append({
                "task_id": task_id, 
                "document_id": document_id,
                "document_title": document_title
            })
            continue
        
        try:
            result = rerun_job(api_url, task_id, use_batch_api, async_processing)
            
            if result.get("status") == "queued":
                logger.info(f"Successfully requeued job. New task ID: {result.get('task_id')}")
                results["successful"].append({
                    "original_task_id": task_id,
                    "new_task_id": result.get("task_id"),
                    "document_id": document_id,
                    "document_title": document_title
                })
            else:
                logger.warning(f"Unexpected response when rerunning job {task_id}: {result}")
                results["failed"].append({
                    "task_id": task_id,
                    "document_id": document_id, 
                    "document_title": document_title,
                    "error": "Unexpected response"
                })
            
            # Sleep briefly to avoid overwhelming the API
            time.sleep(0.5)
            
        except Exception as e:
            logger.error(f"Error rerunning job {task_id}: {e}")
            results["failed"].append({
                "task_id": task_id,
                "document_id": document_id,
                "document_title": document_title,
                "error": str(e)
            })
    
    # Print summary
    logger.info("\n===== SUMMARY =====")
    logger.info(f"Total jobs processed: {len(failed_jobs)}")
    logger.info(f"Successfully requeued: {len(results['successful'])}")
    logger.info(f"Failed to requeue: {len(results['failed'])}")
    
    if results["failed"]:
        logger.warning("The following jobs failed to requeue:")
        for job in results["failed"]:
            logger.warning(f"  - {job['task_id']} ({job['document_title']}): {job.get('error', 'Unknown error')}")
    
    # Save results to file
    timestamp = time.strftime("%Y%m%d-%H%M%S")
    results_file = f"kg_rerun_results_{timestamp}.json"
    
    with open(results_file, "w") as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"Results saved to {results_file}")


if __name__ == "__main__":
    main()