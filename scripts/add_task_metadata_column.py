#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to add task_metadata column to processing_tasks table.
"""
import psycopg2
from common.config import settings
from urllib.parse import urlparse

def main():
    """Add task_metadata column to processing_tasks table."""
    print("Parsing database URL...")
    url = urlparse(settings.DATABASE_URL)
    dbname = url.path[1:]
    user = url.username
    password = url.password
    host = url.hostname
    port = url.port

    print("Connecting to database...")
    conn = psycopg2.connect(
        host=host,
        port=port,
        user=user,
        password=password,
        dbname=dbname
    )

    try:
        with conn.cursor() as cursor:
            print("Adding task_metadata column to processing_tasks table...")
            cursor.execute("""
                ALTER TABLE processing_tasks
                ADD COLUMN IF NOT EXISTS task_metadata JSONB;
            """)
            conn.commit()
            print("Column added successfully.")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    main()
