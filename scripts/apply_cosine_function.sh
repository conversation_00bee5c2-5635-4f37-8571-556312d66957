#!/bin/bash
set -e

# Get PostgreSQL container ID
PG_CONTAINER=$(docker ps -q --filter name=postgres)

if [ -z "$PG_CONTAINER" ]; then
  echo "PostgreSQL container not found!"
  exit 1
fi

echo "Applying cosine_similarity function to PostgreSQL..."

# Execute the function creation directly
docker exec -i $PG_CONTAINER psql -U longevity -d longevity << 'EOSQL'
-- Create the cosine_similarity function
CREATE OR REPLACE FUNCTION cosine_similarity(vector1 vector, vector2 numeric[])
RETURNS float AS $$
BEGIN
    -- Convert the numeric array to a vector
    RETURN 1 - (vector1 <=> vector2::vector);
END;
$$ LANGUAGE plpgsql IMMUTABLE STRICT;

-- Create an index on the entities.embedding column if table exists
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'entities') THEN
        IF NOT EXISTS (
            SELECT 1 
            FROM pg_indexes 
            WHERE tablename = 'entities' AND indexname = 'idx_entities_embedding'
        ) THEN
            CREATE INDEX idx_entities_embedding ON entities USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);
            RAISE NOTICE 'Created index on entities.embedding';
        ELSE
            RAISE NOTICE 'Index on entities.embedding already exists';
        END IF;
    ELSE
        RAISE NOTICE 'Table entities does not exist, skipping index creation';
    END IF;
END
$$;
EOSQL

# Verify the function exists
echo "Verifying function creation..."
docker exec -i $PG_CONTAINER psql -U longevity -d longevity -c "\df cosine_similarity"

echo "Migration complete!"
