# Version control
.git
.gitignore
.github

# Python cache files
__pycache__/
**/__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.mypy_cache/

# Virtual environments
env/
venv/
ENV/
env.bak/
venv.bak/
.env
.venv/
**/venv/
**/.venv/

# Logs
*.log
logs/
celerybeat-schedule
celerybeat.pid

# Data directories that shouldn't be copied
data/
**/data/
**/datasets/
**/raw_data/
**/processed_data/
**/interim_data/
**/external_data/
**/features/
**/figures/
**/outputs/
**/results/
**/cache/
**/.cache/
**/checkpoints/
**/checkpoint/
**/logs/
**/temp/
**/tmp/
**/__MACOSX/
**/.Trash/
**/.Trash-*/
**/.DS_Store
**/*.sqlite
**/*.db
**/*.bak

# Jupyter notebooks and checkpoints
**/*.ipynb
**/.ipynb_checkpoints/

# Build and distribution
build/
dist/
*.egg-info/

# Docker
.dockerignore
Dockerfile*
docker-compose*.yml
docker-compose*.yaml

# Documentation
docs/
*.md
!README.md

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Database files
*.db
*.sqlite3

# Large files and media
*.pdf
*.zip
*.tar
*.tar.gz
*.tgz
*.rar
*.7z
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.ico
*.svg
*.mp3
*.mp4
*.wav
*.avi
*.mov
*.mkv
*.flv
*.webm
*.webp
*.psd
*.ai
*.indd
*.eps
**/*.model
**/embeddings/
**/downloads/
**/uploads/
**/media/
**/static/img/
**/static/uploads/
**/static/media/
**/.git/objects/
**/.terraform/

# Local config that shouldn't go into container
.env.local
.env.development.local
.env.test.local
.env.production.local

# Test files
tests/
test_*.py

# Dependency management
pip-lock.txt
poetry.lock

# Temporary files
tmp/
temp/

# Credentials and secrets (except the ones you need)
# Keep gcs_login.json as it's mounted in the docker-compose
*credentials*.json
*secret*.json
*token*.json
*key*.json
!gcs_login.json

# Infrastructure scripts
infra/

# Generated content
kg_rerun_results_*.json
**/*.ipynb_checkpoints/
**/*.parquet
**/*.h5
**/*.hdf5
**/*.pkl
**/*.joblib
**/trained_models/
**/data_processed/
celerybeat-schedule

# Large dataset files
**/*.csv
**/*.tsv
**/*.xlsx
**/*.xls
**/*.json
!package.json
!package-lock.json

# Downloaded model weights and large binary files
**/*.bin
**/*.pth
**/*.pt
**/*.onnx
**/*.tflite
**/*.pb

# Examples (unless needed for runtime)
examples/

# Development artifacts
node_modules/
package-lock.json
yarn.lock
.cache/

# Local development config files
.ipynb_checkpoints/
.npm/
.config/
**/.config/
**/.cache/
