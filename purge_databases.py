#!/usr/bin/env python3
"""
Script to purge all data from PostgreSQL and Neo4j databases.
"""
import os
import sys
from sqlalchemy.orm import Session
from transport.data_transport import DataTransport
from transport.neo4j_client import Neo4jClient
from common.database import Relationship, Entity, KGChunk, Chunk, ProcessingTask, Document
from common.config import settings

def purge_postgres():
    """Purge all data from PostgreSQL."""
    print("Purging PostgreSQL database...")
    with DataTransport() as transport:
        db_session = transport.db_client.db

        # Delete all data in reverse order of dependencies
        print("Deleting relationships...")
        db_session.query(Relationship).delete()

        print("Deleting entities...")
        db_session.query(Entity).delete()

        print("Deleting KG chunks...")
        db_session.query(KGChunk).delete()

        print("Deleting chunks...")
        db_session.query(Chunk).delete()

        print("Deleting processing tasks...")
        db_session.query(ProcessingTask).delete()

        print("Deleting documents...")
        db_session.query(Document).delete()

        db_session.commit()
        print("PostgreSQL database purged successfully.")

def purge_neo4j():
    """Purge all data from Neo4j."""
    print("Purging Neo4j database...")
    neo4j_client = Neo4jClient()

    try:
        # Delete all nodes and relationships
        neo4j_client._driver.session().run("MATCH (n) DETACH DELETE n")
        print("Neo4j database purged successfully.")
    finally:
        # Close client
        neo4j_client.close()

def main():
    """Main function."""
    try:
        # Settings are now automatically loaded based on APP_ENV
        # Manual overrides for HOST, PORT, and URLs have been removed.
        # The Settings object from common.config will provide the correct values.

        print(f"Purging data based on APP_ENV: {settings.APP_ENV}")
        print(f"Using MinIO at {settings.STORAGE_URL}")
        print(f"Using PostgreSQL at {settings.POSTGRES_HOST}:{settings.POSTGRES_PORT} (DB: {settings.POSTGRES_DB})")
        print(f"Using Neo4j at {settings.NEO4J_HOST}:{settings.NEO4J_PORT}")

        purge_postgres()
        purge_neo4j()
        print("All databases purged successfully.")
        return 0
    except Exception as e:
        print(f"Error purging databases: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
