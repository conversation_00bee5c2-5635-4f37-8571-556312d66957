#!/usr/bin/env python
"""
Quick system test script that assumes Docker services are already running.
This script will:
1. Clean databases
2. Ingest sample documents
3. Monitor processing
4. Verify results
"""
import os
import sys
import time
import logging
import requests
import subprocess
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Constants
API_URL = "http://localhost:8000"
TEST_DATA_DIR = Path("tests/data")
SAMPLE_FILES = [
    "sample_txt.txt",
    "sample_epub.epub", 
    "sample_pdf.pdf",
    "sample_url.txt"
]

def check_services():
    """Check if services are running."""
    logger.info("Checking if services are running...")
    
    try:
        response = requests.get(f"{API_URL}/health", timeout=5)
        if response.status_code == 200:
            logger.info("✅ API service is running")
            return True
        else:
            logger.error(f"❌ API service returned status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        logger.error(f"❌ API service is not accessible: {e}")
        return False

def clean_databases():
    """Clean databases using the purge script."""
    logger.info("🧹 Cleaning databases...")
    
    try:
        result = subprocess.run(
            ["python", "purge_db_direct.py"],
            check=True,
            capture_output=True,
            text=True,
            cwd=os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
        )
        logger.info("✅ Databases cleaned successfully")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Failed to clean databases: {e}")
        logger.error(f"stdout: {e.stdout}")
        logger.error(f"stderr: {e.stderr}")
        return False

def ingest_pubmed_articles():
    """Ingest PubMed articles."""
    logger.info("📚 Ingesting PubMed articles...")
    
    try:
        url = f"{API_URL}/api/pubmed/insert_articles_with_query"
        payload = {
            "query": "longevity diet",
            "max_results": 2
        }
        
        response = requests.post(url, json=payload, timeout=60)
        response.raise_for_status()
        
        result = response.json()
        task_ids = result.get("task_ids", [])
        logger.info(f"✅ PubMed ingestion started with {len(task_ids)} tasks")
        return task_ids
        
    except requests.exceptions.RequestException as e:
        logger.error(f"❌ Failed to ingest PubMed articles: {e}")
        return []

def ingest_sample_files():
    """Ingest sample files."""
    logger.info("📁 Ingesting sample files...")
    
    task_ids = []
    success_count = 0
    
    for filename in SAMPLE_FILES:
        file_path = TEST_DATA_DIR / filename
        
        if not file_path.exists():
            logger.warning(f"⚠️  Sample file not found: {file_path}")
            continue
        
        try:
            if filename == "sample_url.txt":
                # Handle URL file specially
                with open(file_path, 'r') as f:
                    url_to_ingest = f.read().strip()
                
                if url_to_ingest:
                    logger.info(f"📄 Ingesting URL: {url_to_ingest}")
                    api_url = f"{API_URL}/api/documents/ingest"
                    data = {'url': url_to_ingest}
                    response = requests.post(api_url, data=data, timeout=120)
                else:
                    logger.warning(f"⚠️  No URL found in {filename}")
                    continue
            else:
                # Handle regular file upload
                logger.info(f"📄 Ingesting file: {filename}")
                api_url = f"{API_URL}/api/documents/ingest"
                with open(file_path, 'rb') as f:
                    files = {'file': f}
                    response = requests.post(api_url, files=files, timeout=120)
            
            response.raise_for_status()
            result = response.json()
            task_id = result.get("task_id")
            
            if task_id:
                task_ids.append(task_id)
                success_count += 1
                logger.info(f"✅ {filename} ingestion started with task ID: {task_id}")
            else:
                logger.warning(f"⚠️  No task ID returned for {filename}")
                
        except Exception as e:
            logger.error(f"❌ Error ingesting {filename}: {e}")
    
    logger.info(f"✅ Successfully started ingestion for {success_count}/{len(SAMPLE_FILES)} files")
    return task_ids

def wait_for_processing(timeout=1800):
    """Wait for processing to complete."""
    logger.info("⏳ Waiting for processing to complete...")
    
    start_time = time.time()
    check_interval = 30
    
    while time.time() - start_time < timeout:
        try:
            # Simple check - just wait and then verify data exists
            logger.info(f"⏳ Processing... ({int(time.time() - start_time)}s elapsed)")
            time.sleep(check_interval)
            
            # After some time, check if we have data
            if time.time() - start_time > 300:  # After 5 minutes, start checking
                if check_data_exists():
                    logger.info("✅ Processing appears to be complete!")
                    return True
                    
        except Exception as e:
            logger.warning(f"⚠️  Error during processing check: {e}")
    
    logger.warning(f"⚠️  Processing timeout after {timeout} seconds")
    return False

def check_data_exists():
    """Simple check to see if any data exists."""
    try:
        # Check if API is responsive
        response = requests.get(f"{API_URL}/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def verify_results():
    """Verify the ingestion results."""
    logger.info("🔍 Verifying results...")
    
    # For now, just check that the API is still responsive
    try:
        response = requests.get(f"{API_URL}/health", timeout=5)
        if response.status_code == 200:
            logger.info("✅ API is responsive - basic verification passed")
            return True
        else:
            logger.error(f"❌ API returned status {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ Verification failed: {e}")
        return False

def main():
    """Main function."""
    logger.info("🚀 Starting Quick System Test")
    logger.info("=" * 50)
    
    # Step 1: Check services
    if not check_services():
        logger.error("❌ Services are not running. Please start Docker services first.")
        return 1
    
    # Step 2: Clean databases
    if not clean_databases():
        logger.error("❌ Failed to clean databases")
        return 1
    
    # Step 3: Ingest documents
    pubmed_tasks = ingest_pubmed_articles()
    file_tasks = ingest_sample_files()
    
    all_tasks = pubmed_tasks + file_tasks
    if not all_tasks:
        logger.error("❌ No ingestion tasks were started")
        return 1
    
    logger.info(f"📊 Started {len(all_tasks)} ingestion tasks")
    
    # Step 4: Wait for processing
    if not wait_for_processing():
        logger.warning("⚠️  Processing may not have completed, but continuing with verification...")
    
    # Step 5: Verify results
    if verify_results():
        logger.info("🎉 QUICK SYSTEM TEST PASSED!")
        return 0
    else:
        logger.error("❌ QUICK SYSTEM TEST FAILED!")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
