#!/usr/bin/env python3
"""
Test script for processing Vertex AI batch response files.

This script parses a Vertex AI batch response file and prints the entities and relationships.
It can be used to verify that the response format is correct and that the entities and
relationships are being extracted correctly.

Usage:
    python test_vertex_ai_batch_response.py --file /path/to/response.jsonl
"""
import os
import sys
import json
import argparse
from pathlib import Path

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from services.kg_entity_relationship_processor import EntityRelationshipProcessor

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Test Vertex AI batch response processing")
    parser.add_argument("--file", type=str, default="tests/data/sample_vertex_ai_batch_response.jsonl",
                        help="Path to the Vertex AI batch response file")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    parser.add_argument("--output", type=str, help="Path to save the parsed results as JSON")
    return parser.parse_args()

def main():
    """Main function."""
    args = parse_args()
    
    # Check if the file exists
    if not os.path.exists(args.file):
        print(f"Error: File not found: {args.file}")
        return 1
    
    print(f"Processing file: {args.file}")
    
    # Create an EntityRelationshipProcessor
    processor = EntityRelationshipProcessor()
    
    # Parse the Vertex AI batch response
    results = processor.parse_vertex_ai_response(args.file)
    
    # Print the results
    print(f"Parsed {len(results)} chunks from the batch response")
    
    # Count total entities and relationships
    total_entities = sum(len(result.get("entities", [])) for result in results)
    total_relationships = sum(len(result.get("relationships", [])) for result in results)
    
    print(f"Total entities: {total_entities}")
    print(f"Total relationships: {total_relationships}")
    
    if args.verbose:
        # Print details for each chunk
        for i, result in enumerate(results):
            chunk_id = result.get("chunk_id")
            entities = result.get("entities", [])
            relationships = result.get("relationships", [])
            
            print(f"\nChunk {i+1}: {chunk_id}")
            print(f"  Entities: {len(entities)}")
            
            # Print entity details
            for j, entity in enumerate(entities[:5]):  # Show only first 5 entities
                print(f"    Entity {j+1}: {entity.get('text')} ({entity.get('type')})")
            
            if len(entities) > 5:
                print(f"    ... and {len(entities) - 5} more entities")
            
            print(f"  Relationships: {len(relationships)}")
            
            # Print relationship details
            for j, rel in enumerate(relationships[:5]):  # Show only first 5 relationships
                print(f"    Relationship {j+1}: {rel.get('source_text')} --{rel.get('type')}--> {rel.get('target_text')}")
            
            if len(relationships) > 5:
                print(f"    ... and {len(relationships) - 5} more relationships")
    
    # Save the results to a file if requested
    if args.output:
        output_dir = os.path.dirname(args.output)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        with open(args.output, "w") as f:
            json.dump(results, f, indent=2)
        
        print(f"Saved parsed results to: {args.output}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
