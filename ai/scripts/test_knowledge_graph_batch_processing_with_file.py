#!/usr/bin/env python3
"""
Test script for knowledge graph batch processing with a real batch response file.
"""
import os
import sys
import json
import uuid
import argparse
from pathlib import Path
from unittest.mock import MagicMock, patch

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from services.kg_entity_relationship_processor import EntityRelationshipProcessor
from transport.vertex_ai_batch_client import VertexAIBatchClient
from services.knowledge_graph_service import KnowledgeGraphService

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Test knowledge graph batch processing with a real batch response file")
    parser.add_argument("--file", type=str, default="/Users/<USER>/Repos/LongevityCo/bath_response_latest.jsonl",
                        help="Path to the Vertex AI batch response file")
    parser.add_argument("--document-id", type=str, help="Document ID to use (if not provided, a new document will be created)")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    return parser.parse_args()

def main():
    """Main function."""
    args = parse_args()

    # Check if the file exists
    if not os.path.exists(args.file):
        print(f"Error: File not found: {args.file}")
        return 1

    print(f"Processing file: {args.file}")

    # Create an EntityRelationshipProcessor
    processor = EntityRelationshipProcessor()

    # Parse the Vertex AI batch response
    results = processor.parse_vertex_ai_response(args.file)

    # Print the results
    print(f"Parsed {len(results)} chunks from the batch response")

    # Count total entities and relationships
    total_entities = sum(len(result.get("entities", [])) for result in results)
    total_relationships = sum(len(result.get("relationships", [])) for result in results)

    print(f"Total entities: {total_entities}")
    print(f"Total relationships: {total_relationships}")

    # Create a document ID if not provided
    document_id = args.document_id if args.document_id else str(uuid.uuid4())
    print(f"Using document ID: {document_id}")

    # Create a mock VertexAIBatchClient that returns our parsed results
    class MockVertexAIBatchClient(VertexAIBatchClient):
        def __init__(self):
            # Skip initialization to avoid connecting to real services
            pass

        def get_batch_results(self, job_id_or_job, chunks=None):
            print(f"Mock get_batch_results called with job_id: {job_id_or_job}")
            return results

    # Create mock objects for testing
    mock_document = MagicMock()
    mock_document.id = document_id

    mock_task = MagicMock()
    task_id = str(uuid.uuid4())
    mock_task.id = task_id
    mock_task.document_id = document_id
    mock_task.task_type = "knowledge_graph_building"
    mock_task.status = "processing"
    mock_task.result = {"batch_job_id": f"projects/test-project/locations/us-central1/batchPredictionJobs/test-batch-{uuid.uuid4().hex[:8]}", "async_processing": True}

    # Create a mock DataTransport
    mock_transport = MagicMock()
    mock_transport.db_client.get_document.return_value = mock_document
    mock_transport.db_client.get_processing_task.return_value = mock_task
    mock_transport.get_document_chunks.return_value = []

    # Create a KnowledgeGraphService with our mock client
    with patch('services.knowledge_graph_service.DataTransport', return_value=mock_transport):
        kg_service = KnowledgeGraphService()
        kg_service.vertex_client = MockVertexAIBatchClient()

        # Process the batch results
        print("Processing batch results...")

        # Mock the continue_knowledge_graph_building method
        original_method = kg_service.continue_knowledge_graph_building

        def mock_continue_knowledge_graph_building(task_id):
            print(f"Mock continue_knowledge_graph_building called with task_id: {task_id}")
            # Process the batch results directly
            entities, relationships = processor.process_batch_results(results, document_id)
            print(f"Processed {len(entities)} entities and {len(relationships)} relationships")
            return {"status": "success", "entities_count": len(entities), "relationships_count": len(relationships)}

        # Replace the method with our mock
        kg_service.continue_knowledge_graph_building = mock_continue_knowledge_graph_building

        # Call the method
        result = kg_service.continue_knowledge_graph_building(task_id)
        print(f"Result: {result}")

    return 0

if __name__ == "__main__":
    sys.exit(main())
