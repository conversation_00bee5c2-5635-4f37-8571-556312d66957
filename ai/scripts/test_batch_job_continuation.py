#!/usr/bin/env python
"""
Test script for batch job status checking and continuation logic.

This script simulates the flow of checking batch job status and continuing
knowledge graph building when a batch job completes.

Usage:
    python ai/scripts/test_batch_job_continuation.py --document_id <document_id>
"""

import argparse
import json
import logging
import sys
import time
import uuid
from typing import Dict, Any, Optional, List, Union

# Add parent directory to path
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from common.database import ProcessingTask
from services.knowledge_graph_service import KnowledgeGraphService
from transport.data_transport import DataTransport
from transport.vertex_ai_batch_client import VertexAIBatchClient
from google.cloud import aiplatform

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_mock_batch_job(document_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
    """
    Create a mock batch job for testing.
    
    Args:
        document_id: Document ID
        
    Returns:
        Dict[str, Any]: Mock batch job info
    """
    logger.info(f"Creating mock batch job for document: {document_id}")
    
    # Create a task ID
    task_id = uuid.uuid4()
    
    # Create a mock batch job ID
    batch_job_id = f"projects/test-project/locations/us-central1/batchPredictionJobs/test-batch-{uuid.uuid4().hex[:8]}"
    
    # Create a processing task
    with DataTransport() as transport:
        # Create processing task
        task = transport.db_client.create_processing_task(
            document_id=document_id,
            task_type="knowledge_graph_building",
            celery_task_id=None
        )
        
        # Update task with batch job info
        transport.update_task_status(
            task_id=task.id,
            status="batch_job_submitted",
            result={
                "batch_job_id": batch_job_id,
                "status": "submitted",
                "submitted_at": time.time(),
                "async_processing": True
            }
        )
        
        logger.info(f"Created mock batch job: {batch_job_id}")
        logger.info(f"Task ID: {task.id}")
        
        return {
            "task_id": str(task.id),
            "document_id": str(document_id),
            "batch_job_id": batch_job_id
        }


def simulate_batch_job_completion(task_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
    """
    Simulate batch job completion by updating the task status.
    
    Args:
        task_id: Processing task ID
        
    Returns:
        Dict[str, Any]: Updated task info
    """
    logger.info(f"Simulating batch job completion for task: {task_id}")
    
    with DataTransport() as transport:
        # Get task
        task = transport.db_client.get_processing_task(task_id)
        if not task:
            logger.error(f"Task {task_id} not found")
            return {"status": "error", "message": f"Task {task_id} not found"}
        
        # Get batch job ID
        batch_job_id = task.result.get("batch_job_id") if task.result else None
        if not batch_job_id:
            logger.error(f"No batch job ID found in task {task_id}")
            return {"status": "error", "message": "No batch job ID found in task"}
        
        # Update task status to ready_to_continue
        transport.update_task_status(
            task_id=task_id,
            status="ready_to_continue",
            result={
                **task.result,
                "status": "ready_to_continue",
                "completed_at": time.time()
            }
        )
        
        logger.info(f"Updated task status to ready_to_continue")
        
        # Get updated task
        updated_task = transport.db_client.get_processing_task(task_id)
        
        return {
            "task_id": str(updated_task.id),
            "document_id": str(updated_task.document_id),
            "status": updated_task.status,
            "result": updated_task.result
        }


def create_mock_batch_results(task_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
    """
    Create mock batch results for testing.
    
    Args:
        task_id: Processing task ID
        
    Returns:
        Dict[str, Any]: Mock batch results
    """
    logger.info(f"Creating mock batch results for task: {task_id}")
    
    with DataTransport() as transport:
        # Get task
        task = transport.db_client.get_processing_task(task_id)
        if not task:
            logger.error(f"Task {task_id} not found")
            return {"status": "error", "message": f"Task {task_id} not found"}
        
        # Get document ID
        document_id = task.document_id
        
        # Get chunks
        chunks = transport.get_document_chunks(document_id, chunk_type="kg")
        if not chunks:
            logger.error(f"No chunks found for document {document_id}")
            return {"status": "error", "message": f"No chunks found for document {document_id}"}
        
        # Create mock batch results
        mock_results = []
        for chunk in chunks:
            # Create mock entities (2-3 per chunk)
            entities = []
            for i in range(2, 5):
                entity_id = f"entity-{uuid.uuid4().hex[:8]}"
                entities.append({
                    "id": entity_id,
                    "name": f"Test Entity {i}",
                    "type": "TEST_ENTITY",
                    "metadata": {
                        "confidence": 0.9,
                        "source": "mock"
                    }
                })
            
            # Create mock relationships (1-2 per chunk)
            relationships = []
            for i in range(1, 3):
                if len(entities) >= 2:
                    relationships.append({
                        "source": entities[0]["id"],
                        "target": entities[1]["id"],
                        "type": "TEST_RELATIONSHIP",
                        "metadata": {
                            "confidence": 0.8,
                            "source": "mock"
                        }
                    })
            
            # Add to mock results
            mock_results.append({
                "chunk_id": chunk["id"],
                "entities": entities,
                "relationships": relationships
            })
        
        # Update task with mock batch results
        transport.update_task_status(
            task_id=task_id,
            status="ready_to_continue",
            result={
                **task.result,
                "batch_results": json.dumps(mock_results)
            }
        )
        
        logger.info(f"Created mock batch results with {len(mock_results)} chunks")
        
        return {
            "status": "success",
            "task_id": str(task_id),
            "document_id": str(document_id),
            "chunk_count": len(chunks),
            "result_count": len(mock_results)
        }


def test_check_and_continue_batch_jobs() -> Dict[str, Any]:
    """
    Test the check_and_continue_batch_jobs function.
    
    Returns:
        Dict[str, Any]: Test result
    """
    logger.info("Testing check_and_continue_batch_jobs function")
    
    # Create KnowledgeGraphService instance
    service = KnowledgeGraphService()
    
    # Patch the get_batch_job_state method to always return JOB_STATE_SUCCEEDED
    original_get_batch_job_state = VertexAIBatchClient.get_batch_job_state
    
    def mock_get_batch_job_state(self, batch_job_id):
        logger.info(f"Mock get_batch_job_state called for {batch_job_id}")
        return aiplatform.JobState.JOB_STATE_SUCCEEDED
    
    try:
        # Apply the patch
        VertexAIBatchClient.get_batch_job_state = mock_get_batch_job_state
        
        # Run the check_and_continue_batch_jobs function
        result = service.check_and_continue_batch_jobs()
        
        logger.info(f"check_and_continue_batch_jobs result: {json.dumps(result, indent=2)}")
        return result
    finally:
        # Restore the original method
        VertexAIBatchClient.get_batch_job_state = original_get_batch_job_state


def test_continue_knowledge_graph_building(task_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
    """
    Test the continue_knowledge_graph_building function.
    
    Args:
        task_id: Processing task ID
        
    Returns:
        Dict[str, Any]: Test result
    """
    logger.info(f"Testing continue_knowledge_graph_building for task: {task_id}")
    
    # Create KnowledgeGraphService instance
    service = KnowledgeGraphService()
    
    # Continue knowledge graph building
    result = service.continue_knowledge_graph_building(task_id)
    
    logger.info(f"continue_knowledge_graph_building result: {json.dumps(result, indent=2)}")
    return result


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Test Batch Job Continuation")
    parser.add_argument("--document_id", required=True, help="Document ID to use for testing")
    parser.add_argument("--create_mock", action="store_true", help="Create mock batch job")
    parser.add_argument("--simulate_completion", help="Simulate batch job completion for task ID")
    parser.add_argument("--create_results", help="Create mock batch results for task ID")
    parser.add_argument("--test_check", action="store_true", help="Test check_and_continue_batch_jobs")
    parser.add_argument("--test_continue", help="Test continue_knowledge_graph_building for task ID")
    
    args = parser.parse_args()
    
    if args.create_mock:
        # Create mock batch job
        result = create_mock_batch_job(args.document_id)
        print(f"Created mock batch job: {json.dumps(result, indent=2)}")
    
    if args.simulate_completion:
        # Simulate batch job completion
        result = simulate_batch_job_completion(args.simulate_completion)
        print(f"Simulated batch job completion: {json.dumps(result, indent=2)}")
    
    if args.create_results:
        # Create mock batch results
        result = create_mock_batch_results(args.create_results)
        print(f"Created mock batch results: {json.dumps(result, indent=2)}")
    
    if args.test_check:
        # Test check_and_continue_batch_jobs
        result = test_check_and_continue_batch_jobs()
        print(f"Test check_and_continue_batch_jobs result: {json.dumps(result, indent=2)}")
    
    if args.test_continue:
        # Test continue_knowledge_graph_building
        result = test_continue_knowledge_graph_building(args.test_continue)
        print(f"Test continue_knowledge_graph_building result: {json.dumps(result, indent=2)}")


if __name__ == "__main__":
    main()
