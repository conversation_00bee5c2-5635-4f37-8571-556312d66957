#!/usr/bin/env python
"""
Monitor script for knowledge graph batch jobs.

This script monitors the status of knowledge graph batch jobs in the database
and provides information about their progress.

Usage:
    python ai/scripts/monitor_batch_jobs.py
    python ai/scripts/monitor_batch_jobs.py --watch
"""

import argparse
import json
import logging
import sys
import time
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional

# Add parent directory to path
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from common.database import ProcessingTask
from transport.data_transport import DataTransport
from transport.vertex_ai_batch_client import VertexAIBatchClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def get_batch_jobs(status_filter: Optional[List[str]] = None) -> List[Dict[str, Any]]:
    """
    Get batch jobs from the database.
    
    Args:
        status_filter: Optional list of statuses to filter by
        
    Returns:
        List[Dict[str, Any]]: List of batch jobs
    """
    logger.info(f"Getting batch jobs with status filter: {status_filter}")
    
    with DataTransport() as transport:
        # Build query
        query = transport.db_client.db.query(ProcessingTask).filter(
            ProcessingTask.task_type == "knowledge_graph_building"
        )
        
        # Apply status filter if provided
        if status_filter:
            query = query.filter(ProcessingTask.status.in_(status_filter))
        
        # Order by created_at
        query = query.order_by(ProcessingTask.created_at.desc())
        
        # Execute query
        tasks = query.all()
        
        # Convert to dictionaries
        batch_jobs = []
        for task in tasks:
            # Check if this is a batch job
            if task.result and task.result.get("batch_job_id"):
                batch_job_id = task.result.get("batch_job_id")
                submitted_at = task.result.get("submitted_at")
                completed_at = task.result.get("completed_at")
                
                # Calculate duration if both timestamps are available
                duration = None
                if submitted_at and completed_at:
                    duration = completed_at - submitted_at
                
                # Format timestamps
                if submitted_at:
                    submitted_at = datetime.fromtimestamp(submitted_at, tz=timezone.utc).isoformat()
                if completed_at:
                    completed_at = datetime.fromtimestamp(completed_at, tz=timezone.utc).isoformat()
                
                batch_jobs.append({
                    "id": str(task.id),
                    "document_id": str(task.document_id),
                    "status": task.status,
                    "batch_job_id": batch_job_id,
                    "submitted_at": submitted_at,
                    "completed_at": completed_at,
                    "duration": duration,
                    "created_at": task.created_at.isoformat() if task.created_at else None,
                    "updated_at": task.updated_at.isoformat() if task.updated_at else None
                })
        
        logger.info(f"Found {len(batch_jobs)} batch jobs")
        return batch_jobs


def check_vertex_ai_batch_job(batch_job_id: str) -> Optional[str]:
    """
    Check the status of a Vertex AI batch job directly.
    
    Args:
        batch_job_id: Batch job ID
        
    Returns:
        Optional[str]: Job state or None if error
    """
    logger.info(f"Checking Vertex AI batch job status: {batch_job_id}")
    
    try:
        # Create VertexAIBatchClient instance
        client = VertexAIBatchClient()
        
        # Get batch job state
        job_state = client.get_batch_job_state(batch_job_id)
        
        logger.info(f"Batch job state: {job_state}")
        return job_state
    except Exception as e:
        logger.error(f"Error checking batch job status: {e}")
        return None


def print_batch_job_summary(batch_jobs: List[Dict[str, Any]]):
    """
    Print a summary of batch jobs.
    
    Args:
        batch_jobs: List of batch jobs
    """
    if not batch_jobs:
        print("No batch jobs found")
        return
    
    print(f"Found {len(batch_jobs)} batch jobs:")
    print("")
    
    # Group by status
    status_groups = {}
    for job in batch_jobs:
        status = job["status"]
        if status not in status_groups:
            status_groups[status] = []
        status_groups[status].append(job)
    
    # Print summary by status
    for status, jobs in status_groups.items():
        print(f"{status}: {len(jobs)} jobs")
    
    print("")
    
    # Print details for each job
    for job in batch_jobs:
        print(f"Task ID: {job['id']}")
        print(f"Document ID: {job['document_id']}")
        print(f"Status: {job['status']}")
        print(f"Batch Job ID: {job['batch_job_id']}")
        print(f"Submitted At: {job['submitted_at']}")
        print(f"Completed At: {job['completed_at']}")
        if job['duration'] is not None:
            print(f"Duration: {job['duration']:.2f} seconds")
        print(f"Created At: {job['created_at']}")
        print(f"Updated At: {job['updated_at']}")
        
        # Check Vertex AI batch job status
        if job['batch_job_id'] and job['status'] in ['batch_job_submitted', 'processing']:
            job_state = check_vertex_ai_batch_job(job['batch_job_id'])
            print(f"Vertex AI Job State: {job_state}")
        
        print("")


def watch_batch_jobs(interval: int = 10):
    """
    Watch batch jobs in real-time.
    
    Args:
        interval: Interval in seconds between updates
    """
    try:
        while True:
            # Clear screen
            os.system('cls' if os.name == 'nt' else 'clear')
            
            # Get batch jobs
            batch_jobs = get_batch_jobs()
            
            # Print timestamp
            print(f"Last updated: {datetime.now().isoformat()}")
            print("")
            
            # Print summary
            print_batch_job_summary(batch_jobs)
            
            # Wait for next update
            print(f"Updating in {interval} seconds... (Press Ctrl+C to exit)")
            time.sleep(interval)
    except KeyboardInterrupt:
        print("\nExiting...")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Monitor Knowledge Graph Batch Jobs")
    parser.add_argument("--watch", action="store_true", help="Watch batch jobs in real-time")
    parser.add_argument("--interval", type=int, default=10, help="Interval in seconds between updates (for watch mode)")
    parser.add_argument("--status", nargs="+", help="Filter by status")
    
    args = parser.parse_args()
    
    if args.watch:
        # Watch batch jobs
        watch_batch_jobs(args.interval)
    else:
        # Get batch jobs
        batch_jobs = get_batch_jobs(args.status)
        
        # Print summary
        print_batch_job_summary(batch_jobs)


if __name__ == "__main__":
    main()
