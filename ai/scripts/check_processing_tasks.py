#!/usr/bin/env python
"""
Script to check processing tasks in the database.
"""
import sys
import logging
from common.database import ProcessingTask
from transport.data_transport import DataTransport

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_processing_tasks():
    """
    Check processing tasks in the database.
    """
    with DataTransport() as transport:
        # Get all processing tasks
        tasks = transport.db_client.db.query(ProcessingTask).all()
        logger.info(f"Found {len(tasks)} total tasks")
        
        # Get processing tasks
        processing_tasks = transport.db_client.db.query(ProcessingTask).filter(
            ProcessingTask.status == "processing"
        ).all()
        logger.info(f"Found {len(processing_tasks)} processing tasks")
        
        # Get ready_to_continue tasks
        ready_tasks = transport.db_client.db.query(ProcessingTask).filter(
            ProcessingTask.status == "ready_to_continue"
        ).all()
        logger.info(f"Found {len(ready_tasks)} ready_to_continue tasks")
        
        # Print details of all tasks
        for task in tasks:
            logger.info(f"Task: {task.id}, Document: {task.document_id}, Type: {task.task_type}, Status: {task.status}")
            logger.info(f"Result: {task.result}")
            logger.info("---")

if __name__ == "__main__":
    check_processing_tasks()
