#!/usr/bin/env python
"""
Script to check entities and relationships in Neo4j.
"""
import sys
import logging
from transport.data_transport import DataTransport

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_neo4j(document_id):
    """
    Check entities and relationships in Neo4j.
    """
    with DataTransport() as transport:
        # Get entities
        with transport.neo4j_client._driver.session() as session:
            query = """
            MATCH (e:Entity)-[:MENTIONED_IN]->(c:Chunk)-[:PART_OF]->(d:Document {id: $document_id})
            RETURN e.id, e.text, e.type, c.id
            LIMIT 10
            """
            result = session.run(query, document_id=document_id)
            results = list(result)
            logger.info(f"Found {len(results)} entities for document {document_id}")

            # Print entities
            logger.info("Entities:")
            for record in results:
                logger.info(f"  {record['e.text']} ({record['e.type']}) - Chunk: {record['c.id']}")

            # Get relationships
            query = """
            MATCH (e1:Entity)-[r]->(e2:Entity)
            WHERE (e1)-[:MENTIONED_IN]->(:Chunk)-[:PART_OF]->(:Document {id: $document_id})
            RETURN e1.text, type(r) as rel_type, e2.text
            LIMIT 10
            """
            result = session.run(query, document_id=document_id)
            results = list(result)
            logger.info(f"Found {len(results)} relationships for document {document_id}")

            # Print relationships
            logger.info("Relationships:")
            for record in results:
                logger.info(f"  {record['e1.text']} --{record['rel_type']}--> {record['e2.text']}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python check_neo4j.py <document_id>")
        sys.exit(1)

    document_id = sys.argv[1]
    check_neo4j(document_id)
