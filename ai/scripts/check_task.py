#!/usr/bin/env python
"""
Script to check a specific processing task in the database.
"""
import sys
import logging
from common.database import ProcessingTask
from transport.data_transport import DataTransport

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_task(task_id):
    """
    Check a specific processing task in the database.
    """
    with DataTransport() as transport:
        # Get the task
        task = transport.db_client.db.query(ProcessingTask).filter(
            ProcessingTask.id == task_id
        ).first()
        
        if not task:
            logger.error(f"Task {task_id} not found")
            return
        
        logger.info(f"Task: {task.id}, Document: {task.document_id}, Type: {task.task_type}, Status: {task.status}")
        logger.info(f"Result keys: {task.result.keys() if task.result else None}")
        logger.info(f"Result: {task.result}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python check_task.py <task_id>")
        sys.exit(1)
    
    task_id = sys.argv[1]
    check_task(task_id)
