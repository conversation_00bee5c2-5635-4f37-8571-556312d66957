#!/usr/bin/env python
"""
Helper script to test the knowledge graph batch processing functionality.

This script:
1. Creates a test document in the database
2. Creates a test processing task
3. Simulates a completed Vertex AI batch job
4. Calls the continue_knowledge_graph_building method
5. Verifies the results

Usage:
    python ai/scripts/test_knowledge_graph_batch_processing.py

"""
import os
import sys
import uuid
import json
import tempfile
import argparse
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

from services.knowledge_graph_service import KnowledgeGraphService
from services.kg_entity_relationship_processor import EntityRelationshipProcessor
from transport.vertex_ai_batch_client import VertexAIBatchClient
from transport.data_transport import DataTransport


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Test knowledge graph batch processing")
    parser.add_argument("--sample-file", type=str,
                        default=str(Path(__file__).parent.parent.parent / "tests" / "data" / "sample_vertex_ai_batch_response.jsonl"),
                        help="Path to sample Vertex AI batch response file")
    parser.add_argument("--document-id", type=str,
                        help="Document ID to use (if not provided, a new document will be created)")
    parser.add_argument("--task-id", type=str,
                        help="Task ID to use (if not provided, a new task will be created)")
    parser.add_argument("--cleanup", action="store_true",
                        help="Clean up test data after running")
    parser.add_argument("--verbose", action="store_true",
                        help="Print verbose output")

    return parser.parse_args()


def main():
    """Run the test."""
    args = parse_args()

    # Override settings for testing
    from common.config import settings
    settings.MINIO_HOST = "localhost"  # Connect to localhost from outside Docker
    settings.MINIO_PORT = 9003  # Mapped port from docker-compose.test.yml
    settings.STORAGE_URL = f"http://{settings.MINIO_HOST}:{settings.MINIO_PORT}"

    # Load the sample response
    sample_file = Path(args.sample_file)
    if not sample_file.exists():
        print(f"Sample file {sample_file} not found")
        return 1

    with open(sample_file, 'r') as f:
        sample_response = f.read()

    # Create a temporary file with the sample response
    with tempfile.NamedTemporaryFile(mode='w', delete=False) as temp_file:
        temp_file.write(sample_response)
        temp_file_path = temp_file.name

    try:
        # Connect to the database
        with DataTransport() as transport:
            # Create or use the provided document ID
            if args.document_id:
                document_id = uuid.UUID(args.document_id)
                document = transport.db_client.get_document(document_id)
                if not document:
                    print(f"Document {document_id} not found, creating a new one")
                    document_created = True
                    transport.db_client.create_document(
                        id=document_id,
                        title="Test Document",
                        content="Test content for knowledge graph batch processing",
                        source_url="https://example.com/test",
                        document_type="text"
                    )
                else:
                    document_created = False
                    print(f"Using existing document {document_id}")
            else:
                document_id = uuid.uuid4()
                document_created = True
                print(f"Creating new document {document_id}")
                transport.db_client.create_document(
                    id=document_id,
                    title="Test Document",
                    content="Test content for knowledge graph batch processing",
                    source_url="https://example.com/test",
                    document_type="text"
                )

            # Create or use the provided task ID
            if args.task_id:
                task_id = uuid.UUID(args.task_id)
                task = transport.db_client.get_processing_task(task_id)
                if not task:
                    print(f"Task {task_id} not found, creating a new one")
                    task_created = True
                    batch_job_id = f"projects/test-project/locations/us-central1/batchPredictionJobs/test-batch-{uuid.uuid4().hex[:8]}"
                    transport.db_client.create_processing_task(
                        id=task_id,
                        document_id=document_id,
                        task_type="knowledge_graph",
                        status="ready_to_continue",
                        result={"batch_job_id": batch_job_id}
                    )
                else:
                    task_created = False
                    print(f"Using existing task {task_id}")
                    # Update the task status
                    transport.update_task_status(
                        task_id=task_id,
                        status="ready_to_continue"
                    )
            else:
                task_id = uuid.uuid4()
                task_created = True
                print(f"Creating new task {task_id}")
                batch_job_id = f"projects/test-project/locations/us-central1/batchPredictionJobs/test-batch-{uuid.uuid4().hex[:8]}"
                transport.db_client.create_processing_task(
                    id=task_id,
                    document_id=document_id,
                    task_type="knowledge_graph",
                    status="ready_to_continue",
                    result={"batch_job_id": batch_job_id}
                )

        # Process the batch results
        processor = EntityRelationshipProcessor()
        results = processor.parse_vertex_ai_response(temp_file_path)

        if args.verbose:
            print(f"Parsed {len(results)} results from the sample response")
            for i, result in enumerate(results):
                print(f"Result {i+1}:")
                print(f"  Chunk ID: {result.get('chunk_id')}")
                print(f"  Entities: {len(result.get('entities', []))}")
                print(f"  Relationships: {len(result.get('relationships', []))}")

        # Mock the VertexAIBatchClient to return our sample results
        vertex_client = VertexAIBatchClient()

        # Patch the get_batch_results method
        original_get_batch_results = vertex_client.get_batch_results

        def mock_get_batch_results(batch_job_id):
            return results

        vertex_client.get_batch_results = mock_get_batch_results

        try:
            # Create the service with our mocked client
            service = KnowledgeGraphService()

            # Patch the service to use our mocked client
            service.vertex_client = vertex_client

            # Call the method
            print(f"Calling continue_knowledge_graph_building with task ID {task_id}")
            result = service.continue_knowledge_graph_building(str(task_id))

            # Print the result
            print("Result:")
            print(json.dumps(result, indent=2))

            # Verify the task status was updated
            with DataTransport() as transport:
                task = transport.db_client.get_processing_task(task_id)
                print(f"Task status: {task.status}")

                # Get the entities and relationships
                entities = transport.db_client.get_entities_by_document(document_id)
                print(f"Entities: {len(entities)}")

                if args.verbose and entities:
                    for i, entity in enumerate(entities[:5]):  # Show first 5 entities
                        print(f"Entity {i+1}: {entity.text} ({entity.entity_type})")

                    if len(entities) > 5:
                        print(f"... and {len(entities) - 5} more entities")
        finally:
            # Restore the original method
            vertex_client.get_batch_results = original_get_batch_results
    finally:
        # Clean up the temporary file
        os.unlink(temp_file_path)

        # Clean up the test data if requested
        if args.cleanup:
            print("Cleaning up test data...")
            with DataTransport() as transport:
                if task_created:
                    print(f"Deleting task {task_id}")
                    transport.db_client.delete_processing_task(task_id)

                if document_created:
                    print(f"Deleting document {document_id}")
                    transport.db_client.delete_document(document_id)

    return 0


if __name__ == "__main__":
    sys.exit(main())
