#!/usr/bin/env python3
"""
System test for the knowledge graph pipeline.

This script tests the entire knowledge graph pipeline, from document ingestion to entity extraction
and relationship creation. It uses a sample document and the sample Vertex AI batch response file.

Usage:
    python test_knowledge_graph_system.py --document-path tests/data/sample_txt.txt
"""
import os
import sys
import json
import uuid
import time
import argparse
from pathlib import Path
from unittest.mock import MagicMock, patch

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from services.kg_entity_relationship_processor import EntityRelationshipProcessor
from transport.vertex_ai_batch_client import VertexAIBatchClient
from services.knowledge_graph_service import KnowledgeGraphService
from transport.data_transport import DataTransport
from workers.knowledge_graph_worker import build_knowledge_graph

def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="System test for the knowledge graph pipeline")
    parser.add_argument("--document-path", type=str, default="tests/data/sample_txt.txt",
                        help="Path to the document to ingest")
    parser.add_argument("--batch-response", type=str, default="tests/data/sample_vertex_ai_batch_response.jsonl",
                        help="Path to the Vertex AI batch response file")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    parser.add_argument("--skip-db-check", action="store_true", help="Skip database checks")
    return parser.parse_args()

def main():
    """Main function."""
    args = parse_args()

    # Check if the document exists
    if not os.path.exists(args.document_path):
        print(f"Error: Document not found: {args.document_path}")
        return 1

    # Check if the batch response file exists
    if not os.path.exists(args.batch_response):
        print(f"Error: Batch response file not found: {args.batch_response}")
        return 1

    print(f"Testing knowledge graph pipeline with document: {args.document_path}")
    print(f"Using batch response file: {args.batch_response}")

    # Create an EntityRelationshipProcessor
    processor = EntityRelationshipProcessor()

    # Parse the Vertex AI batch response
    batch_results = processor.parse_vertex_ai_response(args.batch_response)

    # Print the results
    print(f"Parsed {len(batch_results)} chunks from the batch response")

    # Count total entities and relationships
    total_entities = sum(len(result.get("entities", [])) for result in batch_results)
    total_relationships = sum(len(result.get("relationships", [])) for result in batch_results)

    print(f"Total entities: {total_entities}")
    print(f"Total relationships: {total_relationships}")

    # Create a mock VertexAIBatchClient that returns our parsed results
    class MockVertexAIBatchClient(VertexAIBatchClient):
        def __init__(self):
            # Skip initialization to avoid connecting to real services
            pass

        def batch_process_chunks(self, chunks, wait_for_results=True):
            print(f"Mock batch_process_chunks called with {len(chunks)} chunks, wait_for_results={wait_for_results}")
            batch_job_id = f"projects/test-project/locations/us-central1/batchPredictionJobs/test-batch-{uuid.uuid4().hex[:8]}"
            if wait_for_results:
                return {
                    "status": "success",
                    "job": batch_job_id,
                    "batch_job_id": batch_job_id,
                    "input_uri": "gs://test-bucket/input.jsonl",
                    "output_uri": "gs://test-bucket/output/",
                    "chunk_count": len(chunks)
                }
            else:
                return {
                    "status": "submitted",
                    "batch_job_id": batch_job_id,
                    "input_uri": "gs://test-bucket/input.jsonl",
                    "output_uri": "gs://test-bucket/output/",
                    "chunk_count": len(chunks)
                }

        def batch_process_chunks_async(self, chunks):
            print(f"Mock batch_process_chunks_async called with {len(chunks)} chunks")
            return {
                "status": "submitted",
                "batch_job_id": f"projects/test-project/locations/us-central1/batchPredictionJobs/test-batch-{uuid.uuid4().hex[:8]}",
                "input_uri": "gs://test-bucket/input.jsonl",
                "output_uri": "gs://test-bucket/output/",
                "chunk_count": len(chunks)
            }

        def get_batch_results(self, job_id_or_job, chunks=None):
            print(f"Mock get_batch_results called with job_id: {job_id_or_job}")
            return batch_results

    # Read the document content
    with open(args.document_path, "r") as f:
        document_content = f.read()

    # Create a document ID
    document_id = str(uuid.uuid4())
    print(f"Using document ID: {document_id}")

    # Create mock objects for testing
    mock_document = MagicMock()
    mock_document.id = document_id
    mock_document.to_dict.return_value = {
        "id": document_id,
        "filename": os.path.basename(args.document_path),
        "content": document_content[:100] + "...",  # Truncated for display
        "metadata": {"source": "test", "build_knowledge_graph": True}
    }

    # Create mock chunks
    mock_chunks = []
    for i, result in enumerate(batch_results):
        chunk_id = result.get("chunk_id", str(uuid.uuid4()))
        mock_chunks.append({
            "id": chunk_id,
            "document_id": document_id,
            "text": f"Sample chunk text {i+1}",
            "chunk_index": i,
            "metadata": {"element_id": i}
        })

    # Create a mock DataTransport
    mock_transport = MagicMock()
    mock_transport.db_client.get_document.return_value = mock_document
    mock_transport.get_document_chunks.return_value = mock_chunks
    mock_transport.db_client.create_entity.side_effect = lambda **kwargs: MagicMock(id=kwargs.get("id"))
    mock_transport.db_client.create_relationship.side_effect = lambda **kwargs: MagicMock(id=kwargs.get("id"))
    mock_transport.find_similar_entities.return_value = []  # No similar entities found

    # Create a mock Neo4j client
    mock_neo4j_client = MagicMock()
    mock_transport.neo4j_client = mock_neo4j_client

    # Create a mock NLPService
    mock_nlp_service = MagicMock()
    mock_nlp_service.deduplicate_and_normalize_entities.side_effect = lambda entities, document_id: entities

    # Create a KnowledgeGraphService with our mock client
    with patch('services.knowledge_graph_service.DataTransport', return_value=mock_transport), \
         patch('services.knowledge_graph_service.VertexAIBatchClient', return_value=MockVertexAIBatchClient()), \
         patch('services.knowledge_graph_service.NLPService', mock_nlp_service):

        # Create a KnowledgeGraphService
        kg_service = KnowledgeGraphService()

        # Test synchronous processing
        print("\nTesting synchronous knowledge graph building...")
        task_id = str(uuid.uuid4())
        result = kg_service.build_knowledge_graph_from_document(document_id, task_id, async_mode=False)
        print(f"Synchronous result: {result}")

        # Test asynchronous processing
        print("\nTesting asynchronous knowledge graph building...")
        task_id = str(uuid.uuid4())
        result = kg_service.build_knowledge_graph_from_document(document_id, task_id, async_mode=True)
        print(f"Asynchronous result: {result}")

        # Test continuing knowledge graph building
        print("\nTesting continue_knowledge_graph_building...")
        # Create a mock task
        mock_task = MagicMock()
        mock_task.id = task_id
        mock_task.document_id = document_id
        mock_task.task_type = "knowledge_graph_building"
        mock_task.status = "processing"
        mock_task.result = {"batch_job_id": result.get("batch_job_id"), "async_processing": True}
        mock_transport.db_client.get_processing_task.return_value = mock_task

        # Continue knowledge graph building
        result = kg_service.continue_knowledge_graph_building(task_id)
        print(f"Continue result: {result}")

    # Check if we should skip database checks
    if args.skip_db_check:
        print("\nSkipping database checks")
        return 0

    # Test with real database connection
    print("\nTesting with real database connection...")
    try:
        with DataTransport() as transport:
            # Create a test document
            print("Creating test document...")
            document_id = transport.db_client.create_document(
                filename=os.path.basename(args.document_path),
                content=document_content,
                metadata={"source": "test", "build_knowledge_graph": True}
            )
            print(f"Created test document with ID: {document_id}")

            # Create a processing task
            print("Creating processing task...")
            task_id = transport.create_processing_task(
                document_id=document_id,
                task_type="knowledge_graph_building",
                metadata={"use_batch_api": True, "async_processing": True}
            )
            print(f"Created processing task with ID: {task_id}")

            # Update the task with a batch job ID
            batch_job_id = f"projects/test-project/locations/us-central1/batchPredictionJobs/test-batch-{uuid.uuid4().hex[:8]}"
            transport.update_task_status(
                task_id=task_id,
                status="processing",
                result={"batch_job_id": batch_job_id, "async_processing": True}
            )
            print(f"Updated task with batch job ID: {batch_job_id}")

            # Create a KnowledgeGraphService with our mock client
            with patch('services.knowledge_graph_service.VertexAIBatchClient', return_value=MockVertexAIBatchClient()):
                # Create a KnowledgeGraphService
                kg_service = KnowledgeGraphService()

                # Continue knowledge graph building
                print("Continuing knowledge graph building...")
                result = kg_service.continue_knowledge_graph_building(task_id)
                print(f"Result: {result}")

                # Check if entities were stored in the database
                entities = transport.db_client.db.query("SELECT COUNT(*) FROM entities").fetchone()[0]
                print(f"Entities in database: {entities}")

                # Check if relationships were stored in the database
                relationships = transport.db_client.db.query("SELECT COUNT(*) FROM relationships").fetchone()[0]
                print(f"Relationships in database: {relationships}")
    except Exception as e:
        print(f"Error testing with real database connection: {e}")
        print("Skipping database checks")

    return 0

if __name__ == "__main__":
    sys.exit(main())
