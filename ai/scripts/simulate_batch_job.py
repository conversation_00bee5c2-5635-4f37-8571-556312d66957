#!/usr/bin/env python
"""
Script to simulate a successful batch job by updating the processing task in the database.
"""
import sys
import uuid
import json
import logging
from typing import Dict, Any, List, Union

from transport.data_transport import DataTransport
from common.database import ProcessingTask

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def simulate_batch_job(document_id: str) -> None:
    """
    Simulate a successful batch job by updating the processing task in the database.

    Args:
        document_id: Document ID to simulate batch job for
    """
    with DataTransport() as transport:
        # Find processing tasks for the document
        tasks = transport.db_client.db.query(ProcessingTask).filter(
            ProcessingTask.document_id == document_id,
            ProcessingTask.task_type == "knowledge_graph_building",
            ProcessingTask.status == "pending"
        ).all()

        if not tasks:
            logger.error(f"No pending knowledge_graph_building tasks found for document {document_id}")
            return

        logger.info(f"Found {len(tasks)} pending knowledge_graph_building tasks for document {document_id}")

        # Update each task with a simulated batch job ID
        for task in tasks:
            task_id = task.id
            logger.info(f"Updating task {task_id} with simulated batch job ID")

            # Create a simulated batch job ID
            batch_job_id = f"projects/test-project/locations/us-central1/batchPredictionJobs/test-batch-{uuid.uuid4().hex[:8]}"

            # Update the task with the batch job ID
            transport.db_client.db.query(ProcessingTask).filter(
                ProcessingTask.id == task_id
            ).update({
                "status": "processing",
                "result": {
                    "status": "processing",
                    "document_id": document_id,
                    "batch_job_id": batch_job_id,
                    "async_processing": True
                }
            })

            logger.info(f"Updated task {task_id} with batch job ID {batch_job_id}")

            # Commit the changes
            transport.db_client.db.commit()

            # Load the sample Vertex AI batch response
            with open("tests/data/sample_vertex_ai_batch_response.jsonl", "r") as f:
                sample_response = f.read()

            # Create a simulated batch job result in the database
            transport.db_client.db.query(ProcessingTask).filter(
                ProcessingTask.id == task_id
            ).update({
                "status": "ready_to_continue",
                "result": {
                    "status": "ready_to_continue",
                    "document_id": document_id,
                    "batch_job_id": batch_job_id,
                    "async_processing": True,
                    "batch_results": sample_response
                }
            })

            logger.info(f"Updated task {task_id} with simulated batch job result")

            # Commit the changes
            transport.db_client.db.commit()

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python simulate_batch_job.py <document_id>")
        sys.exit(1)

    document_id = sys.argv[1]
    simulate_batch_job(document_id)
