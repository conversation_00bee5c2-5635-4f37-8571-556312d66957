#!/usr/bin/env python
"""
Test script for the ID validator.

This script tests the ID validator functions in common/id_validator.py.

Usage:
    python ai/scripts/test_id_validator.py
"""

import sys
import uuid
import logging

# Add parent directory to path
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from common.id_validator import validate_and_normalize_id, is_valid_uuid, safe_uuid_conversion, validate_id_with_context

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_validate_and_normalize_id():
    """Test the validate_and_normalize_id function."""
    logger.info("Testing validate_and_normalize_id function")

    # Test cases
    test_cases = [
        {
            "name": "Valid alphanumeric ID",
            "id_value": "abc-123",
            "expected": "abc-123"
        },
        {
            "name": "UUID string",
            "id_value": "123e4567-e89b-12d3-a456-************",
            "expected": "123e4567-e89b-12d3-a456-************"
        },
        {
            "name": "UUID object",
            "id_value": uuid.UUID("123e4567-e89b-12d3-a456-************"),
            "expected": "123e4567-e89b-12d3-a456-************"
        },
        {
            "name": "UUID with braces",
            "id_value": "{123e4567-e89b-12d3-a456-************}",
            "expected": "123e4567-e89b-12d3-a456-************"
        },
        {
            "name": "Invalid characters",
            "id_value": "abc 123@#$%",
            "expected": "abc123"
        },
        {
            "name": "Empty string",
            "id_value": "",
            "expected": None
        },
        {
            "name": "None value",
            "id_value": None,
            "expected": None
        },
        {
            "name": "Integer value",
            "id_value": 123,
            "expected": "123"
        }
    ]

    # Run tests
    for test_case in test_cases:
        logger.info(f"Testing {test_case['name']}")
        result = validate_and_normalize_id(test_case["id_value"], log_prefix=f"{test_case['name']}: ")

        if result == test_case["expected"]:
            logger.info(f"✅ Test passed: {test_case['name']}")
        else:
            logger.error(f"❌ Test failed: {test_case['name']}")
            logger.error(f"  Expected: {test_case['expected']}")
            logger.error(f"  Got: {result}")


def test_is_valid_uuid():
    """Test the is_valid_uuid function."""
    logger.info("Testing is_valid_uuid function")

    # Test cases
    test_cases = [
        {
            "name": "Valid UUID string",
            "id_value": "123e4567-e89b-12d3-a456-************",
            "expected": True
        },
        {
            "name": "Valid UUID object",
            "id_value": uuid.UUID("123e4567-e89b-12d3-a456-************"),
            "expected": True
        },
        {
            "name": "Invalid UUID string",
            "id_value": "not-a-uuid",
            "expected": False
        },
        {
            "name": "Empty string",
            "id_value": "",
            "expected": False
        },
        {
            "name": "None value",
            "id_value": None,
            "expected": False
        },
        {
            "name": "Integer value",
            "id_value": 123,
            "expected": False
        }
    ]

    # Run tests
    for test_case in test_cases:
        logger.info(f"Testing {test_case['name']}")
        result = is_valid_uuid(test_case["id_value"])

        if result == test_case["expected"]:
            logger.info(f"✅ Test passed: {test_case['name']}")
        else:
            logger.error(f"❌ Test failed: {test_case['name']}")
            logger.error(f"  Expected: {test_case['expected']}")
            logger.error(f"  Got: {result}")


def test_safe_uuid_conversion():
    """Test the safe_uuid_conversion function."""
    logger.info("Testing safe_uuid_conversion function")

    # Test cases
    test_cases = [
        {
            "name": "Valid UUID string",
            "id_value": "123e4567-e89b-12d3-a456-************",
            "expected": uuid.UUID("123e4567-e89b-12d3-a456-************")
        },
        {
            "name": "Valid UUID object",
            "id_value": uuid.UUID("123e4567-e89b-12d3-a456-************"),
            "expected": uuid.UUID("123e4567-e89b-12d3-a456-************")
        },
        {
            "name": "Invalid UUID string",
            "id_value": "not-a-uuid",
            "expected": "not-a-uuid"
        },
        {
            "name": "None value",
            "id_value": None,
            "expected": None
        }
    ]

    # Run tests
    for test_case in test_cases:
        logger.info(f"Testing {test_case['name']}")
        result = safe_uuid_conversion(test_case["id_value"])

        if result == test_case["expected"]:
            logger.info(f"✅ Test passed: {test_case['name']}")
        else:
            logger.error(f"❌ Test failed: {test_case['name']}")
            logger.error(f"  Expected: {test_case['expected']}")
            logger.error(f"  Got: {result}")


def test_validate_id_with_context():
    """Test the validate_id_with_context function."""
    logger.info("Testing validate_id_with_context function")

    # Test cases
    test_cases = [
        {
            "name": "Valid alphanumeric ID",
            "id_value": "abc-123",
            "context": "Test context",
            "error_on_invalid": False,
            "expected": "abc-123",
            "should_raise": False
        },
        {
            "name": "UUID string",
            "id_value": "123e4567-e89b-12d3-a456-************",
            "context": "Test context",
            "error_on_invalid": False,
            "expected": "123e4567-e89b-12d3-a456-************",
            "should_raise": False
        },
        {
            "name": "Invalid characters",
            "id_value": "abc 123@#$%",
            "context": "Test context",
            "error_on_invalid": False,
            "expected": "abc123",
            "should_raise": False
        },
        {
            "name": "None value without error",
            "id_value": None,
            "context": "Test context",
            "error_on_invalid": False,
            "expected": None,
            "should_raise": False
        },
        {
            "name": "None value with error",
            "id_value": None,
            "context": "Test context",
            "error_on_invalid": True,
            "expected": None,
            "should_raise": True
        },
        {
            "name": "Empty string with error",
            "id_value": "",
            "context": "Test context",
            "error_on_invalid": True,
            "expected": None,
            "should_raise": True
        }
    ]

    # Run tests
    for test_case in test_cases:
        logger.info(f"Testing {test_case['name']}")
        try:
            result = validate_id_with_context(
                test_case["id_value"],
                test_case["context"],
                error_on_invalid=test_case["error_on_invalid"]
            )

            if test_case["should_raise"]:
                logger.error(f"❌ Test failed: {test_case['name']} - Expected exception but none was raised")
                continue

            if result == test_case["expected"]:
                logger.info(f"✅ Test passed: {test_case['name']}")
            else:
                logger.error(f"❌ Test failed: {test_case['name']}")
                logger.error(f"  Expected: {test_case['expected']}")
                logger.error(f"  Got: {result}")

        except ValueError as e:
            if test_case["should_raise"]:
                logger.info(f"✅ Test passed: {test_case['name']} - Exception raised as expected: {e}")
            else:
                logger.error(f"❌ Test failed: {test_case['name']} - Unexpected exception: {e}")


def main():
    """Main function."""
    print("=== Testing ID Validator ===\n")

    print("\n=== Test 1: validate_and_normalize_id ===")
    test_validate_and_normalize_id()

    print("\n=== Test 2: is_valid_uuid ===")
    test_is_valid_uuid()

    print("\n=== Test 3: safe_uuid_conversion ===")
    test_safe_uuid_conversion()

    print("\n=== Test 4: validate_id_with_context ===")
    test_validate_id_with_context()

    print("\n=== Testing Complete ===")


if __name__ == "__main__":
    main()
