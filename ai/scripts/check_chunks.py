#!/usr/bin/env python
"""
Script to check chunks in the database.
"""
import sys
import logging
from common.database import Chunk, KGChunk
from transport.data_transport import DataTransport

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_chunks(document_id):
    """
    Check chunks in the database.
    """
    with DataTransport() as transport:
        # Get chunks
        chunks = transport.db_client.db.query(Chunk).filter(
            Chunk.document_id == document_id
        ).all()
        logger.info(f"Found {len(chunks)} chunks for document {document_id}")
        
        # Get KG chunks
        kg_chunks = transport.db_client.db.query(KGChunk).filter(
            KGChunk.document_id == document_id
        ).all()
        logger.info(f"Found {len(kg_chunks)} KG chunks for document {document_id}")
        
        # Print chunk IDs
        logger.info("Chunk IDs:")
        for chunk in chunks:
            logger.info(f"  {chunk.id}")
        
        # Print KG chunk IDs
        logger.info("KG Chunk IDs:")
        for kg_chunk in kg_chunks:
            logger.info(f"  {kg_chunk.id}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python check_chunks.py <document_id>")
        sys.exit(1)
    
    document_id = sys.argv[1]
    check_chunks(document_id)
