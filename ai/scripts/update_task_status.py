#!/usr/bin/env python
"""
Script to update a processing task's status in the database.
"""
import sys
import logging
from common.database import ProcessingTask
from transport.data_transport import DataTransport

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def update_task_status(task_id, new_status):
    """
    Update a processing task's status in the database.
    """
    with DataTransport() as transport:
        # Get the task
        task = transport.db_client.db.query(ProcessingTask).filter(
            ProcessingTask.id == task_id
        ).first()
        
        if not task:
            logger.error(f"Task {task_id} not found")
            return
        
        logger.info(f"Task: {task.id}, Document: {task.document_id}, Type: {task.task_type}, Status: {task.status}")
        logger.info(f"Updating status from {task.status} to {new_status}")
        
        # Update the task status
        transport.db_client.db.query(ProcessingTask).filter(
            ProcessingTask.id == task_id
        ).update({
            "status": new_status
        })
        
        # Commit the changes
        transport.db_client.db.commit()
        
        logger.info(f"Task status updated to {new_status}")

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python update_task_status.py <task_id> <new_status>")
        sys.exit(1)
    
    task_id = sys.argv[1]
    new_status = sys.argv[2]
    update_task_status(task_id, new_status)
