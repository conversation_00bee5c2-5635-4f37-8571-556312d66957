#!/usr/bin/env python
"""
Test script for task result handling in knowledge graph service.

This script tests the task result handling in the knowledge graph service,
specifically how task results are preserved when updating task status.

Usage:
    python ai/scripts/test_task_result_handling.py
"""

import json
import logging
import sys
import time
import uuid
from typing import Dict, Any, Union

# Add parent directory to path
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from common.database import ProcessingTask
from services.knowledge_graph_service import KnowledgeGraphService
from transport.data_transport import DataTransport

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def create_test_task(document_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
    """
    Create a test task with initial result data.
    
    Args:
        document_id: Document ID
        
    Returns:
        Dict[str, Any]: Task information
    """
    logger.info(f"Creating test task for document: {document_id}")
    
    with DataTransport() as transport:
        # Create a task
        task = transport.db_client.create_processing_task(
            document_id=document_id,
            task_type="knowledge_graph_building",
            celery_task_id=None
        )
        
        # Update task with initial result data
        batch_job_id = f"projects/test-project/locations/us-central1/batchPredictionJobs/test-batch-{uuid.uuid4().hex[:8]}"
        transport.update_task_status(
            task_id=task.id,
            status="batch_job_submitted",
            result={
                "batch_job_id": batch_job_id,
                "batch_job_status": "submitted",
                "submitted_at": time.time(),
                "async_processing": True,
                "test_metadata": {
                    "test_id": str(uuid.uuid4()),
                    "test_name": "Task Result Handling Test",
                    "test_timestamp": time.time()
                }
            }
        )
        
        logger.info(f"Created test task: {task.id}")
        
        # Get updated task
        updated_task = transport.db_client.get_processing_task(task.id)
        
        return {
            "task_id": str(updated_task.id),
            "document_id": str(updated_task.document_id),
            "status": updated_task.status,
            "result": updated_task.result
        }


def update_task_status(task_id: Union[str, uuid.UUID], status: str, additional_data: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    Update task status with additional data.
    
    Args:
        task_id: Task ID
        status: New status
        additional_data: Additional data to add to result
        
    Returns:
        Dict[str, Any]: Updated task information
    """
    logger.info(f"Updating task status: {task_id} -> {status}")
    
    with DataTransport() as transport:
        # Get task
        task = transport.db_client.get_processing_task(task_id)
        if not task:
            logger.error(f"Task {task_id} not found")
            return {"status": "error", "message": f"Task {task_id} not found"}
        
        # Update task status with additional data
        result = {
            **(task.result or {}),  # Preserve existing result data
            "status": status,       # Match the task status
            "updated_at": time.time()
        }
        
        # Add additional data if provided
        if additional_data:
            result.update(additional_data)
        
        # Update task status
        transport.update_task_status(
            task_id=task_id,
            status=status,
            result=result
        )
        
        # Get updated task
        updated_task = transport.db_client.get_processing_task(task_id)
        
        return {
            "task_id": str(updated_task.id),
            "document_id": str(updated_task.document_id),
            "status": updated_task.status,
            "result": updated_task.result
        }


def simulate_continue_knowledge_graph_building(task_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
    """
    Simulate continuing knowledge graph building.
    
    Args:
        task_id: Task ID
        
    Returns:
        Dict[str, Any]: Updated task information
    """
    logger.info(f"Simulating continue_knowledge_graph_building for task: {task_id}")
    
    with DataTransport() as transport:
        # Get task
        task = transport.db_client.get_processing_task(task_id)
        if not task:
            logger.error(f"Task {task_id} not found")
            return {"status": "error", "message": f"Task {task_id} not found"}
        
        # Update task status with completion data
        result = {
            **(task.result or {}),  # Preserve existing result data
            "status": "completed",  # Match the task status
            "entities_count": 10,
            "relationships_count": 5,
            "completed_at": time.time()
        }
        
        # Update task status
        transport.update_task_status(
            task_id=task_id,
            status="completed",
            result=result
        )
        
        # Get updated task
        updated_task = transport.db_client.get_processing_task(task_id)
        
        return {
            "task_id": str(updated_task.id),
            "document_id": str(updated_task.document_id),
            "status": updated_task.status,
            "result": updated_task.result
        }


def simulate_error_handling(task_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
    """
    Simulate error handling in continue_knowledge_graph_building.
    
    Args:
        task_id: Task ID
        
    Returns:
        Dict[str, Any]: Updated task information
    """
    logger.info(f"Simulating error handling for task: {task_id}")
    
    with DataTransport() as transport:
        # Get task
        task = transport.db_client.get_processing_task(task_id)
        if not task:
            logger.error(f"Task {task_id} not found")
            return {"status": "error", "message": f"Task {task_id} not found"}
        
        # Update task status with error data
        result = {
            **(task.result or {}),  # Preserve existing result data
            "status": "failed",     # Match the task status
            "error_message": "Simulated error in continue_knowledge_graph_building",
            "failed_at": time.time()
        }
        
        # Update task status
        transport.update_task_status(
            task_id=task_id,
            status="failed",
            result=result,
            error="Simulated error in continue_knowledge_graph_building"
        )
        
        # Get updated task
        updated_task = transport.db_client.get_processing_task(task_id)
        
        return {
            "task_id": str(updated_task.id),
            "document_id": str(updated_task.document_id),
            "status": updated_task.status,
            "result": updated_task.result,
            "error": updated_task.error
        }


def verify_task_result_preservation(task_id: Union[str, uuid.UUID]) -> bool:
    """
    Verify that task result data is preserved through updates.
    
    Args:
        task_id: Task ID
        
    Returns:
        bool: True if test passes, False otherwise
    """
    logger.info(f"Verifying task result preservation for task: {task_id}")
    
    with DataTransport() as transport:
        # Get task
        task = transport.db_client.get_processing_task(task_id)
        if not task:
            logger.error(f"Task {task_id} not found")
            return False
        
        # Check if test_metadata is preserved
        if not task.result or "test_metadata" not in task.result:
            logger.error(f"Test metadata not found in task result: {task.result}")
            return False
        
        # Check if batch_job_id is preserved
        if not task.result or "batch_job_id" not in task.result:
            logger.error(f"batch_job_id not found in task result: {task.result}")
            return False
        
        logger.info(f"Task result preservation verified for task: {task_id}")
        return True


def main():
    """Main function."""
    # Create a test document ID
    document_id = uuid.uuid4()
    
    # Create a test task
    task_info = create_test_task(document_id)
    task_id = task_info["task_id"]
    
    print(f"Created test task: {task_id}")
    print(f"Initial task result: {json.dumps(task_info['result'], indent=2)}")
    print("")
    
    # Update task status to ready_to_continue
    updated_task = update_task_status(
        task_id=task_id,
        status="ready_to_continue",
        additional_data={
            "completed_at": time.time()
        }
    )
    
    print(f"Updated task status to ready_to_continue")
    print(f"Updated task result: {json.dumps(updated_task['result'], indent=2)}")
    print("")
    
    # Simulate continue_knowledge_graph_building
    completed_task = simulate_continue_knowledge_graph_building(task_id)
    
    print(f"Simulated continue_knowledge_graph_building")
    print(f"Completed task result: {json.dumps(completed_task['result'], indent=2)}")
    print("")
    
    # Verify task result preservation
    if verify_task_result_preservation(task_id):
        print("✅ Task result preservation test PASSED")
    else:
        print("❌ Task result preservation test FAILED")
    
    # Create another test task for error handling
    error_task_info = create_test_task(document_id)
    error_task_id = error_task_info["task_id"]
    
    print(f"Created test task for error handling: {error_task_id}")
    print("")
    
    # Simulate error handling
    failed_task = simulate_error_handling(error_task_id)
    
    print(f"Simulated error handling")
    print(f"Failed task result: {json.dumps(failed_task['result'], indent=2)}")
    print(f"Failed task error: {failed_task['error']}")
    print("")
    
    # Verify task result preservation for error case
    if verify_task_result_preservation(error_task_id):
        print("✅ Task result preservation test for error handling PASSED")
    else:
        print("❌ Task result preservation test for error handling FAILED")


if __name__ == "__main__":
    main()
