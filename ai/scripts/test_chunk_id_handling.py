#!/usr/bin/env python
"""
Test script for chunk ID handling in knowledge graph service.

This script tests the chunk ID handling in the knowledge graph service,
specifically how chunk IDs are extracted, validated, and used throughout the flow.

Usage:
    python ai/scripts/test_chunk_id_handling.py
"""

import json
import logging
import sys
import uuid
import re
# No need for unused imports

# Add parent directory to path
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from services.kg_entity_relationship_processor import EntityRelationshipProcessor
# We're simulating VertexAIBatchClient behavior, not actually using it
# from transport.vertex_ai_batch_client import VertexAIBatchClient
from services.knowledge_graph_service import KnowledgeGraphService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_chunk_id_extraction_from_request():
    """
    Test chunk ID extraction from request text using multiple patterns.
    """
    logger.info("Testing chunk ID extraction from request text")

    # Create test request texts with different chunk ID formats
    test_cases = [
        {
            "name": "JSON format",
            "text": 'Extract entities from the following text:\n\n{"chunk_id": "abc-123", "text": "Sample text"}',
            "expected": "abc-123"
        },
        {
            "name": "YAML-like format",
            "text": 'Extract entities from the following text:\n\nchunk_id: "def-456"\ntext: "Sample text"',
            "expected": "def-456"
        },
        {
            "name": "Plain text format",
            "text": 'Extract entities from the following text. Chunk ID: ghi-789\n\nSample text',
            "expected": "ghi-789"
        },
        {
            "name": "Case insensitive",
            "text": 'Extract entities from the following text. chunk id: jkl-012\n\nSample text',
            "expected": "jkl-012"
        },
        {
            "name": "Invalid characters",
            "text": 'Extract entities from the following text:\n\n{"chunk_id": "mno 456@#$%", "text": "Sample text"}',
            "expected": "mno456"  # After cleaning
        },
        {
            "name": "UUID format",
            "text": 'Extract entities from the following text:\n\n{"chunk_id": "123e4567-e89b-12d3-a456-************", "text": "Sample text"}',
            "expected": "123e4567-e89b-12d3-a456-************"
        },
        {
            "name": "UUID with braces",
            "text": 'Extract entities from the following text:\n\n{"chunk_id": "{123e4567-e89b-12d3-a456-************}", "text": "Sample text"}',
            "expected": "123e4567-e89b-12d3-a456-************"  # Braces removed
        },
        {
            "name": "Missing chunk ID",
            "text": 'Extract entities from the following text:\n\n{"text": "Sample text"}',
            "expected": None
        }
    ]

    # Define the patterns to test
    chunk_id_patterns = [
        r'"chunk_id":\s*"([^"]+)"',  # JSON format
        r'chunk_id:\s*"([^"]+)"',      # YAML-like format
        r'Chunk ID:\s*([\w-]+)',         # Plain text format
        r'chunk ID:\s*([\w-]+)'          # Case insensitive
    ]

    # Test each case
    for test_case in test_cases:
        logger.info(f"Testing {test_case['name']}")

        # Try to extract chunk ID using patterns
        chunk_id = None
        for pattern in chunk_id_patterns:
            chunk_id_match = re.search(pattern, test_case["text"], re.IGNORECASE)
            if chunk_id_match:
                chunk_id = chunk_id_match.group(1)
                logger.info(f"Extracted chunk_id using pattern '{pattern}': {chunk_id}")
                break

        # Clean up chunk ID if found
        if chunk_id:
            chunk_id = chunk_id.strip()

            # Check if it's a UUID format (with or without braces)
            uuid_pattern = r'^\{?[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\}?$'
            if re.match(uuid_pattern, chunk_id, re.IGNORECASE):
                # It's a UUID, remove braces if present
                if chunk_id.startswith('{') and chunk_id.endswith('}'):
                    original_chunk_id = chunk_id
                    chunk_id = chunk_id[1:-1]
                    logger.info(f"Removed braces from UUID: '{original_chunk_id}' -> '{chunk_id}'")
                logger.info(f"Validated UUID format chunk_id: {chunk_id}")
            elif not re.match(r'^[\w\-]+$', chunk_id):
                # Not a UUID and contains invalid characters
                logger.warning(f"Extracted chunk_id '{chunk_id}' contains invalid characters, cleaning up")
                original_chunk_id = chunk_id
                chunk_id = re.sub(r'[^\w\-]', '', chunk_id)
                logger.info(f"Cleaned up chunk_id: '{original_chunk_id}' -> '{chunk_id}'")

        # Check if result matches expected
        if chunk_id == test_case["expected"]:
            logger.info(f"✅ Test passed: {test_case['name']}")
        else:
            logger.error(f"❌ Test failed: {test_case['name']}")
            logger.error(f"  Expected: {test_case['expected']}")
            logger.error(f"  Got: {chunk_id}")


def test_entity_relationship_processor_chunk_id_handling():
    """
    Test chunk ID handling in EntityRelationshipProcessor.
    """
    logger.info("Testing chunk ID handling in EntityRelationshipProcessor")

    # Create test chunk results with different chunk ID formats
    test_cases = [
        {
            "name": "Valid chunk ID",
            "chunk_id": "abc-123",
            "expected_success": True
        },
        {
            "name": "UUID chunk ID",
            "chunk_id": uuid.uuid4(),
            "expected_success": True
        },
        {
            "name": "Invalid characters in chunk ID",
            "chunk_id": "def 456@#$%",
            "expected_success": True,  # Should clean up and succeed
            "expected_cleaned": "def456"
        },
        {
            "name": "Empty chunk ID",
            "chunk_id": "",
            "expected_success": False
        },
        {
            "name": "None chunk ID",
            "chunk_id": None,
            "expected_success": False
        }
    ]

    # Create processor
    processor = EntityRelationshipProcessor()

    # Test each case
    for test_case in test_cases:
        logger.info(f"Testing {test_case['name']}")

        # Create test entities and document ID
        document_id = str(uuid.uuid4())
        entities = [
            {"name": "Test Entity 1", "category": "TEST"},
            {"name": "Test Entity 2", "category": "TEST"}
        ]

        # Process entities with test chunk ID
        processed_entities = processor._process_direct_entities(entities, test_case["chunk_id"], document_id)

        # Check if processing succeeded as expected
        if test_case["expected_success"]:
            if processed_entities:
                logger.info(f"✅ Test passed: {test_case['name']} - Processing succeeded")

                # Check if chunk ID was cleaned up as expected
                if "expected_cleaned" in test_case:
                    actual_chunk_id = processed_entities[0]["chunk_id"]
                    if actual_chunk_id == test_case["expected_cleaned"]:
                        logger.info(f"✅ Test passed: Chunk ID cleaned up correctly")
                    else:
                        logger.error(f"❌ Test failed: Chunk ID not cleaned up correctly")
                        logger.error(f"  Expected: {test_case['expected_cleaned']}")
                        logger.error(f"  Got: {actual_chunk_id}")
            else:
                logger.error(f"❌ Test failed: {test_case['name']} - Processing failed")
        else:
            if not processed_entities:
                logger.info(f"✅ Test passed: {test_case['name']} - Processing failed as expected")
            else:
                logger.error(f"❌ Test failed: {test_case['name']} - Processing succeeded unexpectedly")


def test_vertex_ai_batch_client_chunk_id_handling():
    """
    Test chunk ID handling in VertexAIBatchClient.
    """
    logger.info("Testing chunk ID handling in VertexAIBatchClient")

    # Create test chunks with different chunk ID formats
    test_cases = [
        {
            "name": "Valid chunk ID",
            "chunks": [
                {"id": "abc-123", "text": "Sample text"}
            ],
            "expected_valid_count": 1
        },
        {
            "name": "UUID chunk ID",
            "chunks": [
                {"id": uuid.uuid4(), "text": "Sample text"}
            ],
            "expected_valid_count": 1
        },
        {
            "name": "Missing chunk ID",
            "chunks": [
                {"text": "Sample text"}
            ],
            "expected_valid_count": 0
        },
        {
            "name": "Empty text",
            "chunks": [
                {"id": "def-456", "text": ""}
            ],
            "expected_valid_count": 0
        },
        {
            "name": "Mixed valid and invalid",
            "chunks": [
                {"id": "ghi-789", "text": "Sample text 1"},
                {"text": "Sample text 2"},
                {"id": "jkl-012", "text": ""}
            ],
            "expected_valid_count": 1
        }
    ]

    # Note: We're not actually using the client in this test, just simulating its validation logic

    # Test each case
    for test_case in test_cases:
        logger.info(f"Testing {test_case['name']}")

        # Filter chunks using the client's validation logic
        valid_chunks = []
        for i, chunk in enumerate(test_case["chunks"]):
            # Validate chunk ID
            chunk_id = chunk.get("id")
            if not chunk_id:
                logger.error(f"Chunk at index {i} is missing an ID, skipping")
                continue

            # Ensure chunk ID is a string
            if not isinstance(chunk_id, str):
                try:
                    chunk_id = str(chunk_id)
                    chunk["id"] = chunk_id  # Update the chunk with string ID
                    logger.info(f"Converted chunk ID to string: {chunk_id}")
                except Exception as e:
                    logger.error(f"Failed to convert chunk ID to string: {e}, skipping chunk")
                    continue

            # Validate chunk text
            chunk_text = chunk.get("text", "")
            if not chunk_text or len(chunk_text.strip()) == 0:
                logger.warning(f"Skipping empty chunk: {chunk_id}")
                continue

            # Log chunk details
            logger.info(f"Validated chunk {i}: ID={chunk_id}, Text length={len(chunk_text)}")
            valid_chunks.append(chunk)

        # Check if valid count matches expected
        if len(valid_chunks) == test_case["expected_valid_count"]:
            logger.info(f"✅ Test passed: {test_case['name']} - Valid count matches expected")
        else:
            logger.error(f"❌ Test failed: {test_case['name']} - Valid count doesn't match expected")
            logger.error(f"  Expected: {test_case['expected_valid_count']}")
            logger.error(f"  Got: {len(valid_chunks)}")


def test_knowledge_graph_service_batch_results_processing():
    """
    Test batch results processing in KnowledgeGraphService.
    """
    logger.info("Testing batch results processing in KnowledgeGraphService")

    # Create test batch results with different chunk ID formats
    test_cases = [
        {
            "name": "Valid chunk ID in extracted data",
            "batch_results": json.dumps({
                "response": {
                    "candidates": [{
                        "content": {
                            "parts": [{
                                "text": '```json\n{"chunk_id": "abc-123", "entities": [{"name": "Test Entity", "category": "TEST"}], "relationships": []}\n```'
                            }]
                        }
                    }]
                }
            }),
            "expected_success": True,
            "expected_chunk_id": "abc-123"
        },
        {
            "name": "Chunk ID in request text",
            "batch_results": json.dumps({
                "request": {
                    "contents": [{
                        "parts": [{
                            "text": 'Extract entities from the following text:\n\n{"chunk_id": "def-456", "text": "Sample text"}'
                        }]
                    }]
                },
                "response": {
                    "candidates": [{
                        "content": {
                            "parts": [{
                                "text": '```json\n{"entities": [{"name": "Test Entity", "category": "TEST"}], "relationships": []}\n```'
                            }]
                        }
                    }]
                }
            }),
            "expected_success": True,
            "expected_chunk_id": "def-456"
        },
        {
            "name": "Invalid characters in chunk ID",
            "batch_results": json.dumps({
                "response": {
                    "candidates": [{
                        "content": {
                            "parts": [{
                                "text": '```json\n{"chunk_id": "ghi 789@#$%", "entities": [{"name": "Test Entity", "category": "TEST"}], "relationships": []}\n```'
                            }]
                        }
                    }]
                }
            }),
            "expected_success": True,
            "expected_chunk_id": "ghi789"  # After cleaning
        },
        {
            "name": "Missing chunk ID",
            "batch_results": json.dumps({
                "response": {
                    "candidates": [{
                        "content": {
                            "parts": [{
                                "text": '```json\n{"entities": [{"name": "Test Entity", "category": "TEST"}], "relationships": []}\n```'
                            }]
                        }
                    }]
                }
            }),
            "expected_success": False
        }
    ]

    # Create service
    service = KnowledgeGraphService()

    # Test each case
    for test_case in test_cases:
        logger.info(f"Testing {test_case['name']}")

        try:
            # Process batch results
            results = service._process_batch_results(test_case["batch_results"], None)

            # Check if processing succeeded as expected
            if test_case["expected_success"]:
                if results:
                    logger.info(f"✅ Test passed: {test_case['name']} - Processing succeeded")

                    # Check if chunk ID matches expected
                    if "expected_chunk_id" in test_case:
                        actual_chunk_id = results[0]["chunk_id"]
                        if actual_chunk_id == test_case["expected_chunk_id"]:
                            logger.info(f"✅ Test passed: Chunk ID matches expected")
                        else:
                            logger.error(f"❌ Test failed: Chunk ID doesn't match expected")
                            logger.error(f"  Expected: {test_case['expected_chunk_id']}")
                            logger.error(f"  Got: {actual_chunk_id}")
                else:
                    logger.error(f"❌ Test failed: {test_case['name']} - No results returned")
            else:
                logger.error(f"❌ Test failed: {test_case['name']} - Processing succeeded unexpectedly")
        except Exception as e:
            if not test_case["expected_success"]:
                logger.info(f"✅ Test passed: {test_case['name']} - Processing failed as expected with error: {e}")
            else:
                logger.error(f"❌ Test failed: {test_case['name']} - Processing failed unexpectedly with error: {e}")


def main():
    """Main function."""
    print("=== Testing Chunk ID Handling ===\n")

    print("\n=== Test 1: Chunk ID Extraction from Request ===")
    test_chunk_id_extraction_from_request()

    print("\n=== Test 2: EntityRelationshipProcessor Chunk ID Handling ===")
    test_entity_relationship_processor_chunk_id_handling()

    print("\n=== Test 3: VertexAIBatchClient Chunk ID Handling ===")
    test_vertex_ai_batch_client_chunk_id_handling()

    print("\n=== Test 4: KnowledgeGraphService Batch Results Processing ===")
    test_knowledge_graph_service_batch_results_processing()

    print("\n=== Testing Complete ===")


if __name__ == "__main__":
    main()
