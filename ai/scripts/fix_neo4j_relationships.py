#!/usr/bin/env python
"""
Script to check and fix document-chunk relationships in Neo4j.
"""
import logging
import argpar<PERSON>
from typing import List, Dict, Any

from transport.data_transport import DataTransport

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_document_chunks(document_id: str) -> Dict[str, Any]:
    """
    Check document-chunk relationships for a specific document.

    Args:
        document_id: Document ID

    Returns:
        Dict[str, Any]: Verification results
    """
    with DataTransport() as transport:
        # Verify relationships
        results = transport.neo4j_client.verify_document_chunk_relationships(document_id)

        # Log results
        logger.info(f"Document {document_id} verification results:")
        logger.info(f"  Document exists: {results['document_exists']}")
        logger.info(f"  Chunks with proper relationships: {results['chunk_count']}")
        logger.info(f"  Orphaned chunks for this document: {results['document_orphaned_chunks']}")
        logger.info(f"  Total orphaned chunks in database: {results['all_orphaned_chunks']}")

        if results['sample_chunks']:
            logger.info(f"  Sample chunks with proper relationships:")
            for chunk in results['sample_chunks']:
                logger.info(f"    Chunk ID: {chunk['id']}, Index: {chunk['chunk_index']}")

        if results['sample_orphaned_chunks']:
            logger.info(f"  Sample orphaned chunks for this document:")
            for chunk in results['sample_orphaned_chunks']:
                logger.info(f"    Chunk ID: {chunk['id']}, Index: {chunk['chunk_index']}")

        return results

def fix_document_chunks(document_id: str) -> Dict[str, Any]:
    """
    Fix document-chunk relationships for a specific document.

    Args:
        document_id: Document ID

    Returns:
        Dict[str, Any]: Fix results
    """
    with DataTransport() as transport:
        # Fix orphaned chunks
        results = transport.neo4j_client.fix_orphaned_chunks(document_id)

        # Log results
        logger.info(f"Document {document_id} fix results:")
        logger.info(f"  Document created/verified: {results['document_created']}")
        logger.info(f"  Chunks fixed: {results['chunks_fixed']}")

        if results['fixed_chunks']:
            logger.info(f"  Fixed chunks:")
            for chunk_id in results['fixed_chunks'][:5]:  # Show first 5
                logger.info(f"    Chunk ID: {chunk_id}")

            if len(results['fixed_chunks']) > 5:
                logger.info(f"    ... and {len(results['fixed_chunks']) - 5} more")

        # Verify relationships after fixing
        verification = transport.neo4j_client.verify_document_chunk_relationships(document_id)
        logger.info(f"After fixing, document has {verification['chunk_count']} chunks with proper relationships")

        return results

def check_all_documents() -> List[Dict[str, Any]]:
    """
    Check all documents in the database.

    Returns:
        List[Dict[str, Any]]: List of verification results for each document
    """
    with DataTransport() as transport:
        # Get all documents from PostgreSQL
        documents = transport.db_client.get_all_documents()

        results = []
        total_chunks = 0
        total_orphaned = 0
        documents_with_issues = 0

        for document in documents:
            document_id = str(document.id)
            logger.info(f"Checking document {document_id} ({document.filename})")

            # Verify relationships
            verification = transport.neo4j_client.verify_document_chunk_relationships(document_id)
            verification['filename'] = document.filename
            results.append(verification)

            # Update totals
            total_chunks += verification['chunk_count']
            total_orphaned += verification['document_orphaned_chunks']

            # Log results
            logger.info(f"  Document has {verification['chunk_count']} chunks with proper relationships")
            if verification['document_orphaned_chunks'] > 0:
                logger.warning(f"  Document has {verification['document_orphaned_chunks']} orphaned chunks!")
                documents_with_issues += 1
            elif verification['chunk_count'] == 0:
                logger.warning(f"  Document has no chunks with proper relationships!")
                documents_with_issues += 1

        # Log summary
        logger.info(f"Summary: Checked {len(documents)} documents")
        logger.info(f"  Total chunks with proper relationships: {total_chunks}")
        logger.info(f"  Total orphaned chunks: {total_orphaned}")
        logger.info(f"  Documents with issues: {documents_with_issues}")

        return results

def fix_all_documents() -> List[Dict[str, Any]]:
    """
    Fix all documents in the database.

    Returns:
        List[Dict[str, Any]]: List of fix results for each document
    """
    with DataTransport() as transport:
        # Get all documents from PostgreSQL
        documents = transport.db_client.get_all_documents()

        results = []
        total_fixed = 0
        documents_fixed = 0

        for document in documents:
            document_id = str(document.id)

            # First check if document has orphaned chunks
            verification = transport.neo4j_client.verify_document_chunk_relationships(document_id)

            if verification['document_orphaned_chunks'] > 0:
                logger.info(f"Fixing document {document_id} ({document.filename}) - {verification['document_orphaned_chunks']} orphaned chunks")

                # Fix orphaned chunks
                fix_result = transport.neo4j_client.fix_orphaned_chunks(document_id)
                fix_result['filename'] = document.filename
                results.append(fix_result)

                # Update totals
                total_fixed += fix_result['chunks_fixed']
                if fix_result['chunks_fixed'] > 0:
                    documents_fixed += 1

                logger.info(f"  Fixed {fix_result['chunks_fixed']} chunks for document")
            else:
                logger.info(f"Skipping document {document_id} ({document.filename}) - no orphaned chunks")
                results.append({
                    "document_id": document_id,
                    "filename": document.filename,
                    "chunks_fixed": 0,
                    "status": "skipped"
                })

        # Log summary
        logger.info(f"Summary: Processed {len(documents)} documents")
        logger.info(f"  Documents fixed: {documents_fixed}")
        logger.info(f"  Total chunks fixed: {total_fixed}")

        return results

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description='Check and fix document-chunk relationships in Neo4j')
    parser.add_argument('--document-id', help='Document ID to check/fix')
    parser.add_argument('--check-all', action='store_true', help='Check all documents')
    parser.add_argument('--fix-all', action='store_true', help='Fix all documents')
    parser.add_argument('--fix', action='store_true', help='Fix the specified document')

    args = parser.parse_args()

    if args.check_all:
        logger.info("Checking all documents...")
        results = check_all_documents()
        logger.info(f"Checked {len(results)} documents")

        # Summarize results
        documents_with_chunks = sum(1 for r in results if r['chunk_count'] > 0)
        total_chunks = sum(r['chunk_count'] for r in results)
        logger.info(f"Summary: {documents_with_chunks}/{len(results)} documents have chunks, {total_chunks} total chunks")

    elif args.fix_all:
        logger.info("Fixing all documents...")
        results = fix_all_documents()
        logger.info(f"Fixed {len(results)} documents")

        # Summarize results
        total_fixed = sum(r['chunks_fixed'] for r in results)
        logger.info(f"Summary: Fixed {total_fixed} chunks across all documents")

    elif args.document_id:
        if args.fix:
            logger.info(f"Fixing document {args.document_id}...")
            fix_document_chunks(args.document_id)
        else:
            logger.info(f"Checking document {args.document_id}...")
            check_document_chunks(args.document_id)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()
