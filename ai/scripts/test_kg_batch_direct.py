#!/usr/bin/env python
"""
Test knowledge graph batch processing with direct database connection.

This script:
1. Creates a sample document
2. Processes it with the knowledge graph service
3. Checks the results

Usage:
    python ai/scripts/test_kg_batch_direct.py
"""
import os
import sys
import uuid
import json
import argparse
from pathlib import Path
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

# Import SQLAlchemy models
from sqlalchemy import Column, String, Text, DateTime, ForeignKey, JSON, func
from sqlalchemy.dialects.postgresql import UUID as PGUUID, JSONB
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship

# Create a custom Base for our models
TestBase = declarative_base()

# Define models that match the actual database schema
class TestDocument(TestBase):
    """Document model for testing."""
    __tablename__ = "documents"

    id = Column(PGUUID, primary_key=True)
    title = Column(String(255), nullable=False)
    content = Column(Text)
    source_url = Column(String(500))
    document_type = Column(String(100), nullable=False)
    doc_metadata = Column(JSONB)
    status = Column(String(50), default="pending")
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())


class TestProcessingTask(TestBase):
    """Processing task model for testing."""
    __tablename__ = "processing_tasks"

    id = Column(PGUUID, primary_key=True)
    document_id = Column(PGUUID, ForeignKey("documents.id"), nullable=False)
    task_type = Column(String(100), nullable=False)
    celery_task_id = Column(String(255))
    status = Column(String(50), default="pending")
    result = Column(JSONB)
    error = Column(Text)
    task_metadata = Column(JSONB)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

from services.knowledge_graph_service import KnowledgeGraphService
from services.kg_entity_relationship_processor import EntityRelationshipProcessor
from transport.vertex_ai_batch_client import VertexAIBatchClient
from transport.database_client import DatabaseClient
from transport.neo4j_client import Neo4jClient
from transport.storage_client import StorageClient


def parse_args():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Test knowledge graph batch processing with direct database connection.")
    parser.add_argument("--sample-file", default="tests/data/sample_vertex_ai_batch_response.jsonl",
                        help="Path to the sample Vertex AI batch response file")
    parser.add_argument("--verbose", action="store_true", help="Enable verbose output")
    parser.add_argument("--cleanup", action="store_true", help="Clean up test data after running")
    return parser.parse_args()


def main():
    """Run the test."""
    args = parse_args()

    # Override settings for testing
    from common.config import settings

    # Database settings
    settings.POSTGRES_USER = "longevity"
    settings.POSTGRES_PASSWORD = "longevity"
    settings.POSTGRES_DB = "longevity_test"
    settings.POSTGRES_HOST = "localhost"  # Connect to localhost from outside Docker
    settings.POSTGRES_PORT = 5435  # Mapped port from docker-compose.test.yml
    settings.DATABASE_URL = f"postgresql://{settings.POSTGRES_USER}:{settings.POSTGRES_PASSWORD}@{settings.POSTGRES_HOST}:{settings.POSTGRES_PORT}/{settings.POSTGRES_DB}"

    # Print the database URL for debugging
    print(f"Database URL: {settings.DATABASE_URL}")

    # MinIO settings
    settings.MINIO_ROOT_USER = "longevity"
    settings.MINIO_ROOT_PASSWORD = "longevity"
    settings.MINIO_HOST = "localhost"  # Connect to localhost from outside Docker
    settings.MINIO_PORT = 9003  # Mapped port from docker-compose.test.yml
    settings.MINIO_SECURE = False
    settings.STORAGE_URL = f"http://{settings.MINIO_HOST}:{settings.MINIO_PORT}"
    settings.STORAGE_BUCKET_NAME = "test-longevity-documents"

    # Neo4j settings
    settings.NEO4J_HOST = "localhost"  # Connect to localhost from outside Docker
    settings.NEO4J_PORT = 7687  # Mapped port from docker-compose.test.yml
    settings.NEO4J_USER = "neo4j"
    settings.NEO4J_PASSWORD = "longevity"

    # Load the sample response
    sample_file = Path(args.sample_file)
    if not sample_file.exists():
        print(f"Sample file {sample_file} not found")
        return 1

    # Create a direct database connection
    engine = create_engine(settings.DATABASE_URL)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db_session = SessionLocal()

    try:
        # Create database client
        db_client = DatabaseClient(db_session)

        # Create Neo4j client
        neo4j_client = Neo4jClient()

        # Create storage client
        storage_client = StorageClient()

        # Create a sample document directly using SQLAlchemy
        document_id = uuid.uuid4()
        document = db_session.execute(
            text("INSERT INTO documents (id, title, content, source_url, document_type, doc_metadata, status) "
            "VALUES (:id, :title, :content, :source_url, :document_type, :doc_metadata, :status) RETURNING id"),
            {
                "id": document_id,
                "title": "Test Document",
                "content": "Test content for knowledge graph batch processing",
                "source_url": "https://example.com/test",
                "document_type": "text",
                "doc_metadata": json.dumps({"test": True}),
                "status": "pending"
            }
        ).fetchone()[0]
        db_session.commit()

        if args.verbose:
            print(f"Created document with ID: {document_id}")

        # Create a sample task directly using SQLAlchemy
        task_id = uuid.uuid4()
        batch_job_id = f"projects/test-project/locations/us-central1/batchPredictionJobs/test-batch-{uuid.uuid4().hex[:8]}"
        db_session.execute(
            text("INSERT INTO processing_tasks (id, document_id, task_type, status, result) "
            "VALUES (:id, :document_id, :task_type, :status, :result) RETURNING id"),
            {
                "id": task_id,
                "document_id": document_id,
                "task_type": "knowledge_graph",
                "status": "ready_to_continue",
                "result": json.dumps({"batch_job_id": batch_job_id})
            }
        ).fetchone()[0]
        db_session.commit()

        if args.verbose:
            print(f"Created task with ID: {task_id}")

        # Process the batch results
        processor = EntityRelationshipProcessor()
        results = processor.parse_vertex_ai_response(sample_file)

        if args.verbose:
            print(f"Parsed {len(results)} chunks from the sample response")
            for i, result in enumerate(results):
                print(f"Chunk {i+1}: {result['chunk_id']}")
                print(f"  Entities: {len(result['entities'])}")
                print(f"  Relationships: {len(result['relationships'])}")

        # Mock the VertexAIBatchClient to return our sample response
        vertex_client = VertexAIBatchClient()

        # Patch the get_batch_results method
        original_get_batch_results = vertex_client.get_batch_results

        def mock_get_batch_results(batch_job_id):
            return results

        vertex_client.get_batch_results = mock_get_batch_results

        try:
            # Create the service with our mocked client
            service = KnowledgeGraphService()

            # Patch the service to use our mocked client
            service.vertex_client = vertex_client

            # Patch the DataTransport class to use our database connection
            from transport.data_transport import DataTransport
            original_enter = DataTransport.__enter__

            def patched_enter(self):
                self.db_session = db_session
                self.db_client = DatabaseClient(self.db_session)
                return self

            DataTransport.__enter__ = patched_enter

            # Call the method
            result = service.continue_knowledge_graph_building(str(task_id))

            # Print the result
            print("\nKnowledge graph building result:")
            print(f"  Status: {result['status']}")
            print(f"  Document ID: {result['document_id']}")
            print(f"  Entities count: {result['entities_count']}")
            print(f"  Relationships count: {result['relationships_count']}")

            # Verify the task status was updated
            task = db_client.get_processing_task(task_id)
            print(f"  Task status: {task.status}")

            # Get the entities and relationships from the database
            entities = db_client.get_entities_by_document(document_id)
            relationships = db_client.get_relationships_by_document(document_id)

            print(f"\nEntities in database: {len(entities)}")
            if args.verbose and entities:
                for i, entity in enumerate(entities[:5]):  # Show only the first 5
                    print(f"  Entity {i+1}: {entity.text} ({entity.entity_type})")
                if len(entities) > 5:
                    print(f"  ... and {len(entities) - 5} more")

            print(f"\nRelationships in database: {len(relationships)}")
            if args.verbose and relationships:
                for i, rel in enumerate(relationships[:5]):  # Show only the first 5
                    source = db_client.get_entity(rel.source_id)
                    target = db_client.get_entity(rel.target_id)
                    print(f"  Relationship {i+1}: {source.text} --{rel.relationship_type}--> {target.text}")
                if len(relationships) > 5:
                    print(f"  ... and {len(relationships) - 5} more")

            # Check Neo4j
            try:
                neo4j_entities = neo4j_client.get_all_entities()
                neo4j_relationships = neo4j_client.get_all_relationships()

                print(f"\nEntities in Neo4j: {len(neo4j_entities)}")
                print(f"Relationships in Neo4j: {len(neo4j_relationships)}")
            except Exception as e:
                print(f"\nError checking Neo4j: {str(e)}")

            return 0
        finally:
            # Restore the original methods
            vertex_client.get_batch_results = original_get_batch_results
            DataTransport.__enter__ = original_enter

            # Clean up if requested
            if args.cleanup:
                print("\nCleaning up test data...")
                db_client.delete_processing_task(task_id)
                db_client.delete_document(document_id)
    except Exception as e:
        print(f"Error: {str(e)}")
        return 1
    finally:
        # Close the database session
        db_session.close()

        # Close Neo4j client
        neo4j_client.close()

    return 0


if __name__ == "__main__":
    sys.exit(main())
