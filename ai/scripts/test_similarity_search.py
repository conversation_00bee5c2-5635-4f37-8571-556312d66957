"""
<PERSON><PERSON><PERSON> to test the similarity search functionality.

This script:
1. Connects to the database
2. Creates a test document with chunks
3. Performs similarity searches using different methods
4. Cleans up the test data

Usage:
    python ai/scripts/test_similarity_search.py
"""
import sys
import os
import uuid
import logging
import numpy as np
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from common.config import settings
from common.database import Document, Chunk, Base
from transport.database_client import DatabaseClient
from services.nlp_service import NLPService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_test_document_with_chunks(db_session):
    """Create a test document with chunks for similarity search testing."""
    logger.info("Creating test document with chunks")
    
    # Create a test document
    document_id = uuid.uuid4()
    document = Document(
        id=document_id,
        filename="similarity_test.txt",
        source_path="upload",
        content_type="text/plain",
        status="processed"
    )
    
    db_session.add(document)
    db_session.flush()
    
    # Create test chunks with E5-Large-V2 compatible embeddings (1024 dimensions)
    chunks = []
    
    # Chunk 1: About vitamin D and longevity
    chunk1_id = uuid.uuid4()
    chunk1 = Chunk(
        id=chunk1_id,
        document_id=document_id,
        chunk_index=0,
        text="Vitamin D deficiency is common in northern climates and has been linked to various health issues including reduced longevity. Supplementation may help improve overall health outcomes.",
        chunk_metadata={"topic": "nutrition", "subtopic": "vitamins"},
        embedding=np.random.rand(settings.EMBEDDING_DIMENSION).astype(np.float32).tolist()  # Random 1024-dim vector
    )
    chunks.append(chunk1)
    
    # Chunk 2: About exercise and longevity
    chunk2_id = uuid.uuid4()
    chunk2 = Chunk(
        id=chunk2_id,
        document_id=document_id,
        chunk_index=1,
        text="Regular exercise is one of the most effective interventions for extending healthy lifespan. Both aerobic and resistance training provide complementary benefits for longevity.",
        chunk_metadata={"topic": "exercise", "subtopic": "longevity"},
        embedding=np.random.rand(settings.EMBEDDING_DIMENSION).astype(np.float32).tolist()  # Random 1024-dim vector
    )
    chunks.append(chunk2)
    
    # Chunk 3: About nutrition and longevity
    chunk3_id = uuid.uuid4()
    chunk3 = Chunk(
        id=chunk3_id,
        document_id=document_id,
        chunk_index=2,
        text="Proper nutrition plays a crucial role in healthy aging. Mediterranean and plant-based diets have been associated with longer lifespans in multiple studies.",
        chunk_metadata={"topic": "nutrition", "subtopic": "diet"},
        embedding=np.random.rand(settings.EMBEDDING_DIMENSION).astype(np.float32).tolist()  # Random 1024-dim vector
    )
    chunks.append(chunk3)
    
    # Add all chunks to the database
    db_session.add_all(chunks)
    db_session.commit()
    
    logger.info(f"Created document with ID: {document_id}")
    logger.info(f"Created chunks with IDs: {chunk1_id}, {chunk2_id}, {chunk3_id}")
    
    return {
        "document_id": document_id,
        "chunk_ids": {
            "vitamin_d": chunk1_id,
            "exercise": chunk2_id,
            "nutrition": chunk3_id
        }
    }


def test_similarity_search():
    """Test the similarity search functionality."""
    # Connect to the database
    engine = create_engine(settings.DATABASE_URL)
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        # Create test data
        test_data = create_test_document_with_chunks(session)
        document_id = test_data["document_id"]
        chunk_ids = test_data["chunk_ids"]
        
        # Create a database client
        db_client = DatabaseClient(session)
        
        # Test 1: Search by query embedding
        logger.info("Test 1: Search by query embedding")
        query = "What are the benefits of vitamin D supplementation?"
        query_embedding = NLPService.generate_query_embedding(query)
        
        results = db_client.search_similar(
            embedding=query_embedding.tolist(),
            limit=3
        )
        
        logger.info(f"Query: {query}")
        logger.info(f"Found {len(results)} results")
        for i, result in enumerate(results):
            logger.info(f"Result {i+1}:")
            logger.info(f"  Chunk ID: {result['chunk_id']}")
            logger.info(f"  Text: {result['text'][:100]}...")
            logger.info(f"  Similarity: {result['similarity']}")
        
        # Test 2: Find similar by chunk ID
        logger.info("\nTest 2: Find similar by chunk ID")
        vitamin_d_chunk_id = chunk_ids["vitamin_d"]
        
        results = db_client.find_similar_by_chunk_id(
            chunk_id=vitamin_d_chunk_id,
            limit=2
        )
        
        logger.info(f"Source chunk ID: {vitamin_d_chunk_id}")
        logger.info(f"Found {len(results)} similar chunks")
        for i, result in enumerate(results):
            logger.info(f"Result {i+1}:")
            logger.info(f"  Chunk ID: {result['chunk_id']}")
            logger.info(f"  Text: {result['text'][:100]}...")
            logger.info(f"  Similarity: {result['similarity']}")
        
        # Test 3: Search with minimum similarity
        logger.info("\nTest 3: Search with minimum similarity")
        query = "What are the benefits of exercise for longevity?"
        query_embedding = NLPService.generate_query_embedding(query)
        
        results = db_client.search_similar(
            embedding=query_embedding.tolist(),
            limit=3,
            min_similarity=0.5  # Set a minimum similarity threshold
        )
        
        logger.info(f"Query: {query}")
        logger.info(f"Found {len(results)} results with similarity >= 0.5")
        for i, result in enumerate(results):
            logger.info(f"Result {i+1}:")
            logger.info(f"  Chunk ID: {result['chunk_id']}")
            logger.info(f"  Text: {result['text'][:100]}...")
            logger.info(f"  Similarity: {result['similarity']}")
        
        # Test 4: Search with document filter
        logger.info("\nTest 4: Search with document filter")
        query = "What are the benefits of proper nutrition?"
        query_embedding = NLPService.generate_query_embedding(query)
        
        results = db_client.search_similar(
            embedding=query_embedding.tolist(),
            limit=3,
            filter_document_id=document_id  # Filter by document ID
        )
        
        logger.info(f"Query: {query}")
        logger.info(f"Found {len(results)} results from document {document_id}")
        for i, result in enumerate(results):
            logger.info(f"Result {i+1}:")
            logger.info(f"  Chunk ID: {result['chunk_id']}")
            logger.info(f"  Document ID: {result['document_id']}")
            logger.info(f"  Text: {result['text'][:100]}...")
            logger.info(f"  Similarity: {result['similarity']}")
        
    finally:
        # Clean up
        logger.info("\nCleaning up test data")
        session.query(Chunk).filter(Chunk.document_id == document_id).delete()
        session.query(Document).filter(Document.id == document_id).delete()
        session.commit()
        session.close()
        logger.info("Test data cleaned up")


if __name__ == "__main__":
    test_similarity_search()
