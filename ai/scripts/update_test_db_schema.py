"""
<PERSON><PERSON><PERSON> to update the test database schema for the new embedding dimension.

This script:
1. Connects to the test database
2. Drops the existing chunks table
3. Recreates the chunks table with the new embedding dimension
4. Creates the pgvector extension if it doesn't exist

Usage:
    python ai/scripts/update_test_db_schema.py
"""
import sys
import os
import logging
from sqlalchemy import create_engine, text

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '../..')))

from common.config import settings

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# IMPORTANT: For this script to correctly use 'test_local' settings,
# the APP_ENV environment variable should be set to 'test_local'
# *before* this script is executed. For example:
# APP_ENV=test_local python ai/scripts/update_test_db_schema.py
# Setting it here might be too late if 'common.config.settings' was already imported
# and initialized by its top-level import in this script.
os.environ['APP_ENV'] = 'test_local'

from common.config import settings # settings should now reflect APP_ENV (with caveats)

# Manual overrides for POSTGRES settings and DATABASE_URL have been removed.
# This script now relies on common.config.settings, configured by APP_ENV.
# Note: The original script hardcoded POSTGRES_USER="postgres" and POSTGRES_PASSWORD="postgres".
# The 'test_local' environment in common.config.Settings uses "longevity" for user/pass.
# If this script specifically needs "postgres" user/pass, then APP_ENV=test_local
# might not be suitable, or the .env.test_local needs to be adjusted, or
# this script needs a more specific APP_ENV value that points to a different configuration.
# For now, it will use the "longevity" user/pass from 'test_local' settings.

# The embedding dimension is part of the main Settings class and should be configured there
# or via environment variables that Settings picks up, not overridden directly in scripts.
# settings.EMBEDDING_DIMENSION = 1024 # This should be configured via Settings defaults or .env

logger.info(f"Running with APP_ENV: {os.environ.get('APP_ENV')}")
logger.info(f"Using Database URL: {settings.DATABASE_URL}")
logger.info(f"Targeting Postgres Host: {settings.POSTGRES_HOST}, Port: {settings.POSTGRES_PORT}, DB: {settings.POSTGRES_DB}")
logger.info(f"Using Embedding Dimension: {settings.EMBEDDING_DIMENSION}")


def update_test_db_schema():
    """Update the test database schema for the new embedding dimension."""
    logger.info(f"Connecting to test database: {settings.DATABASE_URL}")
    engine = create_engine(settings.DATABASE_URL)
    
    with engine.connect() as connection:
        # Create a transaction
        with connection.begin():
            # Create pgvector extension if it doesn't exist
            logger.info("Creating pgvector extension if it doesn't exist")
            connection.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))
            
            # Drop the existing chunks table
            logger.info("Dropping existing chunks table")
            connection.execute(text("DROP TABLE IF EXISTS chunks"))
            
            # Recreate the chunks table with the new embedding dimension
            logger.info(f"Recreating chunks table with embedding dimension: {settings.EMBEDDING_DIMENSION}")
            connection.execute(text(f"""
                CREATE TABLE chunks (
                    id UUID PRIMARY KEY,
                    document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
                    chunk_index INTEGER NOT NULL,
                    text TEXT NOT NULL,
                    chunk_metadata JSONB,
                    embedding VECTOR({settings.EMBEDDING_DIMENSION}),
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    UNIQUE (document_id, chunk_index)
                )
            """))
            
            # Create vector index for embeddings
            logger.info("Creating vector index for embeddings")
            connection.execute(text("""
                CREATE INDEX IF NOT EXISTS idx_chunks_embedding 
                ON chunks USING ivfflat (embedding vector_cosine_ops) 
                WITH (lists = 100)
            """))
    
    logger.info("Test database schema updated successfully")


if __name__ == "__main__":
    update_test_db_schema()
