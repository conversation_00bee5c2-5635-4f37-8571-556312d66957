#!/usr/bin/env python
"""
Sanity check script for knowledge graph async batch processing flow.

This script tests the entire flow of asynchronous knowledge graph building:
1. Submits a document for async knowledge graph building
2. Checks the status of the batch job
3. Manually triggers the continuation of knowledge graph building
4. Verifies the results in PostgreSQL and Neo4j

Usage:
    python ai/scripts/check_kg_async_flow.py --document_id <document_id>
    python ai/scripts/check_kg_async_flow.py --document_id <document_id> --check_only
    python ai/scripts/check_kg_async_flow.py --document_id <document_id> --continue_task <task_id>
"""

import argparse
import json
import logging
import sys
import time
import uuid
from typing import Dict, Any, Optional, List, Union

# Add parent directory to path
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from common.database import ProcessingTask
from services.knowledge_graph_service import KnowledgeGraphService
from transport.data_transport import DataTransport
from transport.vertex_ai_batch_client import VertexAIBatchClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def submit_async_kg_job(document_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
    """
    Submit a document for async knowledge graph building.
    
    Args:
        document_id: Document ID
        
    Returns:
        Dict[str, Any]: Job submission result
    """
    logger.info(f"Submitting async knowledge graph job for document: {document_id}")
    
    # Create a task ID
    task_id = uuid.uuid4()
    
    # Create KnowledgeGraphService instance
    service = KnowledgeGraphService()
    
    # Build knowledge graph asynchronously
    result = service.build_knowledge_graph_from_document(
        document_id=document_id,
        task_id=task_id,
        async_mode=True
    )
    
    logger.info(f"Async job submitted: {json.dumps(result, indent=2)}")
    return result


def check_batch_job_status(task_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
    """
    Check the status of a batch job.
    
    Args:
        task_id: Processing task ID
        
    Returns:
        Dict[str, Any]: Job status
    """
    logger.info(f"Checking batch job status for task: {task_id}")
    
    # Create KnowledgeGraphService instance
    service = KnowledgeGraphService()
    
    # Check batch job status
    result = service.check_batch_job_status(task_id)
    
    logger.info(f"Batch job status: {json.dumps(result, indent=2)}")
    return result


def continue_knowledge_graph_building(task_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
    """
    Continue knowledge graph building after batch job completes.
    
    Args:
        task_id: Processing task ID
        
    Returns:
        Dict[str, Any]: Processing result
    """
    logger.info(f"Continuing knowledge graph building for task: {task_id}")
    
    # Create KnowledgeGraphService instance
    service = KnowledgeGraphService()
    
    # Continue knowledge graph building
    result = service.continue_knowledge_graph_building(task_id)
    
    logger.info(f"Knowledge graph building continued: {json.dumps(result, indent=2)}")
    return result


def check_and_continue_batch_jobs() -> Dict[str, Any]:
    """
    Run the periodic task to check for completed batch jobs and continue processing.
    
    Returns:
        Dict[str, Any]: Summary of processed tasks
    """
    logger.info("Running periodic check for Vertex AI batch jobs")
    
    # Create KnowledgeGraphService instance
    service = KnowledgeGraphService()
    
    # Check and continue batch jobs
    result = service.check_and_continue_batch_jobs()
    
    logger.info(f"Batch jobs checked: {json.dumps(result, indent=2)}")
    return result


def get_task_info(task_id: Union[str, uuid.UUID]) -> Optional[Dict[str, Any]]:
    """
    Get information about a processing task.
    
    Args:
        task_id: Processing task ID
        
    Returns:
        Optional[Dict[str, Any]]: Task information
    """
    logger.info(f"Getting information for task: {task_id}")
    
    with DataTransport() as transport:
        task = transport.db_client.get_processing_task(task_id)
        if not task:
            logger.error(f"Task {task_id} not found")
            return None
        
        task_info = {
            "id": str(task.id),
            "document_id": str(task.document_id),
            "task_type": task.task_type,
            "status": task.status,
            "result": task.result,
            "error": task.error,
            "created_at": task.created_at.isoformat() if task.created_at else None,
            "updated_at": task.updated_at.isoformat() if task.updated_at else None
        }
        
        logger.info(f"Task info: {json.dumps(task_info, indent=2)}")
        return task_info


def get_pending_batch_jobs() -> List[Dict[str, Any]]:
    """
    Get all pending batch jobs.
    
    Returns:
        List[Dict[str, Any]]: List of pending batch jobs
    """
    logger.info("Getting pending batch jobs")
    
    with DataTransport() as transport:
        tasks = transport.db_client.db.query(ProcessingTask).filter(
            ProcessingTask.task_type == "knowledge_graph_building",
            ProcessingTask.status.in_(["batch_job_submitted"]),
            ProcessingTask.result.contains({"batch_job_id": None}).is_(False),
            ProcessingTask.result.contains({"async_processing": None}).is_(False)
        ).all()
        
        pending_jobs = []
        for task in tasks:
            pending_jobs.append({
                "id": str(task.id),
                "document_id": str(task.document_id),
                "status": task.status,
                "batch_job_id": task.result.get("batch_job_id") if task.result else None,
                "created_at": task.created_at.isoformat() if task.created_at else None
            })
        
        logger.info(f"Found {len(pending_jobs)} pending batch jobs")
        return pending_jobs


def check_vertex_ai_batch_job(batch_job_id: str) -> Optional[str]:
    """
    Check the status of a Vertex AI batch job directly.
    
    Args:
        batch_job_id: Batch job ID
        
    Returns:
        Optional[str]: Job state or None if error
    """
    logger.info(f"Checking Vertex AI batch job status: {batch_job_id}")
    
    try:
        # Create VertexAIBatchClient instance
        client = VertexAIBatchClient()
        
        # Get batch job state
        job_state = client.get_batch_job_state(batch_job_id)
        
        logger.info(f"Batch job state: {job_state}")
        return job_state
    except Exception as e:
        logger.error(f"Error checking batch job status: {e}")
        return None


def check_entities_in_neo4j(document_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
    """
    Check entities and relationships in Neo4j for a document.
    
    Args:
        document_id: Document ID
        
    Returns:
        Dict[str, Any]: Neo4j statistics
    """
    logger.info(f"Checking Neo4j entities for document: {document_id}")
    
    with DataTransport() as transport:
        neo4j_client = transport.neo4j_client
        
        # Get document node
        document_node = neo4j_client.get_document(str(document_id))
        if not document_node:
            logger.warning(f"Document node not found in Neo4j: {document_id}")
            return {"document_exists": False}
        
        # Count entities
        entity_count = neo4j_client.count_entities_for_document(str(document_id))
        
        # Count relationships
        relationship_count = neo4j_client.count_relationships_for_document(str(document_id))
        
        # Count chunks
        chunk_count = neo4j_client.count_chunks_for_document(str(document_id))
        
        result = {
            "document_exists": True,
            "entity_count": entity_count,
            "relationship_count": relationship_count,
            "chunk_count": chunk_count
        }
        
        logger.info(f"Neo4j statistics: {json.dumps(result, indent=2)}")
        return result


def check_entities_in_postgres(document_id: Union[str, uuid.UUID]) -> Dict[str, Any]:
    """
    Check entities in PostgreSQL for a document.
    
    Args:
        document_id: Document ID
        
    Returns:
        Dict[str, Any]: PostgreSQL statistics
    """
    logger.info(f"Checking PostgreSQL entities for document: {document_id}")
    
    with DataTransport() as transport:
        # Count entities
        entities = transport.db_client.get_document_entities(document_id)
        entity_count = len(entities) if entities else 0
        
        # Count chunks
        chunks = transport.get_document_chunks(document_id, chunk_type="kg")
        chunk_count = len(chunks) if chunks else 0
        
        result = {
            "entity_count": entity_count,
            "chunk_count": chunk_count
        }
        
        logger.info(f"PostgreSQL statistics: {json.dumps(result, indent=2)}")
        return result


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Knowledge Graph Async Flow Check")
    parser.add_argument("--document_id", help="Document ID to process")
    parser.add_argument("--check_only", action="store_true", help="Only check existing tasks")
    parser.add_argument("--continue_task", help="Task ID to continue")
    parser.add_argument("--wait", action="store_true", help="Wait for batch job to complete")
    parser.add_argument("--wait_timeout", type=int, default=300, help="Timeout in seconds for waiting")
    
    args = parser.parse_args()
    
    if args.check_only:
        # Get pending batch jobs
        pending_jobs = get_pending_batch_jobs()
        print(f"Found {len(pending_jobs)} pending batch jobs:")
        for job in pending_jobs:
            print(f"  Task ID: {job['id']}")
            print(f"  Document ID: {job['document_id']}")
            print(f"  Status: {job['status']}")
            print(f"  Batch Job ID: {job['batch_job_id']}")
            print(f"  Created At: {job['created_at']}")
            print("")
            
            # Check Vertex AI batch job status
            if job['batch_job_id']:
                job_state = check_vertex_ai_batch_job(job['batch_job_id'])
                print(f"  Vertex AI Job State: {job_state}")
                print("")
        
        return
    
    if args.continue_task:
        # Continue knowledge graph building for a specific task
        task_id = args.continue_task
        
        # Get task info
        task_info = get_task_info(task_id)
        if not task_info:
            print(f"Task {task_id} not found")
            return
        
        print(f"Continuing knowledge graph building for task: {task_id}")
        result = continue_knowledge_graph_building(task_id)
        print(f"Result: {json.dumps(result, indent=2)}")
        
        # Check entities in Neo4j and PostgreSQL
        document_id = task_info['document_id']
        neo4j_stats = check_entities_in_neo4j(document_id)
        postgres_stats = check_entities_in_postgres(document_id)
        
        print(f"Neo4j statistics: {json.dumps(neo4j_stats, indent=2)}")
        print(f"PostgreSQL statistics: {json.dumps(postgres_stats, indent=2)}")
        
        return
    
    if not args.document_id:
        print("Document ID is required")
        return
    
    # Submit async knowledge graph job
    document_id = args.document_id
    result = submit_async_kg_job(document_id)
    
    task_id = result.get('task_id')
    if not task_id:
        print("No task ID returned")
        return
    
    print(f"Async job submitted for document {document_id}")
    print(f"Task ID: {task_id}")
    
    if args.wait:
        print(f"Waiting for batch job to complete (timeout: {args.wait_timeout} seconds)...")
        
        start_time = time.time()
        while time.time() - start_time < args.wait_timeout:
            # Check task status
            task_info = get_task_info(task_id)
            if not task_info:
                print(f"Task {task_id} not found")
                return
            
            status = task_info['status']
            print(f"Task status: {status}")
            
            if status == 'completed':
                print("Task completed successfully")
                break
            
            if status == 'failed':
                print(f"Task failed: {task_info['error']}")
                return
            
            # Check batch job status
            batch_job_id = task_info['result'].get('batch_job_id') if task_info['result'] else None
            if batch_job_id:
                job_state = check_vertex_ai_batch_job(batch_job_id)
                print(f"Vertex AI Job State: {job_state}")
                
                if job_state == 'JOB_STATE_SUCCEEDED':
                    print("Batch job completed, continuing knowledge graph building...")
                    result = continue_knowledge_graph_building(task_id)
                    print(f"Result: {json.dumps(result, indent=2)}")
                    break
            
            # Wait before checking again
            time.sleep(10)
        
        # Check if timeout was reached
        if time.time() - start_time >= args.wait_timeout:
            print(f"Timeout reached after {args.wait_timeout} seconds")
        
        # Check entities in Neo4j and PostgreSQL
        neo4j_stats = check_entities_in_neo4j(document_id)
        postgres_stats = check_entities_in_postgres(document_id)
        
        print(f"Neo4j statistics: {json.dumps(neo4j_stats, indent=2)}")
        print(f"PostgreSQL statistics: {json.dumps(postgres_stats, indent=2)}")
    else:
        print("Job submitted, use --continue_task to continue processing later")


if __name__ == "__main__":
    main()
