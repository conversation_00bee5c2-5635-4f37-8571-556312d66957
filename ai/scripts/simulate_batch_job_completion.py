#!/usr/bin/env python3
"""
Script to simulate a completed Vertex AI batch job and process the results.

This script:
1. Updates the status of a knowledge graph building task to "batch_job_completed"
2. Copies the sample Vertex AI batch response to the expected location
3. Triggers the continue_knowledge_graph_building task to process the results

Usage:
    python ai/scripts/simulate_batch_job_completion.py <task_id>
"""

import sys
import os
import json
import uuid
import shutil
import psycopg2
from datetime import datetime
from psycopg2.extras import Json

# Database connection parameters
DB_PARAMS = {
    "host": "localhost",
    "port": 5435,
    "database": "longevity",
    "user": "longevity",
    "password": "longevitypass"
}

def get_task_info(task_id):
    """Get information about a knowledge graph building task."""
    conn = psycopg2.connect(**DB_PARAMS)
    cursor = conn.cursor()

    cursor.execute(
        "SELECT id, document_id, status, result FROM processing_tasks WHERE id = %s",
        (task_id,)
    )

    task = cursor.fetchone()
    cursor.close()
    conn.close()

    if not task:
        print(f"Task with ID {task_id} not found")
        return None

    return {
        "id": task[0],
        "document_id": task[1],
        "status": task[2],
        "result": task[3]
    }

def update_task_status(task_id, status, result):
    """Update the status and result of a knowledge graph building task."""
    conn = psycopg2.connect(**DB_PARAMS)
    cursor = conn.cursor()

    cursor.execute(
        "UPDATE processing_tasks SET status = %s, result = %s, updated_at = %s WHERE id = %s",
        (status, Json(result), datetime.now(), task_id)
    )

    conn.commit()
    cursor.close()
    conn.close()

    print(f"Updated task {task_id} status to {status}")

def copy_sample_response(batch_job_id):
    """Copy the sample Vertex AI batch response to the expected location."""
    # Create a unique directory for the batch job results
    results_dir = f"/tmp/vertex_ai_batch_results/{batch_job_id}"
    os.makedirs(results_dir, exist_ok=True)

    # Copy the sample response file
    sample_file = "tests/data/sample_vertex_ai_batch_response.jsonl"
    dest_file = f"{results_dir}/results.jsonl"

    shutil.copy(sample_file, dest_file)

    print(f"Copied sample response to {dest_file}")
    return dest_file

def trigger_continue_task(task_id):
    """Trigger the continue_knowledge_graph_building task."""
    # In a real environment, we would use Celery to trigger the task
    # Here we'll just print a message
    print(f"Triggering continue_knowledge_graph_building task for task_id {task_id}")
    print("In a real environment, you would run:")
    print(f"  from workers.knowledge_graph import continue_knowledge_graph_building")
    print(f"  continue_knowledge_graph_building.delay('{task_id}')")

def main():
    if len(sys.argv) != 2:
        print("Usage: python simulate_batch_job_completion.py <task_id>")
        sys.exit(1)

    task_id = sys.argv[1]

    # Get task information
    task = get_task_info(task_id)
    if not task:
        sys.exit(1)

    print(f"Found task: {task}")

    # Generate a batch job ID if not present
    result = task["result"] or {}
    batch_job_id = result.get("batch_job_id")
    if not batch_job_id:
        batch_job_id = f"projects/123456789/locations/us-central1/batchPredictionJobs/{uuid.uuid4()}"
        result["batch_job_id"] = batch_job_id

    # Update the task status to "batch_job_completed"
    result["batch_job_status"] = "completed"
    result["completed_at"] = datetime.now().timestamp()
    update_task_status(task_id, "batch_job_completed", result)

    # Copy the sample response
    results_file = copy_sample_response(batch_job_id)

    # Update the task result with the results file
    result["results_file"] = results_file
    update_task_status(task_id, "batch_job_completed", result)

    # Trigger the continue task
    trigger_continue_task(task_id)

    print("Done!")

if __name__ == "__main__":
    main()
