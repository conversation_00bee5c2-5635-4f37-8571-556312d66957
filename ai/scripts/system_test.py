#!/usr/bin/env python
"""
Comprehensive system test script for document ingestion and knowledge graph building.

This script follows the specified test rules:
1. <PERSON><PERSON> compose up (without build - assumes containers are already built)
2. Remove all old data in databases
3. Start ingesting with full automation after calling ingest endpoint
4. Check RAG chunks and KG chunks are created
5. Verify entities extracted, normalized and inserted in PostgreSQL
6. Check Neo4j contains entities, relationships, chunks, and documents

Test files:
- PubMed articles with query "longevity diet" (2 articles)
- sample_txt.txt
- sample_epub.epub
- sample_pdf.pdf
- sample_url.txt (contains URL to fetch)
"""
import os
import sys
import time
import json
import logging
import requests
import subprocess
from pathlib import Path
from typing import Optional

# Optional imports - will be imported when needed
psycopg2 = None
GraphDatabase = None

def import_database_libs():
    """Import database libraries when needed."""
    global psycopg2, GraphDatabase

    if psycopg2 is None:
        try:
            import psycopg2 as pg
            psycopg2 = pg
        except ImportError:
            logger.warning("psycopg2 not available - PostgreSQL verification will be skipped")

    if GraphDatabase is None:
        try:
            from neo4j import GraphDatabase as gdb
            GraphDatabase = gdb
        except ImportError:
            logger.warning("neo4j driver not available - Neo4j verification will be skipped")

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Constants
API_URL = "http://localhost:8000"
TEST_DATA_DIR = Path("tests/data")
SAMPLE_FILES = [
    "sample_txt.txt",
    "sample_epub.epub",
    "sample_pdf.pdf",
    "sample_url.txt"
]

# Test configuration
PUBMED_QUERY = "longevity diet"
PUBMED_MAX_RESULTS = 2
PROCESSING_TIMEOUT = 1800  # 30 minutes
CHECK_INTERVAL = 30  # 30 seconds


class SystemTestRunner:
    """Main system test runner class."""

    def __init__(self):
        """Initialize the system test runner."""
        self.api_url = API_URL
        self.test_data_dir = TEST_DATA_DIR
        self.ingestion_tasks = []

        # Check if test data directory exists
        if not self.test_data_dir.exists():
            logger.warning(f"Test data directory {self.test_data_dir} not found. Using current directory.")
            self.test_data_dir = Path(".")

    def run_docker_compose(self):
        """Step 1: Start Docker Compose services."""
        logger.info("=== STEP 1: Starting Docker Compose services ===")

        try:
            # Stop any existing containers
            logger.info("Stopping existing containers...")
            subprocess.run(["docker-compose", "-f", "docker-compose.vm.yml", "down"], check=False, capture_output=True)

            # Start services (without building)
            logger.info("Starting services...")
            subprocess.run(
                ["docker-compose", "-f", "docker-compose.vm.yml", "up", "-d"],
                check=True,
                capture_output=True,
                text=True
            )
            logger.info("Docker Compose services started successfully")

            # Wait for services to be ready
            logger.info("Waiting for services to be ready...")
            self._wait_for_services()

            return True

        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to start Docker Compose: {e}")
            return False

    def _wait_for_services(self):
        """Wait for all services to be ready."""
        max_attempts = 60  # 5 minutes
        attempt = 0

        while attempt < max_attempts:
            try:
                # Check API health
                response = requests.get(f"{self.api_url}/health", timeout=5)
                if response.status_code == 200:
                    logger.info("API service is ready")
                    break
            except requests.exceptions.RequestException:
                pass

            attempt += 1
            logger.info(f"Waiting for services... (attempt {attempt}/{max_attempts})")
            time.sleep(5)

        if attempt >= max_attempts:
            raise Exception("Services did not become ready within timeout")

    def clean_databases(self):
        """Step 2: Clean all databases."""
        logger.info("=== STEP 2: Cleaning databases ===")

        try:
            # Run the purge script
            logger.info("Running database purge script...")
            subprocess.run(
                ["python", "purge_db_direct.py"],
                check=True,
                capture_output=True,
                text=True,
                cwd=os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            )
            logger.info("Databases cleaned successfully")
            return True

        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to clean databases: {e}")
            return False

    def ingest_pubmed_articles(self):
        """Step 3a: Ingest PubMed articles."""
        logger.info("=== STEP 3a: Ingesting PubMed articles ===")

        try:
            url = f"{self.api_url}/api/pubmed/insert_articles_with_query"
            payload = {
                "query": PUBMED_QUERY,
                "max_results": PUBMED_MAX_RESULTS
            }

            logger.info(f"Requesting PubMed articles with query: '{PUBMED_QUERY}', max_results: {PUBMED_MAX_RESULTS}")
            response = requests.post(url, json=payload, timeout=60)
            response.raise_for_status()

            result = response.json()
            task_ids = result.get("task_ids", [])
            logger.info(f"PubMed ingestion started with {len(task_ids)} tasks: {task_ids}")

            self.ingestion_tasks.extend(task_ids)
            return True

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to ingest PubMed articles: {e}")
            return False

    def ingest_sample_files(self):
        """Step 3b: Ingest sample files."""
        logger.info("=== STEP 3b: Ingesting sample files ===")

        success_count = 0

        for filename in SAMPLE_FILES:
            file_path = self.test_data_dir / filename

            if not file_path.exists():
                logger.warning(f"Sample file not found: {file_path}")
                continue

            try:
                if filename == "sample_url.txt":
                    # Handle URL file specially
                    success = self._ingest_url_file(file_path)
                else:
                    # Handle regular file upload
                    success = self._ingest_file(file_path)

                if success:
                    success_count += 1

            except Exception as e:
                logger.error(f"Error ingesting {filename}: {e}")

        logger.info(f"Successfully started ingestion for {success_count}/{len(SAMPLE_FILES)} files")
        return success_count > 0

    def _ingest_file(self, file_path: Path):
        """Ingest a single file."""
        logger.info(f"Ingesting file: {file_path.name}")

        try:
            url = f"{self.api_url}/api/documents/ingest"

            with open(file_path, 'rb') as f:
                files = {'file': f}
                response = requests.post(url, files=files, timeout=120)
                response.raise_for_status()

            result = response.json()
            task_id = result.get("task_id")

            if task_id:
                logger.info(f"File {file_path.name} ingestion started with task ID: {task_id}")
                self.ingestion_tasks.append(task_id)
                return True
            else:
                logger.warning(f"No task ID returned for {file_path.name}")
                return False

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to ingest file {file_path.name}: {e}")
            return False

    def _ingest_url_file(self, file_path: Path):
        """Ingest URL from a text file."""
        logger.info(f"Ingesting URL from file: {file_path.name}")

        try:
            # Read URL from file
            with open(file_path, 'r') as f:
                url_to_ingest = f.read().strip()

            if not url_to_ingest:
                logger.error(f"No URL found in {file_path.name}")
                return False

            logger.info(f"Ingesting URL: {url_to_ingest}")

            # Send URL to ingest endpoint
            api_url = f"{self.api_url}/api/documents/ingest"
            data = {'url': url_to_ingest}

            response = requests.post(api_url, data=data, timeout=120)
            response.raise_for_status()

            result = response.json()
            task_id = result.get("task_id")

            if task_id:
                logger.info(f"URL ingestion started with task ID: {task_id}")
                self.ingestion_tasks.append(task_id)
                return True
            else:
                logger.warning(f"No task ID returned for URL ingestion")
                return False

        except Exception as e:
            logger.error(f"Failed to ingest URL from {file_path.name}: {e}")
            return False

    def monitor_processing(self):
        """Step 4: Monitor processing until completion."""
        logger.info("=== STEP 4: Monitoring processing ===")

        if not self.ingestion_tasks:
            logger.warning("No ingestion tasks to monitor")
            return False

        logger.info(f"Monitoring {len(self.ingestion_tasks)} ingestion tasks")

        start_time = time.time()

        while time.time() - start_time < PROCESSING_TIMEOUT:
            # Check if all processing is complete
            if self._check_processing_complete():
                logger.info("All processing completed successfully!")
                return True

            logger.info("Processing still in progress...")
            time.sleep(CHECK_INTERVAL)

        logger.error(f"Processing timeout after {PROCESSING_TIMEOUT} seconds")
        return False

    def _check_processing_complete(self):
        """Check if all processing is complete by verifying data in databases."""
        try:
            # Check if we have documents, chunks, and entities
            postgres_ready = self._check_postgres_data()
            neo4j_ready = self._check_neo4j_data()

            return postgres_ready and neo4j_ready

        except Exception as e:
            logger.warning(f"Error checking processing completion: {e}")
            return False

    def _check_postgres_data(self):
        """Check if PostgreSQL has the expected data."""
        import_database_libs()

        if psycopg2 is None:
            logger.warning("PostgreSQL verification skipped - psycopg2 not available")
            return True  # Don't fail the test for missing optional dependency

        try:
            # Connect to PostgreSQL
            conn = psycopg2.connect(
                host="localhost",
                port="5435",
                database="longevity",
                user="longevity",
                password="longevitypass"
            )
            cursor = conn.cursor()

            # Check documents
            cursor.execute("SELECT COUNT(*) FROM documents")
            doc_count = cursor.fetchone()[0]

            # Check chunks
            cursor.execute("SELECT COUNT(*) FROM chunks")
            chunk_count = cursor.fetchone()[0]

            # Check KG chunks
            cursor.execute("SELECT COUNT(*) FROM kg_chunks")
            kg_chunk_count = cursor.fetchone()[0]

            # Check entities
            cursor.execute("SELECT COUNT(*) FROM entities")
            entity_count = cursor.fetchone()[0]

            cursor.close()
            conn.close()

            logger.debug(f"PostgreSQL data: {doc_count} documents, {chunk_count} chunks, {kg_chunk_count} KG chunks, {entity_count} entities")

            # We expect at least some data in each table
            return doc_count > 0 and chunk_