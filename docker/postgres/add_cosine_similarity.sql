-- Add cosine_similarity function to PostgreSQL
-- This function is a wrapper around the pgvector cosine distance operator

-- Connect to the database
-- Already connected to the correct database from init-user-db.sh

-- Create the cosine_similarity function
CREATE OR REPLACE FUNCTION cosine_similarity(vector1 vector, vector2 numeric[])
RETURNS float AS $$
BEGIN
    -- Convert the numeric array to a vector
    RETURN 1 - (vector1 <=> vector2::vector);
END;
$$ LANGUAGE plpgsql IMMUTABLE STRICT;

-- Create an index on the entities.embedding column
CREATE INDEX IF NOT EXISTS idx_entities_embedding ON entities USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);
