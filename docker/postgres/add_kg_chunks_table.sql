-- Add kg_chunks table for Knowledge Graph optimized chunks
-- This script adds a new table for storing larger chunks optimized for knowledge graph building

-- Connect to the database
-- Already connected to the correct database from init-user-db.sh

-- Create kg_chunks table
CREATE TABLE IF NOT EXISTS kg_chunks (
    id UUID PRIMARY KEY,
    document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    chunk_index INTEGER NOT NULL,
    text TEXT NOT NULL,
    chunk_metadata JSONB,
    embedding VECTOR(:embedding_dimension),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE (document_id, chunk_index)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_kg_chunks_document_id ON kg_chunks(document_id);

-- Create vector index for embeddings
CREATE INDEX IF NOT EXISTS idx_kg_chunks_embedding ON kg_chunks USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- Update entities table to reference kg_chunks
ALTER TABLE entities ADD COLUMN kg_chunk_id UUID REFERENCES kg_chunks(id) ON DELETE SET NULL;
CREATE INDEX IF NOT EXISTS idx_entities_kg_chunk_id ON entities(kg_chunk_id);

-- Update relationships table to reference kg_chunks
ALTER TABLE relationships ADD COLUMN kg_chunk_id UUID REFERENCES kg_chunks(id) ON DELETE SET NULL;
CREATE INDEX IF NOT EXISTS idx_relationships_kg_chunk_id ON relationships(kg_chunk_id);

-- Grant all privileges to longevity user
GRANT ALL PRIVILEGES ON TABLE kg_chunks TO longevity;
