-- Database initialization script for Longevity
-- This script creates all the necessary tables for both the main application and tests

-- User and database creation is now handled by the init-user-db.sh script
-- This script assumes the user and database already exist

-- Database creation is now handled by the init-user-db.sh script

-- Extension and connection setup is handled in init-user-db.sh
-- pgvector extension is enabled there

-- Documents table
CREATE TABLE IF NOT EXISTS documents (
    id UUID PRIMARY KEY,
    filename VA<PERSON>HAR(255) NOT NULL,
    title VARCHAR(255),
    content TEXT,
    source_url VARCHAR(500),
    storage_path VARCHAR(500),
    document_type VARCHAR(100) NOT NULL,
    doc_metadata JSONB,
    content_hash VARCHAR(64),
    status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Chunks table for RAG (Retrieval Augmented Generation)
CREATE TABLE IF NOT EXISTS chunks (
    id UUID PRIMARY KEY,
    document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    chunk_index INTEGER NOT NULL,
    text TEXT NOT NULL,
    chunk_metadata JSONB,
    embedding VECTOR(:embedding_dimension),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE (document_id, chunk_index)
);

-- KG Chunks table for Knowledge Graph
CREATE TABLE IF NOT EXISTS kg_chunks (
    id UUID PRIMARY KEY,
    document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    chunk_index INTEGER NOT NULL,
    text TEXT NOT NULL,
    chunk_metadata JSONB,
    embedding VECTOR(:embedding_dimension),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE (document_id, chunk_index)
);

-- Processing tasks table
CREATE TABLE IF NOT EXISTS processing_tasks (
    id UUID PRIMARY KEY,
    document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    task_type VARCHAR(100) NOT NULL,
    celery_task_id VARCHAR(255),
    status VARCHAR(50) DEFAULT 'pending',
    result JSONB,
    error TEXT,
    task_metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_documents_status ON documents(status);
CREATE INDEX IF NOT EXISTS idx_documents_content_hash ON documents(content_hash);
CREATE INDEX IF NOT EXISTS idx_chunks_document_id ON chunks(document_id);
CREATE INDEX IF NOT EXISTS idx_kg_chunks_document_id ON kg_chunks(document_id);
CREATE INDEX IF NOT EXISTS idx_processing_tasks_document_id ON processing_tasks(document_id);
CREATE INDEX IF NOT EXISTS idx_processing_tasks_status ON processing_tasks(status);

-- Create vector indexes for embeddings
CREATE INDEX IF NOT EXISTS idx_chunks_embedding ON chunks USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);
CREATE INDEX IF NOT EXISTS idx_kg_chunks_embedding ON kg_chunks USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);

-- Entities table
CREATE TABLE IF NOT EXISTS entities (
    id UUID PRIMARY KEY,
    document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    chunk_id UUID REFERENCES chunks(id) ON DELETE SET NULL,
    kg_chunk_id UUID REFERENCES kg_chunks(id) ON DELETE SET NULL,
    text VARCHAR(500) NOT NULL,
    entity_type VARCHAR(100) NOT NULL,
    normalized_id VARCHAR(255),
    start_pos INTEGER,
    end_pos INTEGER,
    confidence FLOAT,
    entity_metadata JSONB,
    embedding VECTOR(:embedding_dimension),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Relationships table
CREATE TABLE IF NOT EXISTS relationships (
    id UUID PRIMARY KEY,
    document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    chunk_id UUID REFERENCES chunks(id) ON DELETE SET NULL,
    kg_chunk_id UUID REFERENCES kg_chunks(id) ON DELETE SET NULL,
    source_id UUID NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
    target_id UUID NOT NULL REFERENCES entities(id) ON DELETE CASCADE,
    relationship_type VARCHAR(100) NOT NULL,
    confidence FLOAT,
    evidence_text TEXT,
    relationship_metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for entities and relationships
CREATE INDEX IF NOT EXISTS idx_entities_document_id ON entities(document_id);
CREATE INDEX IF NOT EXISTS idx_entities_chunk_id ON entities(chunk_id);
CREATE INDEX IF NOT EXISTS idx_entities_kg_chunk_id ON entities(kg_chunk_id);
CREATE INDEX IF NOT EXISTS idx_entities_entity_type ON entities(entity_type);
CREATE INDEX IF NOT EXISTS idx_entities_embedding ON entities USING ivfflat (embedding vector_cosine_ops) WITH (lists = 100);
CREATE INDEX IF NOT EXISTS idx_relationships_document_id ON relationships(document_id);
CREATE INDEX IF NOT EXISTS idx_relationships_chunk_id ON relationships(chunk_id);
CREATE INDEX IF NOT EXISTS idx_relationships_kg_chunk_id ON relationships(kg_chunk_id);
CREATE INDEX IF NOT EXISTS idx_relationships_source_id ON relationships(source_id);
CREATE INDEX IF NOT EXISTS idx_relationships_target_id ON relationships(target_id);
CREATE INDEX IF NOT EXISTS idx_relationships_type ON relationships(relationship_type);

-- Grant all privileges to longevity user
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO longevity;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO longevity;
GRANT ALL PRIVILEGES ON SCHEMA public TO longevity;
