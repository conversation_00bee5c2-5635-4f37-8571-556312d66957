#!/bin/bash
set -e

echo "PostgreSQL custom initialization starting"

# Create a temporary directory for processed scripts
mkdir -p /tmp/postgres-init

# Process initialization scripts without modifying originals
echo "Processing SQL files..."
for f in /docker-entrypoint-initdb.d/*.sql; do
  filename=$(basename "$f")
  echo "Processing $filename"
  # Use cat and redirect instead of sed -i to avoid in-place modification
  # Replace both :POSTGRES_DB and :embedding_dimension with their actual values
  cat "$f" | sed "s/:POSTGRES_DB/$POSTGRES_DB/g" | sed "s/:embedding_dimension/${EMBEDDING_DIMENSION:-1024}/g" > "/tmp/postgres-init/$filename"
  chmod 755 "/tmp/postgres-init/$filename"
done

# Move bash scripts to the temp directory without modification
for f in /docker-entrypoint-initdb.d/*.sh; do
  filename=$(basename "$f")
  echo "Copying $filename"
  cp "$f" "/tmp/postgres-init/$filename"
  chmod 755 "/tmp/postgres-init/$filename"
done

# Move all processed files back to the original directory
echo "Moving processed files back to /docker-entrypoint-initdb.d/"
cp -f /tmp/postgres-init/* /docker-entrypoint-initdb.d/

# Execute the original entrypoint
echo "Starting PostgreSQL with modified init scripts..."
exec docker-entrypoint.sh "$@"