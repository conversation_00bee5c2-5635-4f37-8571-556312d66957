#!/bin/bash
set -e

echo "Starting PostgreSQL initialization script..."
echo "POSTGRES_USER: $POSTGRES_USER"
echo "POSTGRES_DB: $POSTGRES_DB"
echo "EMBEDDING_DIMENSION: ${EMBEDDING_DIMENSION:-1024}"

# Set default EMBEDDING_DIMENSION if not provided
export EMBEDDING_DIMENSION=${EMBEDDING_DIMENSION:-1024}

# Enable the pgvector extension
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" -v embedding_dimension="$EMBEDDING_DIMENSION" <<-EOSQL
    -- Enable the vector extension
    CREATE EXTENSION IF NOT EXISTS vector;
EOSQL

echo "Vector extension enabled successfully."

# The other SQL files will be executed automatically in sequence by the Docker entrypoint
# based on their alphabetical/numerical order (20-init.sql, 30-add_cosine_similarity.sql, etc.)
