FROM ankane/pgvector:latest

# Copy initialization scripts in order
COPY init-user-db.sh /docker-entrypoint-initdb.d/10-init-user-db.sh
COPY init.sql /docker-entrypoint-initdb.d/20-init.sql
COPY add_cosine_similarity.sql /docker-entrypoint-initdb.d/30-add_cosine_similarity.sql
COPY add_kg_chunks_table.sql /docker-entrypoint-initdb.d/40-add_kg_chunks_table.sql

# Copy and set up custom entrypoint wrapper for diagnostics
COPY entrypoint-wrapper.sh /usr/local/bin/
RUN chmod +x /usr/local/bin/entrypoint-wrapper.sh

# Set permissions for the initialization scripts
RUN chmod 755 /docker-entrypoint-initdb.d/10-init-user-db.sh \
    && chmod 755 /docker-entrypoint-initdb.d/20-init.sql \
    && chmod 755 /docker-entrypoint-initdb.d/30-add_cosine_similarity.sql \
    && chmod 755 /docker-entrypoint-initdb.d/40-add_kg_chunks_table.sql

# Set the entrypoint to our wrapper script
ENTRYPOINT ["entrypoint-wrapper.sh"]
CMD ["postgres"]
