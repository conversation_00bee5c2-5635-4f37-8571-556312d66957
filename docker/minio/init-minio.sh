#!/bin/bash
set -e

# Start Min<PERSON> in the background
minio server /data --console-address ":9001" &

# Wait for <PERSON><PERSON> to be ready
echo "Waiting for Min<PERSON> to be ready..."
until mc config host add myminio http://localhost:9000 "${MINIO_ROOT_USER}" "${MINIO_ROOT_PASSWORD}" --api s3v4; do
  echo "MinIO is not ready yet, waiting..."
  sleep 1
done

echo "MinIO is ready, creating bucket..."

# Set default bucket name if not provided
BUCKET_NAME=${STORAGE_BUCKET_NAME:-"longevity-documents"}

# For test environment, use test bucket name
if [ "$MINIO_ROOT_USER" = "minioadmin" ] && [ "$MINIO_ROOT_PASSWORD" = "minioadmin" ]; then
  BUCKET_NAME="test-longevity-documents"
fi

echo "Creating bucket: $BUCKET_NAME"

# Create the bucket if it doesn't exist
mc mb --ignore-existing myminio/$BUCKET_NAME

# Set the bucket policy to public
mc anonymous set download myminio/$BUCKET_NAME

echo "Bucket setup complete."

# Bring the MinIO process to the foreground
wait
