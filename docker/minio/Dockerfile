FROM alpine:latest

# Install MinIO Server and Client
RUN apk add --no-cache curl ca-certificates && \
    curl -O https://dl.min.io/server/minio/release/linux-amd64/minio && \
    chmod +x minio && \
    mv minio /usr/bin/ && \
    curl -O https://dl.min.io/client/mc/release/linux-amd64/mc && \
    chmod +x mc && \
    mv mc /usr/bin/

# Copy initialization script
COPY init-minio.sh /usr/bin/init-minio.sh
RUN chmod +x /usr/bin/init-minio.sh

# Set environment variables
ENV MINIO_ROOT_USER=minioadmin
ENV MINIO_ROOT_PASSWORD=minioadmin

# Expose MinIO ports
EXPOSE 9000 9001

# Start MinIO server and run initialization script
ENTRYPOINT ["/usr/bin/init-minio.sh"]
