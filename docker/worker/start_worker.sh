#!/bin/bash

# Create directory for beat schedule
mkdir -p /app/celerybeat

# Run NLTK fix script to ensure all required data is available
python /app/docker/worker/nltk_fix.py

# Start Celery beat in the background with specific schedule file
celery -A workers.celery_app beat --loglevel=info --schedule=/app/celerybeat/celerybeat-schedule &

# Start Celery worker in the foreground
celery -A workers.celery_app worker --loglevel=info -Q celery,knowledge_graph,document,document_processing_queue,gdrive