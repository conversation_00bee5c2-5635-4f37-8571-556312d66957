aiofiles==24.1.0
amqp==5.3.1
annotated-types==0.7.0
antlr4-python3-runtime==4.9.3
anyio==4.9.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
backoff==2.2.1
beautifulsoup4==4.13.3
billiard==4.2.1
biopython==1.83
cachetools==5.5.2
celery==5.5.1
certifi==2025.1.31
cffi==1.17.1
chardet==5.2.0
charset-normalizer==3.4.1
click==8.1.8
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
coloredlogs==15.0.1
contourpy==1.3.1
coverage==7.8.0
cryptography==44.0.2
cycler==0.12.1
dataclasses-json==0.6.7
Deprecated==1.2.18
docstring_parser==0.16
effdet==0.4.1
emoji==2.14.1
eval_type_backport==0.2.2
fastapi==0.115.12
filelock==3.18.0
filetype==1.2.0
flatbuffers==25.2.10
fonttools==4.57.0
fsspec==2025.3.2
google-ai-generativelanguage==0.6.15
google-api-core==2.24.2
google-api-python-client==2.166.0
google-auth==2.38.0
google-auth-httplib2==0.2.0
google-auth-oauthlib==1.2.1
google-cloud-aiplatform==1.88.0
google-cloud-bigquery==3.31.0
google-cloud-core==2.4.3
google-cloud-resource-manager==1.14.2
google-cloud-storage==2.19.0
google-cloud-vision==3.10.1
google-crc32c==1.7.1
google-generativeai==0.8.4
google-resumable-media==2.7.2
googleapis-common-protos==1.69.2
google-cloud-storage
grpc-google-iam-v1==0.14.2
grpcio==1.72.0rc1
grpcio-status==1.71.0
h11==0.14.0
html5lib==1.1
httpcore==1.0.7
httplib2==0.22.0
httpx==0.28.1
huggingface-hub==0.30.2
humanfriendly==10.0
idna==3.10
iniconfig==2.1.0
Jinja2==3.1.6
joblib==1.4.2
kiwisolver==1.4.8
kombu==5.5.2
langdetect==1.0.9
lxml==5.3.2
MarkupSafe==3.0.2
marshmallow==3.26.1
matplotlib==3.10.1
minio==7.2.15
mpmath==1.3.0
mypy-extensions==1.0.0
neo4j==5.28.1
nest-asyncio==1.6.0
networkx==3.4.2
nltk==3.9.1
numpy==2.2.4
oauthlib==3.2.2
olefile==0.47
omegaconf==2.3.0
onnx==1.17.0
onnxruntime==1.21.0
opencv-python==*********
packaging==24.2
pandas==2.2.3
pdf2image==1.17.0
pdfminer.six==20250327
pgvector==0.4.0
pillow-heif==0.22.0
pikepdf==9.7.0
pillow==11.1.0
pluggy==1.5.0
prompt_toolkit==3.0.50
proto-plus==1.26.1
protobuf==5.29.4
psutil==7.0.0
psycopg2-binary==2.9.10
pyasn1==0.6.1
pyasn1_modules==0.4.2
pycocotools==2.0.8
pycparser==2.22
pycryptodome==3.22.0
pydantic==2.11.3
pydantic-settings==2.8.1
pydantic_core==2.33.1
pyparsing==3.2.3
pypdf==5.4.0
pypdfium2==4.30.1
pytest==8.3.5
pytest-asyncio==0.26.0
pytest-cov==6.1.1
pytest-mock==3.14.0
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-iso639==2025.2.18
python-magic==0.4.27
python-multipart==0.0.20
python-oxmsg==0.0.2
pytz==2025.2
PyYAML==6.0.2
RapidFuzz==3.13.0
redis==5.2.1
regex==2024.11.6
reportlab==4.3.1
requests==2.32.3
requests-oauthlib==2.0.0
requests-toolbelt==1.0.0
rsa==4.9
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.15.2
sentence-transformers==4.0.2
setuptools==78.1.0
shapely==2.1.0
six==1.17.0
sniffio==1.3.1
soupsieve==2.6
SQLAlchemy==2.0.40
SQLAlchemy-Utils==0.41.2
starlette==0.46.1
sympy==1.13.1
threadpoolctl==3.6.0
timm==1.0.15
tokenizers==0.21.1
torch==2.6.0
torchvision==0.21.0
tqdm==4.67.1
transformers==4.51.1
typing-inspect==0.9.0
typing-inspection==0.4.0
typing_extensions==4.13.1
tzdata==2025.2
unstructured==0.17.2
unstructured-client==0.32.3
unstructured-inference==0.8.10
unstructured.pytesseract==0.3.15
markdown==3.8
markdown-it-py==3.0.0
mdit-py-plugins==0.4.2
mdurl==0.1.2
uritemplate==4.1.1
urllib3==2.3.0
vine==5.1.0
wcwidth==0.2.13
webencodings==0.5.1
wrapt==1.17.2
