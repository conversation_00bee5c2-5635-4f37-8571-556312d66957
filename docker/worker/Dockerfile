FROM python:3.10-slim-bookworm

WORKDIR /app

# Update all packages to get security fixes
RUN apt-get update && apt-get upgrade -y && apt-get autoremove -y

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    libpq-dev \
    # Dependencies for PDF processing
    poppler-utils \
    tesseract-ocr \
    tesseract-ocr-eng \
    ghostscript \
    libmagic1 \
    # OpenCV dependencies
    libgl1 \
    # Additional dependencies for unstructured and Markdown
    pandoc \
    python3-dev \
    libxml2-dev \
    libxslt1-dev \
    antiword \
    unrtf \
    # HEIF support dependencies
    libheif-dev \
    libde265-dev \
    dos2unix \
    && rm -rf /var/lib/apt/lists/*

# Install Python dependencies
COPY docker/worker/requirements.txt .
RUN pip install -r requirements.txt

# Clean any existing NLTK data
RUN rm -rf /root/nltk_data /usr/share/nltk_data /usr/local/share/nltk_data /usr/lib/nltk_data

# Download NLTK data required by unstructured
RUN python -c "import nltk; nltk.download('punkt'); nltk.download('punkt_tab'); nltk.download('averaged_perceptron_tagger'); nltk.download('averaged_perceptron_tagger_eng'); nltk.download('wordnet'); nltk.download('omw-1.4')" && \
    mkdir -p /root/nltk_data/tokenizers && \
    chmod -R 777 /root/nltk_data

# Install additional dependencies for unstructured
RUN pip install \
    "unstructured[all]" \
    "unstructured[md]" \
    "unstructured[docx]" \
    "unstructured[pptx]" \
    "unstructured[pdf]" \
    "unstructured[txt]" \
    "unstructured[json]" \
    "unstructured[csv]" \
    "unstructured[rtf]" \
    "unstructured[epub]" \
    "unstructured[xml]" \
    "unstructured[odt]" \
    "unstructured[msg]" \
    "unstructured[html]" \
    "unstructured[markdown]" \
    "pillow-heif"

# Install specific Markdown dependencies
RUN pip install \
    "markdown" \
    "markdown-it-py" \
    "mdit-py-plugins" \
    "mdurl"

# Copy the startup script and NLTK fix script
COPY docker/worker/start_worker.sh /app/docker/worker/start_worker.sh
COPY docker/worker/nltk_fix.py /app/docker/worker/nltk_fix.py
RUN dos2unix /app/docker/worker/start_worker.sh && \
    chmod 755 /app/docker/worker/start_worker.sh

# Copy project files
COPY . .

# Set Python path to include current directory
ENV PYTHONPATH="/app"

# Set worker environment flag
ENV WORKER_ENVIRONMENT="true"

CMD ["/app/docker/worker/start_worker.sh"]
