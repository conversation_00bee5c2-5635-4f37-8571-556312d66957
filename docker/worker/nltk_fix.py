"""
NLTK Fix Script - to be run before starting worker
"""
import os
import sys
import nltk
import logging

def setup_nltk_data():
    """Setup NLTK data directories and download required packages."""
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger("nltk_fix")
    
    # Create directories
    nltk_data_dirs = [
        "/root/nltk_data",
        "/usr/share/nltk_data",
        "/usr/local/share/nltk_data"
    ]
    
    for directory in nltk_data_dirs:
        try:
            os.makedirs(f"{directory}/tokenizers/punkt_tab", exist_ok=True)
            os.chmod(directory, 0o777)
            logger.info(f"Created or verified NLTK directory: {directory}")
        except Exception as e:
            logger.error(f"Error setting up {directory}: {e}")
    
    # Download required NLTK data
    packages = [
        "punkt", 
        "punkt_tab",
        "averaged_perceptron_tagger",
        "averaged_perceptron_tagger_eng",
        "wordnet",
        "omw-1.4"
    ]
    
    for package in packages:
        try:
            nltk.download(package)
            logger.info(f"Successfully downloaded NLTK package: {package}")
        except Exception as e:
            logger.error(f"Error downloading {package}: {e}")

if __name__ == "__main__":
    setup_nltk_data()
    sys.exit(0)
