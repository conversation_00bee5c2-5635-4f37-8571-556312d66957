import json
import os
import sys
from neo4j import GraphDatabase

# Load the sample batch response
sample_file_path = 'data/sample_vertex_ai_batch_response.json'
print(f"Loading sample batch response from: {sample_file_path}")

with open(sample_file_path, 'r') as f:
    batch_response = json.load(f)

# Connect to Neo4j
uri = "neo4j://localhost:7687"
username = "neo4j"
password = "password"

driver = GraphDatabase.driver(uri, auth=(username, password))

def create_entity(tx, entity):
    query = """
    MERGE (e:Entity {id: $id})
    ON CREATE SET e.text = $text, e.type = $type, e.description = $description
    RETURN e
    """
    result = tx.run(query, id=entity['id'], text=entity['text'], 
                   type=entity['type'], description=entity.get('description', ''))
    return result.single()

def create_relationship(tx, source_id, target_id, rel_type):
    query = """
    MATCH (source:Entity {id: $source_id})
    MATCH (target:Entity {id: $target_id})
    MERGE (source)-[r:`{}`]->(target)
    RETURN r
    """.format(rel_type)
    result = tx.run(query, source_id=source_id, target_id=target_id)
    return result.single()

def process_batch_results():
    with driver.session() as session:
        # Process each prediction result
        for prediction in batch_response:
            # Extract entities and relationships
            try:
                result = json.loads(prediction['prediction'])
                chunk_id = result.get('chunk_id', 'unknown')
                entities = result.get('entities', [])
                relationships = result.get('relationships', [])
                
                print(f"Processing chunk: {chunk_id}")
                print(f"Found {len(entities)} entities and {len(relationships)} relationships")
                
                # Create entities
                entity_ids = {}  # Map entity text to ID
                for entity in entities:
                    session.write_transaction(create_entity, entity)
                    entity_ids[entity['text']] = entity['id']
                
                # Create relationships
                for rel in relationships:
                    source_text = rel['source']
                    target_text = rel['target']
                    rel_type = rel['type']
                    
                    if source_text in entity_ids and target_text in entity_ids:
                        session.write_transaction(
                            create_relationship, 
                            entity_ids[source_text], 
                            entity_ids[target_text], 
                            rel_type
                        )
                    else:
                        print(f"Warning: Could not create relationship {source_text} -> {target_text}")
                
            except Exception as e:
                print(f"Error processing prediction: {e}")

    print("Finished processing batch results")

if __name__ == "__main__":
    process_batch_results()
    driver.close()
