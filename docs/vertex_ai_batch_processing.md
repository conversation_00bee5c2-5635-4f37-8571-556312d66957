# Vertex AI Batch Processing

This document explains how to set up and use the Vertex AI batch processing functionality for processing large numbers of document chunks efficiently.

## Prerequisites

1. **Google Cloud Project**: You need a Google Cloud project with the Vertex AI API enabled.
2. **Service Account**: A service account with the necessary permissions to use Vertex AI and Google Cloud Storage.
3. **Google Cloud Storage Bucket**: A GCS bucket for storing batch prediction inputs and outputs.
4. **Required Python Packages**:
   ```bash
   pip install google-cloud-aiplatform google-cloud-storage
   ```

## Configuration

Update the following settings in `common/config.py`:

```python
# Vertex AI Settings
GOOGLE_APPLICATION_CREDENTIALS: Optional[str] = "/path/to/your/credentials.json"
VERTEX_AI_PROJECT_ID: Optional[str] = "your-project-id"
VERTEX_AI_LOCATION: str = "us-central1"
VERTEX_AI_MODEL_NAME: str = "google/gemini-2.0-flash-001"  # For online API
VERTEX_AI_BATCH_MODEL_ID: str = "gemini-1.5-pro-001"  # For batch API

# Google Cloud Storage Settings
GCS_BUCKET_NAME: str = "your-bucket-name"
```

## Creating a GCS Bucket

If you don't have a GCS bucket yet, you can create one using the Google Cloud Console or the `gcloud` CLI:

```bash
gcloud storage buckets create gs://your-bucket-name --location=us-central1
```

Or you can use the Python client as shown in the example script:

```python
from google.cloud import storage
from google.cloud.exceptions import NotFound

def ensure_bucket_exists(bucket_name, project_id=None):
    storage_client = storage.Client(project=project_id)

    try:
        bucket = storage_client.get_bucket(bucket_name)
        print(f"Bucket {bucket_name} already exists")
    except NotFound:
        print(f"Bucket {bucket_name} does not exist. Creating...")
        bucket = storage_client.create_bucket(bucket_name)
        print(f"Bucket {bucket_name} created")

    return bucket
```

## Using the Vertex AI Clients

The system now has two separate clients for different processing needs:

1. `VertexAIClient` - For online processing with parallel API
2. `VertexAIBatchClient` - For batch processing with Vertex AI Batch Prediction API

### Using VertexAIClient for Parallel Processing

```python
from transport.vertex_ai_client import VertexAIClient

# Initialize client
client = VertexAIClient()

# Process chunks in parallel using online API
results = client.process_chunks_in_parallel(chunks)
```

### Using VertexAIBatchClient for Batch Processing

```python
from transport.vertex_ai_batch_client import VertexAIBatchClient

# Initialize client
client = VertexAIBatchClient()

# Process chunks using batch API
results = client.batch_process_chunks(chunks)
```

### Advanced Usage with VertexAIBatchClient

For more control over the batch processing workflow, you can use the following methods:

```python
from transport.vertex_ai_batch_client import VertexAIBatchClient
from google.cloud import aiplatform
import uuid

# Initialize client
client = VertexAIBatchClient()

# Create a batch prediction job
batch_job_id = f"kg-batch-{uuid.uuid4().hex[:8]}"
input_uri = client._create_batch_input_file(chunks, batch_job_id)
output_uri_prefix = f"gs://{settings.GCS_BUCKET_NAME}/batch-predictions/{batch_job_id}/output"

# Submit the job
batch_prediction_job = aiplatform.Model.batch_predict(
    model_name=settings.VERTEX_AI_BATCH_MODEL_ID,
    job_display_name=f"kg-batch-job-{batch_job_id}",
    gcs_source=[input_uri],
    instances_format="jsonl",
    gcs_destination_prefix=output_uri_prefix,
    predictions_format="jsonl",
    model_parameters={
        "temperature": 0.1,
        "maxOutputTokens": 1024,
        "topP": 0.8,
        "topK": 40
    },
    sync=False  # Run asynchronously
)

# Wait for the job to complete
client.wait_for_batch_job(batch_prediction_job)

# Get results
results = client.get_batch_results(batch_prediction_job, chunks)

# Clean up temporary files
client.cleanup_batch_input(batch_prediction_job)
```

## Example Script

See `examples/batch_processing_example.py` for a complete example of how to use the VertexAIBatchClient for batch processing.

## Troubleshooting

### Common Issues

1. **Missing Credentials**: Make sure you have set the `GOOGLE_APPLICATION_CREDENTIALS` environment variable or provided the path in the settings.
2. **Insufficient Permissions**: Ensure your service account has the necessary permissions for Vertex AI and GCS.
3. **Bucket Not Found**: Make sure the GCS bucket exists and is accessible with your credentials.
4. **Model Not Found**: Verify that the model name is correct and available in your region.

### Checking Job Status

You can check the status of batch prediction jobs in the Google Cloud Console:

1. Go to the Vertex AI section
2. Navigate to "Batch Predictions"
3. Find your job by name or ID

### Viewing Logs

To view logs for batch prediction jobs:

1. Go to the Google Cloud Console
2. Navigate to "Logging"
3. Filter for "resource.type=aiplatform.googleapis.com/BatchPredictionJob"
4. Add your job ID to the filter

## Performance Considerations

- Batch processing is more efficient for large numbers of chunks (hundreds or thousands)
- The batch API has higher throughput but also higher latency for small batches
- Consider the cost implications of using the batch API vs. the online API
