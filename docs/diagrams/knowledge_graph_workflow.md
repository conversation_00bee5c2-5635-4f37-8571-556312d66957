# Knowledge Graph Processing Workflow

This document visualizes the complete workflow and status transitions for knowledge graph processing tasks.

## Task Status Flow Diagram

```mermaid
flowchart TD
    %% Main status flow
    start([User Request]) --> A[Status: queued]
    A --> B[Status: created]
    
    %% Batch job submission path
    B --> C[Status: processing]
    C --> D{Batch Job Submitted?}
    D -->|Success| E[Status: batch_job_submitted]
    D -->|Failure| F[Status: batch_job_failed]
    
    %% Batch job processing path
    E --> G[Status: processing]
    G --> H{Batch Job Completed?}
    H -->|Running| G
    H -->|Success| I[Status: ready_to_continue]
    H -->|Failed| J[Status: failed]
    
    %% Knowledge graph building path
    I --> K[Status: batch_processing_started]
    K --> L{Processing Success?}
    L -->|Success| M[Status: batch_processing_completed]
    L -->|Error| N[Status: failed]
    M --> O[Status: completed]
    
    %% Rerun path
    F -.->|Rerun| R[New Task]
    J -.->|Rerun| R
    N -.->|Rerun| R
    R --> A
    
    %% Styling
    classDef error fill:#FFAAAA,stroke:#FF0000,stroke-width:2px
    classDef success fill:#AAFFAA,stroke:#00AA00,stroke-width:2px
    classDef processing fill:#AAAAFF,stroke:#0000FF,stroke-width:1px
    classDef waiting fill:#FFFFAA
    
    class F,J,N error
    class O success
    class C,G,K processing
    class E,I waiting
```

## Detailed Status Descriptions

### Initial Phase:
- **queued**: User initiates knowledge graph building via API
- **created**: Processing task record created in database

### Batch Job Submission Phase:
- **processing**: Document is chunked and prepared for batch processing
- **batch_job_submitted**: Successful submission to Vertex AI batch prediction service
- **batch_job_failed**: Failed to submit batch job to Vertex AI

### Batch Job Processing Phase:
- **processing**: Waiting for Vertex AI batch job to complete
- **ready_to_continue**: Batch job completed successfully on Vertex AI side

### Knowledge Graph Building Phase:
- **batch_processing_started**: System begins processing batch job results
- **batch_processing_completed**: Successfully processed results into entities and relationships
- **completed**: Knowledge graph fully updated with new entities and relationships
- **failed**: Error during results processing (may occur at any phase)

## Error Handling and Recovery

Failed jobs can be rerun through the `/api/knowledge-graph/rerun-job/{task_id}` endpoint, which creates a new task with reference to the original failed task. This creates a new processing task that starts the workflow from the beginning.

Common failure points:
1. **Batch job submission failures**: Network issues, quota limits, configuration problems
2. **Batch job processing failures**: Invalid inputs, Vertex AI internal errors
3. **Knowledge graph building failures**: Missing chunk IDs, JSON parsing problems, Neo4j database issues

## Monitoring Tasks

Use these endpoints to monitor tasks:
- `/api/knowledge-graph/status/{task_id}`: Check status of specific task
- `/api/knowledge-graph/failed-jobs`: List all failed knowledge graph jobs